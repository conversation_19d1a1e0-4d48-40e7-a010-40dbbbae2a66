﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using Shikong.Pokemon2.PCG;

namespace Admin
{
    public partial class 套装管理 : Form
    {
        public 套装管理()
        {
            InitializeComponent();
        }
        List<suits> 套装列表 = new DataProcess().GetAllSuits();
        private void 套装管理_Load(object sender, EventArgs e)
        {
            dataGridView1.DataSource = 套装列表;

        }
        private void textBox1_TextChanged(object sender, EventArgs e)
        {

        }
        private void linkLabel13_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            
            装备列表 列表 = new 装备列表();
            列表.类型 = "ID";
            列表.TEXT = textBox3;
            列表.ShowDialog();
        }

        private void button2_Click(object sender, EventArgs e)
        {
            update();
            suits 套装 = new suits();
            套装.套装名=名字.Text;
            套装.套装序号=序号.Text;
            套装.装备列表 = textBox3.Text.Split('|').ToList();
            套装.套装属性 = 套装属性;
            new DataProcess().SaveSuit(套装);
            MessageBox.Show("增加完毕!");
        }
        List<suit> 套装属性 = new List<suit>();
        private void button1_Click(object sender, EventArgs e)
        {
          
            suit s = new suit();
            s.addNump = textBox2.Text;
            s.Type = comboBox1.Text;
            套装属性.Add(s);
            dataGridView2.DataSource = null;
            dataGridView2.DataSource = 套装属性;
            label5.Text = "一共有" + textBox3.Text.Split('|').Count() + "件装备,已添加了" + 套装属性.Count() + "个属性";
        }

        private void linkLabel1_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            update();
            MessageBox.Show("应用完毕!");
        }
        public void update() {
            List<string> list = textBox3.Text.Split('|').ToList();
            foreach (string s in list)
            {
                if (s.Length == 0)
                {
                    continue;
                }
                EquipmentType 装备 = new DataProcess().GetAET(s);
                装备.suitID = 序号.Text;
                new DataProcess().AddNewEquipment(装备, true);
            }
           
        }
        private void linkLabel2_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            套装属性 = new List<suit>();
            dataGridView2.DataSource = 套装属性;
            label5.Text = "一共有" + textBox3.Text.Split('|').Count() + "件装备,已添加了" + 套装属性.Count() + "个属性";
        }

        private void textBox3_TextChanged(object sender, EventArgs e)
        {
            label5.Text = "一共有" + textBox3.Text.Split('|').Count() + "件装备,已添加了" + 套装属性.Count() + "个属性";
        }
    }
}
