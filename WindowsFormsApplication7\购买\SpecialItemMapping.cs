﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ShikongPlus.Pokemon2.PCG.购买
{
    /// <summary>
    /// 特殊道具映射配置类
    /// 定义特殊商店中商品与所需道具的映射关系
    /// 用于替代原代码中硬编码的道具ID判断逻辑
    /// </summary>
    public class SpecialItemMapping
    {
        /// <summary>
        /// 商店中的商品ID
        /// 对应原代码中的硬编码检查，如：道具序号 == "88230001"
        /// 这是玩家在商店界面看到并尝试购买的商品ID
        ///
        /// 示例值：
        /// - "88230001" (神龙宝匣)
        /// - "88230002" (神秘符文)
        /// - "88230003" (龙魂召唤)
        /// </summary>
        public string ShopItemId { get; set; }

        /// <summary>
        /// 购买该商品所需的道具ID
        /// 对应原代码中GetAP_ID()方法的参数，如："820230309"
        /// 玩家背包中必须拥有足够数量的此道具才能购买商品
        ///
        /// 示例值：
        /// - "820230309" (神龙宝藏钥匙)
        /// - "820230307" (符文召唤书)
        /// - "820230308" (龙魂召唤石)
        /// </summary>
        public string RequiredItemId { get; set; }

        /// <summary>
        /// 商品的显示名称
        /// 用于在错误消息和日志中显示友好的商品名称
        /// 便于玩家理解和运营人员管理
        /// </summary>
        public string ShopItemName { get; set; }

        /// <summary>
        /// 所需道具的显示名称
        /// 用于生成错误提示消息，如："所需道具不足！需要神龙宝藏钥匙*5"
        /// 对应原代码中硬编码的道具名称
        /// </summary>
        public string RequiredItemName { get; set; }

        /// <summary>
        /// 所属商店类型ID
        /// 指定该映射关系适用于哪个商店类型
        /// 对应原代码中的商店类型判断，如：商店类型 == 8
        ///
        /// 常用值：
        /// - 8: 特殊商店 (需要特定道具的商店)
        /// - 可扩展支持其他商店类型的特殊道具需求
        /// </summary>
        public int ShopType { get; set; }
    }
}
