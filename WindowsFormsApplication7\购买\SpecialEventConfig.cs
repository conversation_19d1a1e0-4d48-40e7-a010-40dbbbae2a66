﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ShikongPlus.Pokemon2.PCG.购买
{
    /// <summary>
    /// 特殊事件配置类
    /// 定义特殊时期或活动期间的购买逻辑控制
    /// 支持动态开关各种特殊事件，便于运营活动管理
    /// </summary>
    public class SpecialEventConfig
    {
        /// <summary>
        /// 愚人节活动开关
        /// 对应原代码中的DataProcess.yrj_检查
        /// 当启用时，在结晶商店购买会触发愚人节特殊逻辑
        ///
        /// 原代码逻辑：
        /// if (商店类型 == 6 && DataProcess.yrj_)
        /// {
        ///     发送神谕("愚人节快乐！想要购买结晶道具请输入/彩蛋 关闭后在购买");
        ///     return true;
        /// }
        /// </summary>
        public bool AprilFoolEnabled { get; set; }

        /// <summary>
        /// 自定义事件配置字典
        /// 支持添加任意自定义的特殊事件配置
        /// Key: 事件名称 (如 "ChristmasEvent", "NewYearSale" 等)
        /// Value: 事件配置数据 (可以是bool、string、数字或复杂对象)
        ///
        /// 使用示例：
        /// - "DoubleExpEvent": true (双倍经验活动)
        /// - "DiscountRate": 0.8 (折扣活动，8折)
        /// - "LimitedTimeShop": "2024-12-25" (限时商店结束日期)
        ///
        /// 这种设计允许在不修改代码的情况下添加新的活动逻辑
        /// </summary>
        public Dictionary<string, object> CustomEvents { get; set; }
    }
}
