﻿using Newtonsoft.Json;
using Shikong.Pokemon2.PCG;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace PetShikongWeb.Controllers
{
    public class HomeController : Controller
    {
        public ActionResult Index()
        {
            ViewBag.Title = "Home Page";

            return View();
        }
        [HttpPost]
        public String setMain(String str) {
            if (!str.Contains("9527OA")) {
                return "设置失败，存档格式错误。";
            }
            DataProcess.MaindatData = str;//将当前操作的存档覆盖进来，请注意，一次只能编辑一个用户，不支持多用户同时编辑。
            return "设置成功。";
        }
        [HttpGet]
        public String insertProp(String pid,int num) {
            new DataProcess().AddPlayerProp(new PropInfo()
            {
                道具位置 = "1",
                道具类型ID = pid,
                道具数量 = num.ToString()
            });
            return "添加道具成功。";
        }
        
        [HttpGet]
        public bool setPath(String path) {
            DataProcess.pf = path;
            return true;
        }
        /// <summary>
        /// 获取玩家道具
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public String getPropList() {
            String 配置 = new DataProcess().GetStr();
            配置 = 配置.Split(new string[] { "O4F89" }, StringSplitOptions.None)[1];
            string key = @"qiqiwan.2016.2017.2018.2020.2021.2022";
            //qiqiwan.2016.2017.2018.2020.2021.2022ZNQMCK
            if (!DataProcess.old)
            {
                key = key + "ZNQMCK";
            }
            配置 = SkRC4.DES.DecryptRC4(配置, key + new DataProcess().GetKey1(1));
            配置 = new CompressJson().UncompressPropJson(配置);
            配置 = ConvertJsonString(配置);
            return 配置;
        }
        /// <summary>
        /// 获取玩家道具
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public String getUserInfo()
        {
            String 配置 = new DataProcess().GetStr();
            配置 = 配置.Split(new string[] { "O4F89" }, StringSplitOptions.None)[0];
            string key = @"qiqiwan.2016.2017.2018.2020.2021.2022";
            //qiqiwan.2016.2017.2018.2020.2021.2022ZNQMCK
            if (!DataProcess.old)
            {
                key = key + "ZNQMCK";
            }
            配置 = SkRC4.DES.DecryptRC4(配置, key + new DataProcess().GetKey1(0));
            配置 = ConvertJsonString(配置);
            return 配置;
        }
        public String Main() {
            return DataProcess.MaindatData;
        }
        [HttpPost]
        public String setUserInfo(String str)
        {
            UserInfo user = JsonConvert.DeserializeObject<UserInfo>(str);
            string 存档 = JsonConvert.SerializeObject(user);
            string 存档1 = 存档;
            //   存档 = RC4.EncryptRC4wq(存档, 数据处理.new 数据处理().获取密钥(1));
            string 拼接 = new DataProcess().GetStr();
            string[] 存档组 = { "", "", "", "", "" };
            if (拼接 != null)
            {
                存档组 = 拼接.Split(new[] { "O4F89" }, StringSplitOptions.None);
            }

            if (string.IsNullOrEmpty(存档))
            {
                if (存档1.Length != 0)
                {
                    return "获取失败，JSON字符非法";
                }
            }
            存档组[0] = 存档;
            DataProcess.MaindatData = new DataProcess().JointDataFile(存档组, true);
            return "保存成功。";
        }
        [HttpPost]
        public String setPropList(String str)
        {
            List<PropInfo> lp = JsonConvert.DeserializeObject<List<PropInfo>>(str);
            String 存档 = JsonConvert.SerializeObject(lp);
            //   存档 = RC4.EncryptRC4wq(存档, new 数据处理().获取密钥(1));
            string 拼接 = new DataProcess().GetStr();
            string[] 存档组 = 拼接.Split(new[] { "O4F89" }, StringSplitOptions.None);
            存档组[1] = 存档;
            DataProcess.MaindatData = new DataProcess().JointDataFile(存档组, true);
            return "保存成功。";
        }
        private string ConvertJsonString(string str)
        {
            //格式化json字符串
            JsonSerializer serializer = new JsonSerializer();
            TextReader tr = new StringReader(str);
            JsonTextReader jtr = new JsonTextReader(tr);
            object obj = serializer.Deserialize(jtr);
            if (obj != null)
            {
                StringWriter textWriter = new StringWriter();
                JsonTextWriter jsonWriter = new JsonTextWriter(textWriter)
                {
                    Formatting = Formatting.Indented,
                    Indentation = 4,
                    IndentChar = ' '
                };
                serializer.Serialize(jsonWriter, obj);
                return textWriter.ToString();
            }
            else
            {
                return str;
            }
        }
    }
}
