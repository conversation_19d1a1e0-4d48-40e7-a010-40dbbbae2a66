﻿using System;

using System.Text;


namespace PetShikongMonitor
{
    public class SkRC4
    {
        public class CryptoBase
        {
            public enum EncoderMode
            {
                Base64Encoder,//Base64
                HexEncoder//Rc4
            };


            /// <summary>
            /// 编码转换器，用于字节码和字符串之间的转换，默认为本机编码
            /// </summary>
            internal static Encoding Encode = Encoding.Default;
            internal EncoderMode Mode = EncoderMode.Base64Encoder;
            /// <summary>
            /// 带编码模式的字符串加密
            /// </summary>
            /// <param name="data">要加密的数据</param>
            /// <param name="pass">密码</param>
            /// <param name="em">编码模式</param>
            /// <returns>加密后经过编码的字符串</returns>
            public string Encrypt(string data, string pass, EncoderMode em)
            {
                try
                {
                    if (data == null || pass == null) return null;
                    if (em == EncoderMode.Base64Encoder)
                        return Convert.ToBase64String(EncryptEx(Encode.GetBytes(data), pass));
                    return ByteToHex(EncryptEx(Encode.GetBytes(data), pass));
                }
                catch
                {
                    return null;
                }
            }
            /// <summary>
            /// 带编码模式的字符串解密
            /// </summary>
            /// <param name="data">要解密的数据</param>
            /// <param name="pass">密码</param>
            /// <param name="em">编码模式</param>
            /// <returns>明文</returns>
            public string Decrypt(string data, string pass, EncoderMode em)
            {
                try
                {
                    if (data == null || pass == null || data.Length <= 0) return null;
                    if (em == EncoderMode.Base64Encoder)
                        return Encode.GetString(DecryptEx(Convert.FromBase64String(data), pass));
                    return Encode.GetString(DecryptEx(HexToByte(data), pass));
                }
                catch
                {
                    return null;
                }
            }

            /// <summary>
            /// 加密
            /// </summary>
            /// <param name="data">要加密的数据</param>
            /// <param name="pass">密钥</param>
            /// <returns>密文</returns>
            public virtual byte[] EncryptEx(byte[] data, string pass) { return null; }
            /// <summary>
            /// 解密
            /// </summary>
            /// <param name="data">要解密的数据</param>
            /// <param name="pass">密码</param>
            /// <returns>明文</returns>
            public virtual byte[] DecryptEx(byte[] data, string pass) { return null; }
            public static byte[] HexToByte(string szHex)
            {
                // 两个十六进制代表一个字节
                int iLen = szHex.Length;
                if (iLen <= 0 || 0 != iLen % 2)
                {
                    return null;
                }
                int dwCount = iLen / 2;
                byte[] pbBuffer = new byte[dwCount];
                for (int i = 0; i < dwCount; i++)
                {
                    var tmp1 = szHex[i * 2] - (szHex[i * 2] >= (uint)'A' ? (uint)'A' - 10 : '0');
                    if (tmp1 >= 16) return null;
                    var tmp2 = szHex[i * 2 + 1] - (szHex[i * 2 + 1] >= (uint)'A' ? (uint)'A' - 10 : '0');
                    if (tmp2 >= 16) return null;
                    pbBuffer[i] = (byte)(tmp1 * 16 + tmp2);
                }
                return pbBuffer;
            }
            public static string ByteToHex(byte[] vByte)
            {
                if (vByte == null || vByte.Length < 1) return null;
                StringBuilder sb = new StringBuilder(vByte.Length * 2);
                foreach (byte t in vByte)
                {
                    uint k = (uint)t / 16;
                    sb.Append((char)(k + (k > 9 ? 'A' - 10 : '0')));
                    k = (uint)t % 16;
                    sb.Append((char)(k + (k > 9 ? 'A' - 10 : '0')));
                }
                return sb.ToString();
            }
        }


        public class RC4Crypto : CryptoBase
        {
            //internal static RC4Crypto RC4 = new RC4Crypto();
            public override byte[] EncryptEx(byte[] data, string pass)
            {
                //Byte[] data =Encoding.Unicode.GetBytes(text);
                if (data == null || pass == null) return null;
                byte[] output = new byte[data.Length];
                long i = 0;
                long j = 0;
                byte[] mBox = GetKey(Encode.GetBytes(pass), 256);
                // 加密
                for (long offset = 0; offset < data.Length; offset++)
                {
                    i = (i + 1) % mBox.Length;
                    j = (j + mBox[i]) % mBox.Length;
                    byte temp = mBox[i];
                    mBox[i] = mBox[j];
                    mBox[j] = temp;
                    byte a = data[offset];
                    //Byte b = mBox[(mBox[i] + mBox[j] % mBox.Length) % mBox.Length];
                    // mBox[j] 一定比 mBox.Length 小，不需要在取模
                    byte b = mBox[(mBox[i] + mBox[j]) % mBox.Length];
                    output[offset] = (byte)(a ^ b);
                }

                return output;
            }
            public override byte[] DecryptEx(byte[] data, string pass)
            {
                return EncryptEx(data, pass);
            }
            /// <summary>
            /// 打乱密码
            /// </summary>
            /// <param name="pass">密码</param>
            /// <param name="kLen">密码箱长度</param>
            /// <returns>打乱后的密码</returns>
            private static byte[] GetKey(byte[] pass, int kLen)
            {
                byte[] mBox = new byte[kLen];
                for (long i = 0; i < kLen; i++)
                {
                    mBox[i] = (byte)i;
                }
                long j = 0;
                for (long i = 0; i < kLen; i++)
                {
                    j = (j + mBox[i] + pass[i % pass.Length]) % kLen;
                    byte temp = mBox[i];
                    mBox[i] = mBox[j];
                    mBox[j] = temp;
                }
                return mBox;
            }
        }

        public static class DES
        {
            public static string EncryptRC4(string str, string ckey)
            {
                RC4Crypto rc4 = new RC4Crypto();
                string s = rc4.Encrypt(str, ckey, CryptoBase.EncoderMode.HexEncoder);
                return s;
            }

        
            public static string DecryptRC4(string str, string ckey)
            {
                try
                {
                    if (str.IndexOf("O19A87", StringComparison.Ordinal) != -1)
                    {
                        str = str.Split(new[] { "O19A87" }, StringSplitOptions.None)[0];
                    }
                    RC4Crypto rc4 = new RC4Crypto();
                    string s = rc4.Decrypt(str, ckey, CryptoBase.EncoderMode.HexEncoder);
                    return s;
                }
                catch (Exception)
                {
                    return null;
                }
            }
        }
    }
}
