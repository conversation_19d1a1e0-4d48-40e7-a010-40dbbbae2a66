﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using System.IO;
using System.Security.Cryptography;
using PetShikongTools;
using Newtonsoft.Json;
using System.Linq;
using Shikong.Pokemon2.PCG;

namespace Admin
{
    public partial class 管理 : Form
    {
        public 管理()
        {
            InitializeComponent();
        }
        string 文件名 = "";
        int i = -1;
        private void comboBox1_SelectedIndexChanged(object sender, EventArgs e)
        {
            
            /***
             宠物配置
             怪物配置
             道具配置
             人物存档
             宠物存档
             道具存档
             **/
            if (comboBox1.Text.Equals("宠物配置")) {
              
                文件名 = DataProcess.PDC_Path;
                i = -1;
            }
            else if (comboBox1.Text.Equals("怪物配置"))
            {

                文件名 = DataProcess.MTDC_Path;
                i = -1;
            }
            else if (comboBox1.Text.Equals("道具配置"))
            {

                文件名 = DataProcess.PPDC_Path;
                i = -1;
            }
            else if (comboBox1.Text.Equals("人物存档"))
            {

                文件名 = DataProcess.PF_Path ;
                i = 0;
            }

            else if (comboBox1.Text.Equals("道具存档"))
            {
                i = 1;
                文件名 = DataProcess.PF_Path;
            }


            else if (comboBox1.Text.Equals("宠物存档"))
            {

                文件名 = DataProcess.PF_Path;
                i = 2;
            }
            else if (comboBox1.Text.Equals("装备存档"))
            {

                文件名 = DataProcess.PF_Path;
                i = 4;
            }
            else if (comboBox1.Text.Equals("副本进度存档"))
            {

                文件名 = DataProcess.PF_Path;
                i = 5;
            }
            else if (comboBox1.Text.Equals("任务存档"))
            {

                文件名 = DataProcess.PF_Path;
                i = 6;
            }
            
            else if (comboBox1.Text.Equals("地图定义存档"))
            {
                i = -1;
                文件名 = DataProcess.MDC_Path + "_" + 地图ID.Text + "map_.data";
            }
            else if (comboBox1.Text.Equals("地图怪物定义存档"))
            {
                i = -1;
                文件名 =DataProcess.MDC_Path + "_"+地图ID.Text + "pet_.data";
            }
            else if (comboBox1.Text.Equals("技能配置"))
            {
                i = -1;
                文件名 = DataProcess.SDC_Path;
            }
            文件.Text = 文件名;
            string 配置 = "";
            if (文件名 == DataProcess.PF_Path)
            {
                配置 = new DataProcess().GetStr();
            }
            else {
                配置 = new DataProcess().ReadFile(文件名);
            }
            if (i != -1) {
                配置 = 配置.Split(new string[] { "O4F89" }, StringSplitOptions.None)[i];
                string key = @"qiqiwan.2016.2017.2018.2020.2021.2022";
                             //qiqiwan.2016.2017.2018.2020.2021.2022ZNQMCK
                if (!DataProcess.old) {
                    key = key + "ZNQMCK";
                }
                配置 = SkRC4.DES.DecryptRC4(配置, key+ new DataProcess().GetKey1(i));
            }
            else
            {
                配置 = SkRC4.DES.DecryptRC4(配置, @"qiqiwan.2016.2017.2018.2020.2021.2022");

            }

            if (i == 1)
            {
                配置 = new CompressJson().UncompressPropJson(配置);
            }
            
            if (i == 2)
            {
                配置 = new CompressJson().UncompressPetJson(配置);
            }
            textBox1.Text = 配置;
            
        }

        private void Form1_Load(object sender, EventArgs e)
        {
            DataProcess.old = false;
            FORM.Main = this;
           // button6.Location = new Point(0, 0);
        }

        private void button1_Click(object sender, EventArgs e)
        {
            if (i != -1)
            {
                string 总配置 = new DataProcess().GetStr();
                string[] 配置 = 总配置.Split(new string[] { "O4F89" }, StringSplitOptions.None);
                配置[i] = SkRC4.DES.EncryptRC4(textBox1.Text, @"qiqiwan.2016.2017.2018.2020.2021.2022ZNQMCK");
                string 配置文本 = new DataProcess().JointDataFile(配置,true);
                
                new DataProcess().SaveFile(配置文本, DataProcess.PF_Path);
            }
            else
            {
                string 配置 = textBox1.Text;
                配置 = SkRC4.DES.EncryptRC4(配置, @"qiqiwan.2016.2017.2018.2020.2021.2022");
                new DataProcess().SaveFile(配置, 文件.Text);
            }
        }
       
        private void tabControl1_TabIndexChanged(object sender, EventArgs e)
        {
              
        }

        private void tabControl1_Selected(object sender, TabControlEventArgs e)
        {
          //  comboBox1.Text = e.TabPage.Text;
        }

        private void 道具配置_Click(object sender, EventArgs e)
        {
         
        }

        private void button2_Click(object sender, EventArgs e)
        {
            if (new DataProcess().AddNewProp(道具名字.Text, 道具图标.Text, 道具序号.Text, 道具脚本.Text, 道具说明.Text,出售价格.Text))
            {
                MessageBox.Show("添加成功!");
            }
            else
            {
                MessageBox.Show("添加失败,道具序号已经存在!");
            }
        }

        private void linkLabel1_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            道具列表 列表 = new 道具列表();
            列表.TEXT = 道具脚本;
            列表.Show();
        }

        private void linkLabel2_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            道具列表 列表 = new 道具列表();
            列表.类型 = "道具名字";
            列表.TEXT = 道具说明;
            列表.Show();
        }

        private void button3_Click(object sender, EventArgs e)
        {
            if(!new DataProcess().ExistOrNot_PropType(道具序号2.Text)){
                MessageBox.Show("该道具不存在噢!");
                return;
            }
            PropInfo 信息 = new PropInfo();
            信息.道具类型ID = 道具序号2.Text;
            信息.道具位置 = PropLoaction.背包.ToString();
            信息.道具数量 = 添加道具数量.Text;
            if (new DataProcess().AddPlayerProp(信息))
            {
                MessageBox.Show("添加成功!");
            }
            else
            {
                MessageBox.Show("玩家背包格子不够!");
            }
        }

        private void linkLabel3_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            道具列表 列表 = new 道具列表();
            列表.类型 = "道具序号";
            列表.TEXT = 道具序号2;
            列表.Show();
        }

        private void linkLabel4_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            宠物列表 列表 = new 宠物列表();
            列表.类型 = "宠物序号";
            列表.TEXT = 宠物序号;
            列表.ShowDialog();
            pictureBox1.ImageLocation = Environment.CurrentDirectory.ToString() + "\\PageMain\\Content\\PetPhoto\\z" + 宠物序号.Text + ".gif";
        }

        private void button4_Click(object sender, EventArgs e)
        {
            try
            {
                PetInfo 宠物 = new PetInfo();
                PetConfig 类型 = new DataProcess().GetAppointedPetType(宠物序号.Text);
                宠物.宠物名字 = 类型.宠物名字;
                宠物.形象 = 类型.宠物序号;
                宠物.五行 = 类型.系别;
                宠物.当前经验 = "1";
                宠物.宠物序号 = "1";
                宠物 = new DataProcess().SetDefaultAttribute(宠物);
                宠物.成长 = 宠物成长.Text;
                new DataProcess().AddPet(宠物);
            }
            catch (Exception ex) {
                MessageBox.Show(ex.Message);
            }
        }

        private void 宠物序号_Leave(object sender, EventArgs e)
        {
            pictureBox1.ImageLocation = Environment.CurrentDirectory.ToString() + "\\PageMain\\Content\\PetPhoto\\z" + 宠物序号.Text + ".gif";
        }

        private void tabControl1_SelectedIndexChanged(object sender, EventArgs e)
        {

        }

        private void tabPage2_Click(object sender, EventArgs e)
        {

        }

        private void linkLabel5_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            
            道具列表 列表 = new 道具列表();
            列表.TEXT = 地图掉落列表;
            列表.ShowDialog();
        }

        private void linkLabel8_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            FORM.怪物信息的列表 = 怪物信息列表;
            怪物加入列表 列表 = new 怪物加入列表();
            列表.ShowDialog();
        }
        
        private void button5_Click(object sender, EventArgs e)
        {
            if (new DataProcess().ReadMapInfo(地图序号.Text,true) != null)
            {
                MessageBox.Show("该地图序号已经存在!");
            }
            else {

                MapInfo 地图 = new MapInfo();
                地图.地图ID = 地图序号.Text;
                地图.掉落道具 = 地图掉落列表.Text;
                地图.最大掉落 = 地图掉落最大数量.Text;
                地图.最小掉落 = 地图掉落最小数量.Text;
                地图.最小金币 = 地图最小金币.Text;
                地图.最大金币 = 地图最大金币.Text;
                地图.最大元宝 = 地图最大元宝.Text;
                地图.最小元宝 = 地图最小元宝.Text;

                地图.ICO = textBox5.Text;
                if (checkBox2.Checked)
                {
                    地图.Type = "1";
                }
                string 配置 = new ConvertJson().EntityToJson(地图);
                配置 = SkRC4.DES.EncryptRC4(配置, @"qiqiwan.2016.2017.2018.2020.2021.2022");
                new DataProcess().SaveFile(配置,DataProcess.MDC_Path + "_" + 地图.地图ID + "map_.data");
                MessageBox.Show("保存成功!");
            }

        }

        private void linkLabel6_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            怪物列表 列表 = new 怪物列表();
            列表.类型 = "怪物序号";
            列表.TEXT = 怪物序号 ;
            列表.ShowDialog();
        }

        private void button7_Click(object sender, EventArgs e)
        {
            FORM.输入内容 = null;
            输入框 输入 = new 输入框();
            输入.ShowDialog();
            if (怪物信息列表 == null || 怪物信息列表.Count == 0)
            {
                MessageBox.Show("请至少添加一个宠物后再保存!");
                return;
            }
            if (string.IsNullOrEmpty(FORM.输入内容))
            {
                MessageBox.Show("请输入地图编号!否则无法继续!");

            }
            else {
                
                if (new DataProcess().GetAMML(FORM.输入内容, true) == null)
                {
                    string 配置 = new ConvertJson().ListToJson(怪物信息列表);
                    配置 = SkRC4.DES.EncryptRC4(配置, @"qiqiwan.2016.2017.2018.2020.2021.2022");
                    new DataProcess().SaveFile(配置, DataProcess.MDC_Path + "_" + FORM.输入内容 + "pet_.data");
                    MessageBox.Show("保存成功!");
                }
                else {
                    MessageBox.Show("该地图已经存在怪物配置!");
                }
            }
        }
        bool 修改 = false;
        int index = -1;
        public List<MonsterInfo> 怪物信息列表 = new List<MonsterInfo>();
        private void linkLabel7_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            
             if (new DataProcess().GetMonsterName(怪物序号.Text) == "Null")
             {
                 MessageBox.Show("您填写的怪物序号不存在,请检查!\r\n小提示:最好点击旁边的选择链接来选择序号!");
                 return;
             }
             if (怪物成长.Text.Length == 0) {
                 MessageBox.Show("成长是必填的噢!");
                 return;
             }
             if (怪物序号.Text.Length == 0)
             {
                 MessageBox.Show("序号是必填的噢!");
                 return;
             }
             if (怪物最大等级.Text.Length == 0)
             {
                 MessageBox.Show("最大等级是必填的噢!");
                 return;
             }
             if (怪物最小等级.Text.Length == 0)
             {
                 MessageBox.Show("最小等级是必填的噢!");
                 return;
             }
             if (Convert.ToInt32(怪物最小等级.Text) > Convert.ToInt32(怪物最大等级.Text))
             {
                 MessageBox.Show("最小等级不能比最大等级大!");
                 return;
             }
             if (怪物经验.Text.Length == 0) {
                 怪物经验.Text = "0";
             
             }
             if (怪物掉落.Text.Length == 0)
             {
                 怪物掉落.Text = "无";

             }
            MonsterInfo 信息 = new MonsterInfo();
            信息.掉落道具 = 怪物掉落.Text;
            信息.怪物成长 = 怪物成长.Text;
            信息.最大等级 = 怪物最大等级.Text;
            信息.最小等级 = 怪物最小等级.Text;
            信息.最大掉落 = 怪物最大掉落.Text;
            信息.序号 = 怪物序号.Text;
            信息.经验值 = 怪物经验.Text;
            怪物序号.Text = "";
            if (!修改)
            {
                foreach (MonsterInfo 怪物 in 怪物信息列表)
                {
                    if (怪物.序号.Equals(怪物序号.Text))
                    {
                        MessageBox.Show("这只怪物已经存在于列表中,同一个地图不能出现两个相同的怪物!");
                        return;
                    }
                }
                怪物信息列表.Add(信息);
                MessageBox.Show("已经加入到列表,请继续编辑!");
            }
            else
            {
                修改 = false;
                怪物信息列表[index] = 信息;
                linkLabel7.Text = "加入列表";
                MessageBox.Show("已经修改到列表,请继续编辑!");

            }
        }
        public void ediInfo(int i) {
            怪物掉落.Text = 怪物信息列表[i].掉落道具;
            怪物成长.Text = 怪物信息列表[i].怪物成长;
            怪物最大等级.Text = 怪物信息列表[i].最大等级;
            怪物最小等级.Text = 怪物信息列表[i].最小等级;
            怪物最大掉落.Text = 怪物信息列表[i].最大掉落;
            怪物序号.Text = 怪物信息列表[i].序号;
            怪物经验.Text = 怪物信息列表[i].经验值;
            index = i;
            修改 = true;
            linkLabel7.Text = "保存到列表";

        }
        private void button6_Click(object sender, EventArgs e)
        {
            怪物信息列表 = new List<MonsterInfo>();
            button6.Visible = false;
            修改 = false;
            linkLabel7.Text = "加入列表";
        }

        private void button8_Click(object sender, EventArgs e)
        {
            button6.Visible = true;
        }

        private void tabPage3_Click(object sender, EventArgs e)
        {

        }

        private void linkLabel9_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            道具列表 列表 = new 道具列表();
            列表.TEXT = 怪物掉落;
            列表.ShowDialog();
        }

        private void 怪物序号_TextChanged(object sender, EventArgs e)
        {

        }

        private void linkLabel10_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            宠物列表 列表 = new 宠物列表();
            列表.类型 = "宠物序号";
            列表.TEXT = 道具脚本;
            列表.ShowDialog();
        }

        private void linkLabel11_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            宠物列表 列表 = new 宠物列表();
            列表.类型 = "宠物名字";
            列表.TEXT = 道具说明;
            列表.ShowDialog();
        }
        string[] 存档数组;
        private void textBox1_DragDrop(object sender, DragEventArgs e)
        {
            i = -1;
            
            var filenames = (string[])e.Data.GetData(DataFormats.FileDrop);

            using (StreamReader sr = new StreamReader(filenames[0], Encoding.Default))
            {
                文件名 = filenames[0];
                //textBox1.Text = 文件名;
                //根据文件名判断是否为存档文件，若为配置文件则应更改i
                FileInfo fi = new FileInfo(文件名);
                文件.Text = 文件名;
                Text = fi.Name;
                if (文件名.Contains(@"\Main.dat"))
                {
                    DataProcess.PF_Path = 文件名;
                    textBox1.Text = "已切换到目录："+文件名;
                    this.Text = 文件名;
                    return;
                    textBox1.Text = "存档快速写入到目录完毕：\r\n";
                    String 配置 = sr.ReadToEnd();
                    File.WriteAllText("pagemain/Main.dat",配置 );
                   
                    String[] 配置数组 = 配置.Split(new string[] { "O4F89" }, StringSplitOptions.None);
                    配置 = 配置数组[0];
                    配置 = SkRC4.DES.DecryptRC4(配置, @"qiqiwan.2016.2017.2018.2020.2021.2022" + new DataProcess().GetKey1(0));
                    存档数组 = 配置数组;
                    UserInfo 用户存档 = new UserInfo();
                    if (!string.IsNullOrEmpty(配置))
                    {
                        用户存档 = JsonConvert.DeserializeObject<UserInfo>(配置);
                    }

                    textBox1.AppendText("\r\n\r\n*****用户基础信息*****");

                    textBox1.AppendText("\r\n论坛账号：" + 用户存档.论坛ID);
                    textBox1.AppendText("\r\n元宝数值：" + 用户存档.元宝);
                    textBox1.AppendText("\r\n水晶数值：" + 用户存档.水晶);
                    textBox1.AppendText("\r\n宠物数量：" + 用户存档.宠物数量);

                    配置 = 配置数组[2];
                    配置 = SkRC4.DES.DecryptRC4(配置, @"qiqiwan.2016.2017.2018.2020.2021.2022" + new DataProcess().GetKey1(2));
                    List<PetInfo> 宠物存档 = new List<PetInfo>();
                    if (!string.IsNullOrEmpty(配置))
                    {
                        宠物存档 = JsonConvert.DeserializeObject<List<PetInfo>>(配置);
                    }
                    PetInfo maxCCPet = 宠物存档.OrderByDescending(宠物信息 => Convert.ToDouble(宠物信息.成长)).FirstOrDefault();
                    if (maxCCPet != null) {
                        textBox1.AppendText("\r\n\r\n*****最大成长的宠物*****");


                       textBox1.AppendText("\r\n宠物名：" + maxCCPet.宠物名字);
                       textBox1.AppendText("\r\n成长值：" + maxCCPet.成长);
                        
                    }
                    配置 = 配置数组[1];
                    配置 = SkRC4.DES.DecryptRC4(配置, @"qiqiwan.2016.2017.2018.2020.2021.2022" + new DataProcess().GetKey1(1));
                    List<PropInfo> 道具存档 = new List<PropInfo>();
                    if (!string.IsNullOrEmpty(配置))
                    {
                        配置 = new CompressJson().UncompressPropJson(配置);
                        道具存档 = JsonConvert.DeserializeObject<List<PropInfo>>(配置);
                    }
                    if (道具存档 != null && 道具存档.Count > 0) {
                        var propInfo = 道具存档.FirstOrDefault(C => C.道具类型ID  == "20");
                        if (propInfo != null) {
                            textBox1.AppendText("\r\n\r\n*****道具信息*****");
                            textBox1.AppendText("\r\n涅槃卵：" + propInfo.道具数量);
                        }

                    }
                }
                else {
                    textBox1.Text = SkRC4.DES.DecryptRC4(sr.ReadToEnd(), @"qiqiwan.2016.2017.2018.2020.2021.2022");
                }


            }
        }

        private void textBox1_DragEnter(object sender, DragEventArgs e)
        {

            i = -1;
            if (e.Data.GetDataPresent(DataFormats.FileDrop, false) == true)
            {

                var filenames = (string[])e.Data.GetData(DataFormats.FileDrop);

                var hz = filenames[0].LastIndexOf('.') + 1;

                var houzhui = filenames[0].Substring(hz);//文件后缀名 
                e.Effect = DragDropEffects.All;    
       
            }
        }

        private void button9_Click(object sender, EventArgs e)
        {
            textBox1.Text = SkRC4.DES.EncryptRC4(textBox1.Text, @"qiqiwan.2016.2017.2018.2020.2021.2022");

        }

        private void button10_Click(object sender, EventArgs e)
        {
            string json= "";
            json = 取哈希(@"\PageMain\propTable\", "data");
            json += "\r\n" + 取哈希(@"\PageMain\propTable\e\", "dat");
            json += "\r\n" + 取哈希(@"\PageMain\propTable\e\s\", "dat");
            json += "\r\n" + 取哈希(@"\PageMain\propTable\e\s\", "petshikong");
            json += "\r\n" + 取哈希(@"\PageMain\Malls\", "mall");
            json += "\r\n" + 取哈希(@"\PageMain\map\", "data");;
            json += "\r\n" + 取哈希(@"\PageMain\task\", "dat");
            json += "\r\n" + 取哈希(@"\PageMain\task\", "task");
            json += "\r\n" + 取哈希(@"\PageMain\", "html");
            json += "\r\n" + 取哈希(@"\PageMain\", "qingshan");
            json += "\r\n" + 取哈希(@"\PageMain\", "canku");
            json += "\r\n" + 取哈希(@"\PageMain\", "mask");
            json += "\r\n" + 取哈希(@"\PageMain\", "wad");
            textBox2.Text = json;
            new DataProcess().SaveFile(json,程序路径+ @"\PageMain\Content\resources\styles\images\4ie\Shikong.Pokemon2.PCG.cfg");


        }
        
        public static string 程序路径 = Environment.CurrentDirectory;
        internal static string GetFileHash(string filePath)
        {
            if (!File.Exists(filePath))
            {
                return "-1";
            }
            using (FileStream fs = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.Read))
            {
                return BitConverter.ToString(new MD5CryptoServiceProvider().ComputeHash(fs)).Replace("-", "");
            }
        }


        private string 取哈希(string 路径, string 类型)
        {
            DirectoryInfo dir = new DirectoryInfo(Environment.CurrentDirectory + 路径);
            //path为某个目录，如： “D:\Program Files”
            FileInfo[] inf = dir.GetFiles();
            var 文件 = new List<SkFileCheck.FileCheck>();

            foreach (FileInfo finf in inf)
            {
                if (finf.Extension.Equals("." + 类型))
                {

                    var 验证 = new SkFileCheck.FileCheck
                    {
                        特征 = GetFileHash(finf.FullName),
                        文件名 = 路径 + finf.Name
                    };
                    文件.Add(验证);
                }
                //如果扩展名为“.xml”

                //读取文件的完整目录和文件名
            }
            String json = "|";
            foreach (SkFileCheck.FileCheck 文 in 文件)
            {
                if (!文.文件名.Contains("test"))
                {
                    json += "\r\n" + 文.文件名 + "|" + 文.特征;
                }
            }
            json = json.Replace("|\r\n", "");
            return json;

        }

        private void button12_Click(object sender, EventArgs e)
        {
            EquipmentType 装备 = new EquipmentType();
            装备.ID = 装备_ID.Text;
            装备.抵消 = 装备抵消.Text;
            装备.防御 = 装备防御.Text;
            装备.攻击 = 装备攻击.Text;
            装备.加深 = 装备加深.Text;
            装备.类型 = 装备类型.Text;
            装备.名字 = 装备名字.Text;
            装备.魔法 = 装备魔法.Text;
            装备.闪避 = 装备闪避.Text;
            装备.生命 = 装备生命.Text;
            装备.速度 = 装备速度.Text;
            装备.吸魔 = 装备吸魔.Text;
            装备.吸血 = 装备吸血.Text;
            装备.说明 = textBox4.Text;
            装备.ICO = 装备图标.Text;
            装备.命中 = 装备命中.Text;
            装备.主属性 = 主属性.Text;
            装备.SpecialAffect = 装备特效.Text;
            if (new DataProcess().AddNewEquipment(装备))
            {
                MessageBox.Show("增加装备成功!");
            }
            else {
                MessageBox.Show("增加装备失败,序号已存在!");
            }

        }

        private void linkLabel12_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            装备列表 列表 = new 装备列表();
            列表.类型 = "ID";
            列表.TEXT = 存档装备ID;
            列表.edi = textBox1;
            列表.文件 = 文件;
            列表.Show();
        }

        private void tabPage6_Click(object sender, EventArgs e)
        {

        }

        private void button13_Click(object sender, EventArgs e)
        {
            EquipmentInfo 装备 = new EquipmentInfo();
            装备.ID = "0";
            装备.类ID = 存档装备ID.Text;
            装备.强化 = "0";
            if (new DataProcess().AddPlayerEquipment(装备))
            {
                MessageBox.Show("增加装备成功!");
            }
            else {
                MessageBox.Show("增加装备失败 ,只能拥有120个装备!");
            }
        }

        private void linkLabel13_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            装备列表 列表 = new 装备列表();
            列表.类型 = "ID";
            列表.TEXT = 道具脚本;
            列表.ShowDialog();
        }

        private void linkLabel14_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            装备列表 列表 = new 装备列表();
            列表.类型 = "装备名字";
            列表.TEXT = 道具说明;
            列表.ShowDialog();
        }

        private void button14_Click(object sender, EventArgs e)
        {
            宠物进化路线 窗口 = new 宠物进化路线();
            窗口.Show();
        }

        private void 宠物成长_TextChanged(object sender, EventArgs e)
        {

        }

        private void button15_Click(object sender, EventArgs e)
        {
            任务管理 窗口 = new 任务管理();
            窗口.Show();
        }

        private void textBox1_TextChanged(object sender, EventArgs e)
        {

        }

        private void button16_Click(object sender, EventArgs e)
        {
            套装管理 窗口 = new 套装管理();
            窗口.Show();
        }

        private void button17_Click(object sender, EventArgs e)
        {
            List<string> 信息 = new DataProcess().GetMapList();
            foreach (string m in 信息)
            {
                string html = new DataProcess().ReadFile(Environment.CurrentDirectory + @"\PageMain\MapInfo\mapInfo.html", true);

                MapInfo 地图 = new DataProcess().ReadMapInfo(m,true);
                if (地图 == null) {
                    continue;
                }
                html = html.Replace("{最小元宝}", 地图.最小元宝);

                html = html.Replace("{最大元宝}", 地图.最大元宝);
                html = html.Replace("{地图序号}", 地图.地图ID);
                List<MonsterInfo> 怪物 = new DataProcess().GetAMML(m, true);
                string 怪物列表 = "";
                foreach (MonsterInfo g in 怪物)
                {
                    怪物列表 += "<span>" + g.怪物名字 + "</span>、";
                }
                怪物列表 += "|";
                怪物列表 = 怪物列表.Replace("、|", "");
                html = html.Replace("{怪物列表}", 怪物列表);
                new DataProcess().SaveFile(html, @"PageMain\MapInfo\t" + m + ".html");
            }
        }

        private void button11_Click(object sender, EventArgs e)
        {

        }

        private void button18_Click(object sender, EventArgs e)
        {
            DataProcess.old = true;
            new DataProcess().saveDat();
            DataProcess.old = false;
            checkBox1.Checked = false;
        }

        private void checkBox1_CheckedChanged(object sender, EventArgs e)
        {
            DataProcess.old = checkBox1.Checked;
        }

        private void button11_Click_1(object sender, EventArgs e)
        {
            var propList = JsonConvert.DeserializeObject<List<PropInfo1>>(textBox1.Text);
            textBox1.Text = "";
            foreach (var p in propList) {
                bool t = new DataProcess().AddNewProp(p.道具名字, p.道具图标,
                    p.道具序号, p.道具脚本, p.道具说明, p.道具价格, true);
                textBox1.AppendText("\r\n成功添加道具 " + p.道具名字);
                
               
            }
            
        }
    }
}
