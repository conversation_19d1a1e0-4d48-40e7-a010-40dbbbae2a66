using Newtonsoft.Json;
using Shikong.Pokemon2.PCG;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ShikongPlus.Pokemon2.PCG.购买
{
    /// <summary>
    /// 购买配置管理器
    /// 负责从服务器加载、缓存和管理购买配置信息
    /// 使用单例模式确保配置的一致性和性能
    /// 参考GetWeb函数的在线配置加载模式
    /// </summary>
    public static class BuyConfigManager
    {
        #region 私有字段

        /// <summary>
        /// 配置实例缓存
        /// 避免重复加载，提高性能
        /// </summary>
        private static BuyConfig _config;

        /// <summary>
        /// 线程安全锁对象
        /// 确保多线程环境下配置加载的安全性
        /// </summary>
        private static readonly object _lock = new object();

        #endregion

        #region 公共属性

        /// <summary>
        /// 获取购买配置实例
        /// 使用懒加载模式，首次访问时自动加载配置
        /// 线程安全的双重检查锁定模式
        /// </summary>
        public static BuyConfig Config
        {
            get
            {
                // 第一次检查，避免不必要的锁定
                if (_config == null)
                {
                    lock (_lock)
                    {
                        // 第二次检查，确保线程安全
                        if (_config == null)
                        {
                            LoadConfig();
                        }
                    }
                }
                return _config;
            }
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 从服务器加载购买配置
        /// 使用与其他在线配置相同的加载机制
        /// 支持加密传输和本地解密
        /// </summary>
        public static void LoadConfig()
        {
            try
            {
                // 从服务器获取加密的配置JSON
                // 使用与其他配置相同的URL模式和端口
                string configJson = new ConvertJson().GetWeb($"{DataProcess.SK_getUrl()}:9696/sk/buyConfig.json");

                if (!string.IsNullOrEmpty(configJson))
                {
                    // 使用游戏标准加密密钥解密配置
                    string decryptedConfig = SkRC4.DES.DecryptRC4(configJson, new DataProcess().GetKey(1));

                    // 反序列化为配置对象
                    _config = JsonConvert.DeserializeObject<BuyConfig>(decryptedConfig);
                }
                else
                {
                    // 网络获取失败时使用默认配置
                    _config = GetDefaultConfig();
                }
            }
            catch (Exception ex)
            {
                // 记录错误日志，使用游戏标准日志系统
                LogSystem.JoinLog(LogSystem.EventKind.加入日志, $"加载购买配置失败: {ex.Message}");

                // 异常时使用默认配置，确保游戏正常运行
                _config = GetDefaultConfig();
            }
        }

        /// <summary>
        /// 获取默认配置
        /// 当服务器配置加载失败时使用的后备配置
        /// 包含所有必要的默认值，确保游戏功能正常
        /// </summary>
        /// <returns>默认的购买配置实例</returns>
        private static BuyConfig GetDefaultConfig()
        {
            return new BuyConfig
            {
                // 商店类型默认配置
                ShopTypes = new ShopTypeConfig
                {
                    CrystalShopType = 6,    // 结晶商店类型ID
                    SpecialShopType = 8,    // 特殊商店类型ID
                    ShopTypeNames = new Dictionary<int, string>
                    {
                        {1, "元宝商店"},      // 元宝购买商店
                        {2, "水晶商店"},      // 水晶购买商店
                        {3, "金币商店"},      // 金币购买商店
                        {4, "积分商店"},      // 积分购买商店
                        {5, "威望商店"},      // 威望购买商店
                        {6, "结晶商店"},      // 结晶购买商店
                        {8, "特殊商店"}       // 特殊道具商店
                    }
                },

                // 特殊道具映射默认配置
                SpecialItems = new List<SpecialItemMapping>
                {
                    // 神龙宝匣：需要神龙宝藏钥匙
                    new SpecialItemMapping
                    {
                        ShopItemId = "88230001",        // 商店中的商品ID
                        RequiredItemId = "820230309",   // 购买所需的道具ID
                        ShopItemName = "神龙宝匣",       // 商品显示名称
                        RequiredItemName = "神龙宝藏钥匙", // 所需道具显示名称
                        ShopType = 8                    // 所属商店类型
                    },

                    // 神秘符文：需要符文召唤书
                    new SpecialItemMapping
                    {
                        ShopItemId = "88230002",        // 商店中的商品ID
                        RequiredItemId = "820230307",   // 购买所需的道具ID
                        ShopItemName = "神秘符文",       // 商品显示名称
                        RequiredItemName = "符文召唤书", // 所需道具显示名称
                        ShopType = 8                    // 所属商店类型
                    },

                    // 龙魂召唤：需要龙魂召唤石
                    new SpecialItemMapping
                    {
                        ShopItemId = "88230003",        // 商店中的商品ID
                        RequiredItemId = "820230308",   // 购买所需的道具ID
                        ShopItemName = "龙魂召唤",       // 商品显示名称
                        RequiredItemName = "龙魂召唤石", // 所需道具显示名称
                        ShopType = 8                    // 所属商店类型
                    }
                },

                // 使用默认消息配置
                Messages = new MessageConfig(),

                // 使用默认购买限制配置
                Limits = new PurchaseLimitConfig(),

                // 特殊事件默认配置
                SpecialEvents = new SpecialEventConfig
                {
                    AprilFoolEnabled = true  // 默认启用愚人节活动
                }
            };
        }

        /// <summary>
        /// 重新加载配置
        /// 清空当前缓存，下次访问时会重新从服务器加载
        /// 用于配置热更新场景
        /// </summary>
        public static void ReloadConfig()
        {
            lock (_lock)
            {
                // 清空缓存，触发重新加载
                _config = null;
            }
        }

        #endregion
    }
}
