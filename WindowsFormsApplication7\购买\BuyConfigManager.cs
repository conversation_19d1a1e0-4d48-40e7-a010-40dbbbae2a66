﻿using Newtonsoft.Json;
using Shikong.Pokemon2.PCG;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ShikongPlus.Pokemon2.PCG.购买
{
    public static class BuyConfigManager
    {

        private static BuyConfig _config;
        private static readonly object _lock = new object();

        public static BuyConfig Config
        {
            get
            {
                if (_config == null)
                {
                    lock (_lock)
                    {
                        if (_config == null)
                        {
                            LoadConfig();
                        }
                    }
                }
                return _config;
            }
        }

        public static void LoadConfig()
        {
            try
            {
                string configJson = new ConvertJson().GetWeb($"{DataProcess.SK_getUrl()}:9696/sk/buyConfig.json");
                if (!string.IsNullOrEmpty(configJson))
                {
                    string decryptedConfig = SkRC4.DES.DecryptRC4(configJson, new DataProcess().GetKey(1));
                    _config = JsonConvert.DeserializeObject<BuyConfig>(decryptedConfig);
                }
                else
                {
                    _config = GetDefaultConfig();
                }
            }
            catch (Exception ex)
            {
                LogSystem.JoinLog(LogSystem.EventKind.加入日志, $"加载购买配置失败: {ex.Message}");
                _config = GetDefaultConfig();
            }
        }

        private static BuyConfig GetDefaultConfig()
        {
            return new BuyConfig
            {
                ShopTypes = new ShopTypeConfig
                {
                    CrystalShopType = 6,
                    SpecialShopType = 8,
                    ShopTypeNames = new Dictionary<int, string>
                {
                    {1, "元宝商店"},
                    {2, "水晶商店"},
                    {3, "金币商店"},
                    {4, "积分商店"},
                    {5, "威望商店"},
                    {6, "结晶商店"},
                    {8, "特殊商店"}
                }
                },
                SpecialItems = new List<SpecialItemMapping>
            {
                new SpecialItemMapping
                {
                    ShopItemId = "88230001",
                    RequiredItemId = "820230309",
                    ShopItemName = "神龙宝匣",
                    RequiredItemName = "神龙宝藏钥匙",
                    ShopType = 8
                },
                new SpecialItemMapping
                {
                    ShopItemId = "88230002",
                    RequiredItemId = "820230307",
                    ShopItemName = "神秘符文",
                    RequiredItemName = "符文召唤书",
                    ShopType = 8
                },
                new SpecialItemMapping
                {
                    ShopItemId = "88230003",
                    RequiredItemId = "820230308",
                    ShopItemName = "龙魂召唤",
                    RequiredItemName = "龙魂召唤石",
                    ShopType = 8
                }
            },
                Messages = new MessageConfig(),
                Limits = new PurchaseLimitConfig(),
                SpecialEvents = new SpecialEventConfig
                {
                    AprilFoolEnabled = true
                }
            };
        }

        public static void ReloadConfig()
        {
            lock (_lock)
            {
                _config = null;
            }
        }
    }
}
