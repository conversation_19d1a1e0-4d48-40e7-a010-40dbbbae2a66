﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.IO;
using System.Management;
using System.Net;
using System.Text;
using System.Windows.Forms;

namespace 信息收集工具
{
	public partial class Form1 : Form
	{
		public Form1()
		{
			this.InitializeComponent();
		}

		private void button1_Click(object sender, EventArgs e)
		{
            try
            {
                ManagementObjectSearcher managementObjectSearcher = new ManagementObjectSearcher("SELECT * FROM Win32_PhysicalMedia");
                string text = null;
                using (ManagementObjectCollection.ManagementObjectEnumerator enumerator = managementObjectSearcher.Get().GetEnumerator())
                {
                    if (enumerator.MoveNext())
                    {
                        text = ((ManagementObject)enumerator.Current)["SerialNumber"].ToString().Trim();
                    }
                }
                if (string.IsNullOrEmpty(text))
                {
                    this.textBox1.Text = "收集信息失败，请勿在虚拟机中收集信息！";
                    return;
                }
                this.textBox1.Text = text;
            }
            catch
            {
                this.textBox1.Text = "获取信息失败。";
            }



            //ManagementObjectSearcher managementObjectSearcher = new ManagementObjectSearcher("SELECT * FROM Win32_PhysicalMedia");
            //string text = null;
            //using (ManagementObjectCollection.ManagementObjectEnumerator enumerator = managementObjectSearcher.Get().GetEnumerator())
            //{
            //    if (enumerator.MoveNext())
            //    {
            //        text = ((ManagementObject)enumerator.Current)["SerialNumber"].ToString().Trim();
            //    }
            //}
            //if (string.IsNullOrEmpty(text))
            //{
            //    this.textBox1.Text = "收集信息失败，请勿在虚拟机中收集信息！";
            //    return;
            //}
            //this.textBox1.Text = text;
        }

        private void button2_Click(object sender, EventArgs e)
		{
			try
			{
				HttpWebRequest httpWebRequest = (HttpWebRequest)WebRequest.Create(new Uri("http://shikong.info/api/getip"));
				httpWebRequest.ServicePoint.Expect100Continue = false;
				httpWebRequest.ServicePoint.UseNagleAlgorithm = false;
				httpWebRequest.ServicePoint.ConnectionLimit = 65500;
				httpWebRequest.AllowWriteStreamBuffering = false;
				httpWebRequest.Proxy = null;
				HttpWebResponse httpWebResponse = (HttpWebResponse)httpWebRequest.GetResponse();
				Stream responseStream = httpWebResponse.GetResponseStream();
				if (responseStream == null)
				{
					throw new InvalidOperationException();
				}
				StreamReader streamReader = new StreamReader(responseStream, Encoding.GetEncoding("gb2312"));
				string text = streamReader.ReadToEnd();
				streamReader.Close();
				httpWebResponse.Close();
				text = text.Replace("\"", "");
				this.textBox1.Text = text;
			}
			catch (Exception)
			{
				this.textBox1.Text = "获取信息失败，请检查网络状况后重试";
			}
		}
	}
}
