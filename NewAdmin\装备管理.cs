﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using System.IO;
using Newtonsoft.Json;
using 怪物信息 = Shikong.Pokemon2.PCG.MonsterInfo;
using 用户信息 = Shikong.Pokemon2.PCG.UserInfo;
using 宠物信息 = Shikong.Pokemon2.PCG.PetInfo;
using 宠物类型 = Shikong.Pokemon2.PCG.PetConfig;
using 道具信息 = Shikong.Pokemon2.PCG.PropInfo;
using 地图信息 = Shikong.Pokemon2.PCG.MapInfo;
using 装备类型 = Shikong.Pokemon2.PCG.EquipmentType;
using 装备信息 = Shikong.Pokemon2.PCG.EquipmentInfo;
using 道具类型 = Shikong.Pokemon2.PCG.PropType;
using 道具具体信息 = Shikong.Pokemon2.PCG.PropConfig;
using 数据处理 = Shikong.Pokemon2.PCG.DataProcess;
using 商店道具信息 = Shikong.Pokemon2.PCG.GoodsInfo;
using Shikong.Pokemon2.PCG;
using PetShikongTools;
using static PetShikongTools.SkCryptography;

namespace Admin
{
    public partial class 装备管理 : Form
    {
        public 装备管理()
        {
            InitializeComponent();
        }

        private void 装备管理_Load(object sender, EventArgs e)
        {

        }

        List<装备类型> 所有装备 = new 数据处理().GetEquipmentList();
        //string 装备使用类型;
        装备类型 现编辑装备= new 装备类型();
        List<suits> 所有套装 = new 数据处理().GetAllSuits();
        数据处理 处理 = new 数据处理();
        private void button1_Click(object sender, EventArgs e)
        {
            List<装备类型> 所有装备 = new 数据处理().GetEquipmentList();
            dataGridView1.Rows.Clear();
            //MessageBox.Show(所有装备.Count.ToString());
            foreach (装备类型 装备 in 所有装备)
            {
                try
                {
                    string[] str = { 装备.ID, 装备.名字, 取套装名称(装备.suitID) };
                    dataGridView1.Rows.Add(str);
                }
                catch(Exception ex) {
               //     MessageBox.Show(ex.Message);
                }
            }
        }

        private void dataGridView1_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {
            string typeID= dataGridView1.Rows[dataGridView1.CurrentRow.Index].Cells["num"].Value.ToString();
            现编辑装备 = new 装备类型();
            foreach (装备类型 装备 in 所有装备)
            {
                if (装备.ID.Equals(typeID))
                {
                    现编辑装备 = 装备;
                    break;
                }
            }
            //现编辑装备 = 所有装备[dataGridView1.CurrentRow.Index];
            装备_ID.Text = 现编辑装备.ID;
            装备名字.Text = 现编辑装备.名字;
            装备加深.Text = 现编辑装备.加深;
            装备吸血.Text = 现编辑装备.吸血;
            装备吸魔.Text = 现编辑装备.吸魔;
            装备图标.Text = 现编辑装备.ICO;
            装备抵消.Text = 现编辑装备.抵消;
            装备攻击.Text = 现编辑装备.攻击;
            装备生命.Text = 现编辑装备.生命;
            装备类型.Text = 现编辑装备.类型;
            装备速度.Text = 现编辑装备.速度;
            装备闪避.Text = 现编辑装备.闪避;
            装备防御.Text = 现编辑装备.防御;
            装备魔法.Text = 现编辑装备.魔法;
            装备命中.Text = 现编辑装备.命中;
            装备套装.Text = 现编辑装备.suitID;
            主属性.Text = 现编辑装备.主属性;
            textBox4.Text = 现编辑装备.说明;
            textBox1.Text = 数据处理.EDC_Path + GetHash.GetStringHash(SkRC4.DES.EncryptRC4(现编辑装备.ID, T.GetKey(2))) + ".dat";

        }

        private string 取套装名称(string 套装ID)
        {
            if (套装ID==null) return "";
            foreach (suits 套装 in 所有套装)
            {
                if (套装.套装序号.Equals(套装ID))
                {
                    return 套装.套装名;
                }
            }
            return "";


        }

        private void button8_Click(object sender, EventArgs e)
        {
            if (textBox2.Text != "")
            {
                dataGridView1.Rows.Clear();
                if (所有装备.Count < 1)
                    所有装备 = new 数据处理().GetEquipmentList();

                foreach (装备类型 装备 in 所有装备)
                {
                    if (装备.ID.IndexOf(textBox2.Text)>-1 || 装备.名字.IndexOf(textBox2.Text) > -1)
                    {
                        string[] str = { 装备.ID, 装备.名字, 取套装名称(装备.suitID) };
                        dataGridView1.Rows.Add(str);
                    }
                }
            }
        }

        private void button12_Click(object sender, EventArgs e)
        {

            装备类型 装备 = 现编辑装备;
            装备.ID = 装备_ID.Text;
            装备.抵消 = 装备抵消.Text;
            装备.防御 = 装备防御.Text;
            装备.攻击 = 装备攻击.Text;
            装备.加深 = 装备加深.Text;
            装备.类型 = 装备类型.Text;
            装备.名字 = 装备名字.Text;
            装备.魔法 = 装备魔法.Text;
            装备.闪避 = 装备闪避.Text;
            装备.生命 = 装备生命.Text;
            装备.速度 = 装备速度.Text;
            装备.吸魔 = 装备吸魔.Text;
            装备.吸血 = 装备吸血.Text;
            装备.说明 = textBox4.Text;
            装备.ICO = 装备图标.Text;
            装备.命中 = 装备命中.Text;
            装备.主属性 = 主属性.Text;
            装备.suitID = 装备套装.Text;
            装备.五行限制 = textBox3.Text;
            string zbpath = 数据处理.EDC_Path + GetHash.GetStringHash(SkRC4.DES.EncryptRC4(装备.ID, T.GetKey(2))) + ".dat";
            string zb = SkRC4.DES.EncryptRC4(JsonConvert.SerializeObject(装备), T.GetKey(1));
            new 数据处理().SaveFile(zb, zbpath);
            

        }

        private void button2_Click(object sender, EventArgs e)
        {
            装备类型 装备 = new 装备类型();
            装备.ID = 装备_ID.Text;
            装备.抵消 = 装备抵消.Text;
            装备.防御 = 装备防御.Text;
            装备.攻击 = 装备攻击.Text;
            装备.加深 = 装备加深.Text;
            装备.类型 = 装备类型.Text;
            装备.名字 = 装备名字.Text;
            装备.魔法 = 装备魔法.Text;
            装备.闪避 = 装备闪避.Text;
            装备.生命 = 装备生命.Text;
            装备.速度 = 装备速度.Text;
            装备.吸魔 = 装备吸魔.Text;
            装备.吸血 = 装备吸血.Text;
            装备.说明 = textBox4.Text;
            装备.ICO = 装备图标.Text;
            装备.命中 = 装备命中.Text;
            装备.主属性 = 主属性.Text;
            装备.五行限制 = textBox3.Text;
            if (File.Exists(数据处理.EDC_Path + GetHash.GetStringHash(SkRC4.DES.EncryptRC4(装备.ID, T.GetKey(2))) + ".dat"))
            {
                MessageBox.Show("增加装备失败,序号已存在!");
            }else
            {
           new 数据处理().SaveFile(SkRC4.DES.EncryptRC4(JsonConvert.SerializeObject(装备), T.GetKey(1)), 数据处理.EDC_Path + GetHash.GetStringHash(SkRC4.DES.EncryptRC4(装备.ID, T.GetKey(2))) + ".dat");

            }
           
        }

        private void linkLabel1_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            套装管理 t = new 套装管理();
            t.TEXT = 装备套装;
            t.Show();
        }

        private void 按当前列表顺序复制表格数据_Click(object sender, EventArgs e)
        {
            if (dataGridView1.Rows.Count <=1)
            {
                MessageBox.Show("没有数据");
                return;
            }
            string excelText = "";
            foreach (DataGridViewRow row in dataGridView1.Rows)
            {
                var eq = 所有装备.FirstOrDefault(ee => ee.ID.Equals(row.Cells[0].Value.ToString()));
                if (eq != null)
                {
                    //复制表格数据  制表符/t
                    //装备名称	部位	主属性	所属套装
                    //攻击	防御
                    //生命	加深  抵消
                    //吸血   命中 五行限制
                    string txt = $"{row.Cells[1].Value.ToString().Replace("\r\n","")}\t{eq.类型.Replace("\r\n", "")}\t{eq.主属性.Replace("\r\n", "")}\t{row.Cells[2].Value.ToString().Replace("\r\n", "")}\t" +
                        $"{属性内容处理(eq.攻击)}\t{属性内容处理(eq.防御)}\t" +
                        $"{属性内容处理(eq.生命)}\t{属性内容处理(eq.加深)}\t{属性内容处理(eq.抵消)}\t" +
                        $"{属性内容处理(eq.吸血)}\t{属性内容处理(eq.命中)}\t{属性内容处理(eq.五行限制)}\r\n";
                    excelText += txt;
                }
            }
            Clipboard.SetText(excelText);
            MessageBox.Show("已复制");
        }
        /// <summary>
        /// 如果传入的属性不包含小数点则返回整数，否则返回百分比
        /// </summary>
        /// <param name="sx"></param>
        /// <returns></returns>
        string 属性内容处理(string sx)
        {
            if (sx == null)
            {
                return null;
            }
            if (sx.IndexOf(".") == -1)
            {
                return sx;
            }
            else
            {
                double num = Convert.ToDouble(sx) * 100;
                return num.ToString() + "%";
            }
        }
    }
}
