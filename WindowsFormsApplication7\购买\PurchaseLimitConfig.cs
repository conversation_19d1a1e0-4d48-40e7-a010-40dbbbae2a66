﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ShikongPlus.Pokemon2.PCG.购买
{
    /// <summary>
    /// 购买限制配置类
    /// 定义各种商店的购买数量限制和约束条件
    /// 用于防止恶意刷购和维护游戏平衡
    /// </summary>
    public class PurchaseLimitConfig
    {
        /// <summary>
        /// 普通商店的最大购买数量
        /// 对应原代码中DataProcess.Shopping()方法的quantity > 1000检查
        /// 适用于元宝商店、水晶商店、金币商店等常规商店
        /// </summary>
        public int MaxQuantityNormal { get; set; } = 1000;

        /// <summary>
        /// 结晶商店的最大购买数量
        /// 对应原代码中type == 6的特殊数量限制检查
        /// 结晶商店通常限制更严格，防止大量购买
        /// </summary>
        public int MaxQuantityCrystal { get; set; } = 100;

        /// <summary>
        /// 按商店类型定义的购买数量上限字典
        /// Key: 商店类型ID (1=元宝, 2=水晶, 3=金币, 4=积分, 5=威望, 6=结晶, 8=特殊)
        /// Value: 该商店类型的最大购买数量
        /// 支持为不同商店类型设置不同的购买限制
        /// 如果某个商店类型未在此字典中定义，则使用MaxQuantityNormal作为默认值
        /// </summary>
        public Dictionary<int, int> ShopTypeMaxQuantity { get; set; }
    }
}
