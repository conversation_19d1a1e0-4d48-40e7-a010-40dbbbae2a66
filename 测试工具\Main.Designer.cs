﻿namespace 测试工具
{
    partial class Main
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            AntdUI.Tabs.StyleLine styleLine1 = new AntdUI.Tabs.StyleLine();
            this.TitleHeader = new AntdUI.PageHeader();
            this.input_Outout = new AntdUI.Input();
            this.button_Log_Clear = new AntdUI.Button();
            this.Tabs = new AntdUI.Tabs();
            this.Home = new AntdUI.TabPage();
            this.MapTest = new AntdUI.TabPage();
            this.Map_checkbox_TTT = new AntdUI.Checkbox();
            this.Map_Input_FightNum = new AntdUI.Input();
            this.label2 = new AntdUI.Label();
            this.Map_button_TestDrop = new AntdUI.Button();
            this.label1 = new AntdUI.Label();
            this.Map_select_MapList = new AntdUI.Select();
            this.ItemTest = new AntdUI.TabPage();
            this.Item_input_PID = new AntdUI.Input();
            this.label3 = new AntdUI.Label();
            this.Item_input_UserNum = new AntdUI.Input();
            this.label4 = new AntdUI.Label();
            this.Item_button_TestItem = new AntdUI.Button();
            this.Tabs.SuspendLayout();
            this.MapTest.SuspendLayout();
            this.ItemTest.SuspendLayout();
            this.SuspendLayout();
            // 
            // TitleHeader
            // 
            this.TitleHeader.Dock = System.Windows.Forms.DockStyle.Top;
            this.TitleHeader.Font = new System.Drawing.Font("Microsoft YaHei UI", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.TitleHeader.Location = new System.Drawing.Point(0, 0);
            this.TitleHeader.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.TitleHeader.Name = "TitleHeader";
            this.TitleHeader.ShowButton = true;
            this.TitleHeader.Size = new System.Drawing.Size(1004, 40);
            this.TitleHeader.TabIndex = 0;
            this.TitleHeader.Text = "测试工具";
            // 
            // input_Outout
            // 
            this.input_Outout.Dock = System.Windows.Forms.DockStyle.Right;
            this.input_Outout.Location = new System.Drawing.Point(731, 40);
            this.input_Outout.MaxLength = 999999999;
            this.input_Outout.Multiline = true;
            this.input_Outout.Name = "input_Outout";
            this.input_Outout.ReadOnly = true;
            this.input_Outout.Size = new System.Drawing.Size(273, 537);
            this.input_Outout.TabIndex = 1;
            // 
            // button_Log_Clear
            // 
            this.button_Log_Clear.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.button_Log_Clear.Font = new System.Drawing.Font("Microsoft YaHei UI", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.button_Log_Clear.IconRatio = 1F;
            this.button_Log_Clear.IconSvg = "ClearOutlined";
            this.button_Log_Clear.Location = new System.Drawing.Point(966, 47);
            this.button_Log_Clear.Margin = new System.Windows.Forms.Padding(0);
            this.button_Log_Clear.Name = "button_Log_Clear";
            this.button_Log_Clear.Size = new System.Drawing.Size(30, 30);
            this.button_Log_Clear.TabIndex = 2;
            this.button_Log_Clear.Click += new System.EventHandler(this.button_Log_Clear_Click);
            // 
            // Tabs
            // 
            this.Tabs.Controls.Add(this.Home);
            this.Tabs.Controls.Add(this.MapTest);
            this.Tabs.Controls.Add(this.ItemTest);
            this.Tabs.Cursor = System.Windows.Forms.Cursors.Default;
            this.Tabs.Dock = System.Windows.Forms.DockStyle.Fill;
            this.Tabs.Gap = 12;
            this.Tabs.Location = new System.Drawing.Point(0, 40);
            this.Tabs.Name = "Tabs";
            this.Tabs.Pages.Add(this.Home);
            this.Tabs.Pages.Add(this.MapTest);
            this.Tabs.Pages.Add(this.ItemTest);
            this.Tabs.SelectedIndex = 2;
            this.Tabs.Size = new System.Drawing.Size(731, 537);
            this.Tabs.Style = styleLine1;
            this.Tabs.TabIndex = 3;
            this.Tabs.Text = "tabs1";
            // 
            // Home
            // 
            this.Home.Location = new System.Drawing.Point(-725, -503);
            this.Home.Name = "Home";
            this.Home.Size = new System.Drawing.Size(725, 503);
            this.Home.TabIndex = 0;
            this.Home.Text = "Home";
            // 
            // MapTest
            // 
            this.MapTest.Controls.Add(this.Map_checkbox_TTT);
            this.MapTest.Controls.Add(this.Map_Input_FightNum);
            this.MapTest.Controls.Add(this.label2);
            this.MapTest.Controls.Add(this.Map_button_TestDrop);
            this.MapTest.Controls.Add(this.label1);
            this.MapTest.Controls.Add(this.Map_select_MapList);
            this.MapTest.Location = new System.Drawing.Point(-725, -499);
            this.MapTest.Name = "MapTest";
            this.MapTest.Size = new System.Drawing.Size(725, 499);
            this.MapTest.TabIndex = 1;
            this.MapTest.Text = "MapTest";
            // 
            // Map_checkbox_TTT
            // 
            this.Map_checkbox_TTT.Location = new System.Drawing.Point(9, 119);
            this.Map_checkbox_TTT.Name = "Map_checkbox_TTT";
            this.Map_checkbox_TTT.Size = new System.Drawing.Size(138, 40);
            this.Map_checkbox_TTT.TabIndex = 5;
            this.Map_checkbox_TTT.Text = "TTT";
            // 
            // Map_Input_FightNum
            // 
            this.Map_Input_FightNum.Location = new System.Drawing.Point(94, 67);
            this.Map_Input_FightNum.Name = "Map_Input_FightNum";
            this.Map_Input_FightNum.Size = new System.Drawing.Size(233, 46);
            this.Map_Input_FightNum.TabIndex = 4;
            this.Map_Input_FightNum.Text = "14400";
            // 
            // label2
            // 
            this.label2.Location = new System.Drawing.Point(9, 67);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(88, 47);
            this.label2.TabIndex = 3;
            this.label2.Text = "FightNum";
            this.label2.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            // 
            // Map_button_TestDrop
            // 
            this.Map_button_TestDrop.Location = new System.Drawing.Point(333, 14);
            this.Map_button_TestDrop.Name = "Map_button_TestDrop";
            this.Map_button_TestDrop.Size = new System.Drawing.Size(112, 47);
            this.Map_button_TestDrop.TabIndex = 2;
            this.Map_button_TestDrop.Text = "TestDrop";
            this.Map_button_TestDrop.Type = AntdUI.TTypeMini.Primary;
            this.Map_button_TestDrop.Click += new System.EventHandler(this.Map_button_TestDrop_Click);
            // 
            // label1
            // 
            this.label1.Location = new System.Drawing.Point(9, 14);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(88, 47);
            this.label1.TabIndex = 1;
            this.label1.Text = "SelectMap";
            this.label1.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            // 
            // Map_select_MapList
            // 
            this.Map_select_MapList.Location = new System.Drawing.Point(94, 14);
            this.Map_select_MapList.Name = "Map_select_MapList";
            this.Map_select_MapList.Size = new System.Drawing.Size(233, 47);
            this.Map_select_MapList.TabIndex = 0;
            // 
            // ItemTest
            // 
            this.ItemTest.Controls.Add(this.Item_button_TestItem);
            this.ItemTest.Controls.Add(this.Item_input_UserNum);
            this.ItemTest.Controls.Add(this.label4);
            this.ItemTest.Controls.Add(this.Item_input_PID);
            this.ItemTest.Controls.Add(this.label3);
            this.ItemTest.Location = new System.Drawing.Point(3, 35);
            this.ItemTest.Name = "ItemTest";
            this.ItemTest.Size = new System.Drawing.Size(725, 499);
            this.ItemTest.TabIndex = 2;
            this.ItemTest.Text = "ItemTest";
            // 
            // Item_input_PID
            // 
            this.Item_input_PID.Location = new System.Drawing.Point(95, 16);
            this.Item_input_PID.Name = "Item_input_PID";
            this.Item_input_PID.Size = new System.Drawing.Size(233, 46);
            this.Item_input_PID.TabIndex = 6;
            // 
            // label3
            // 
            this.label3.Location = new System.Drawing.Point(10, 16);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(88, 47);
            this.label3.TabIndex = 5;
            this.label3.Text = "ItemID";
            this.label3.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            // 
            // Item_input_UserNum
            // 
            this.Item_input_UserNum.Location = new System.Drawing.Point(95, 69);
            this.Item_input_UserNum.Name = "Item_input_UserNum";
            this.Item_input_UserNum.Size = new System.Drawing.Size(233, 46);
            this.Item_input_UserNum.TabIndex = 8;
            this.Item_input_UserNum.Text = "1000";
            // 
            // label4
            // 
            this.label4.Location = new System.Drawing.Point(10, 69);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(88, 47);
            this.label4.TabIndex = 7;
            this.label4.Text = "UseNum";
            this.label4.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            // 
            // Item_button_TestItem
            // 
            this.Item_button_TestItem.Location = new System.Drawing.Point(334, 16);
            this.Item_button_TestItem.Name = "Item_button_TestItem";
            this.Item_button_TestItem.Size = new System.Drawing.Size(112, 47);
            this.Item_button_TestItem.TabIndex = 9;
            this.Item_button_TestItem.Text = "TestItem";
            this.Item_button_TestItem.Type = AntdUI.TTypeMini.Primary;
            this.Item_button_TestItem.Click += new System.EventHandler(this.Item_button_TestItem_Click);
            // 
            // Main
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(8F, 20F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.White;
            this.ClientSize = new System.Drawing.Size(1004, 577);
            this.Controls.Add(this.Tabs);
            this.Controls.Add(this.button_Log_Clear);
            this.Controls.Add(this.input_Outout);
            this.Controls.Add(this.TitleHeader);
            this.Font = new System.Drawing.Font("Microsoft YaHei UI", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.Name = "Main";
            this.ShowIcon = false;
            this.Text = "测试工具";
            this.Load += new System.EventHandler(this.Main_Load);
            this.Tabs.ResumeLayout(false);
            this.MapTest.ResumeLayout(false);
            this.ItemTest.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private AntdUI.PageHeader TitleHeader;
        private AntdUI.Input input_Outout;
        private AntdUI.Button button_Log_Clear;
        private AntdUI.Tabs Tabs;
        private AntdUI.TabPage Home;
        private AntdUI.TabPage MapTest;
        private AntdUI.TabPage ItemTest;
        private AntdUI.Label label1;
        private AntdUI.Select Map_select_MapList;
        private AntdUI.Button Map_button_TestDrop;
        private AntdUI.Label label2;
        private AntdUI.Input Map_Input_FightNum;
        private AntdUI.Checkbox Map_checkbox_TTT;
        private AntdUI.Input Item_input_UserNum;
        private AntdUI.Label label4;
        private AntdUI.Input Item_input_PID;
        private AntdUI.Label label3;
        private AntdUI.Button Item_button_TestItem;
    }
}

