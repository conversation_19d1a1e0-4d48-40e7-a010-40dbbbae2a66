﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Windows.Forms;
using Newtonsoft.Json;

namespace Shikong.Pokemon2.PCG
{
    public static class PetProcess
    {
        /// <summary>
        /// 获取宠物的成长上限
        /// </summary>
        /// <param name="pet">宠物信息</param>
        /// <returns>成长上限值</returns>
        private static double GetPetGrowthLimit(PetInfo pet)
        {
            // 如果宠物有设置成长上限，使用该值
            if (!string.IsNullOrEmpty(pet.成长上限))
            {
                return Convert.ToDouble(pet.成长上限);
            }

            // 否则使用默认值2000万
            return 20000000;
        }

        public enum 五行序号
        {
            金 = 1,
            木 = 2,
            水 = 3,
            火 = 4,
            土 = 5,
            神 = 6,
            神圣 = 7,
            聖 = 8,
            佛 = 9,
            魔 = 10,
            人 = 11,
            鬼 = 12,
            巫 = 13,
            萌 = 14,
            仙 = 15,
            灵 = 16,
            次元 = 17 
        };

        internal static double Effect = 1.0;

        /*private static Dictionary<string, string> _promotion = new Dictionary<string, string>
        {
            { "金", "水" },
            { "水", "木" },
            { "木", "火" },
            { "火", "土" },
            { "土", "金" }
        };
        private static Dictionary<string, string> _restraint = new Dictionary<string, string>
        {
            { "金", "木" },
            { "木", "土" },
            { "土", "水" },
            { "水", "火" },
            { "火", "金" }
        };*/
        private static double _lastHcTime;
        private static double _lastNpTime;

        /// <summary>
        /// 这个还没写完
        /// </summary>
        /// <param name="pet1"></param>
        /// <param name="pet2"></param>
        /// <param name="道具1脚本"></param>
        /// <param name="道具2脚本"></param>
        /// <param name="是否删除"></param>
        /// <returns></returns>
        /*internal static String SynthesisA(PetInfo pet1, PetInfo pet2, String script1, String script2, out bool 是否删除)
        {
            是否删除 = false;
            //new AntiCheat().反作弊();

            if (_lastHcTime != 0.0)
            {
                if (new DataProcess().GetSystemTS() - _lastHcTime < 5000)
                {
                    return "合成CD未到!";
                }
            }

            if (Convert.ToInt16(Enum.Parse(typeof(五行序号), pet1.五行)) > 5 ||
                Convert.ToInt16(Enum.Parse(typeof(五行序号), pet2.五行)) > 5)
            {
                return "合成失败,不能放入非五系宠!";
            }
            //if (宠物1.被抽取 == "1" || 宠物2.被抽取 == "1")
            //{
            //    return "所选宠物已被抽取过CC,无法再进行合成!";
            //}
            if (Convert.ToInt32(pet1.等级) < NumEncrypt.四十())
            {
                return "主宠等级没有达到40级!";
            }
            if (Convert.ToInt32(pet2.等级) < NumEncrypt.四十())
            {
                return "副宠等级没有达到40级!";
            }
            bool 主宠放入 = false;
            UserInfo user = new DataProcess().ReadUserInfo();
            if (user.主宠物.Equals(pet1.宠物序号) || user.主宠物.Equals(pet2.宠物序号))
            {
                主宠放入 = true;
            }
            if (Convert.ToInt64(user.金币) < NumEncrypt.五万())
            {
                return "金币不足!合成一次需要50000金币噢!";
            }
            // new DataProcess().修改主宠(宠物1.宠物序号);
            user.主宠物 = pet1.宠物序号;


            new DataProcess().Undress_Pet(pet1.宠物序号);
            new DataProcess().Undress_Pet(pet2.宠物序号);


            ///以下为材料处理代码

            bool 失败不消失 = false;
            bool 肯定成功 = false;
            bool 固定神 = false;
            bool 随机神 = false;
            double 效果加成 = 0.0;
            double 成功率加成 = 0.0;
            user.金币 = (Convert.ToInt64(user.金币) - NumEncrypt.五万()).ToString();
            new DataProcess().SaveUserDataFile(user);
            String[] 脚本组 = script1.Split('|');
            String[] 小脚本组;
            foreach (string 脚本 in 脚本组)
            {
                if (脚本.Equals("守护"))
                {
                    失败不消失 = true;
                }
                if (脚本.Equals("成神"))
                {
                    固定神 = true;
                }
                if (脚本.Equals("完美守护"))
                {
                    失败不消失 = true;
                    肯定成功 = true;
                }
                if (脚本.Equals("随机神"))
                {
                    随机神 = true;
                }
                else
                {
                    小脚本组 = 脚本.Split('*');
                    if (小脚本组.Length > 1)
                    {
                        if (小脚本组[0].Equals("成长加成"))
                        {
                            效果加成 += Convert.ToDouble(小脚本组[1]);
                        }
                        if (小脚本组[0].Equals("成功加成"))
                        {
                            成功率加成 += Convert.ToDouble(小脚本组[1]);
                        }
                    }
                }
            }

            脚本组 = script2.Split('|');
            foreach (string 脚本 in 脚本组)
            {
                if (脚本.Equals("守护"))
                {
                    失败不消失 = true;
                }
                if (脚本.Equals("完美守护"))
                {
                    失败不消失 = true;
                    肯定成功 = true;
                }
                if (脚本.Equals("成神"))
                {
                    固定神 = true;
                }
                if (脚本.Equals("随机神"))
                {
                    随机神 = true;
                }
                else
                {
                    小脚本组 = 脚本.Split('*');
                    if (小脚本组.Length > 1)
                    {
                        if (小脚本组[0].Equals("成长加成"))
                        {
                            效果加成 += Convert.ToDouble(小脚本组[1]);
                        }
                        if (小脚本组[0].Equals("成功加成"))
                        {
                            成功率加成 += Convert.ToDouble(小脚本组[1]);
                        }

                    }
                }
            }
            //以上是添加物品效果处理
            //以下是合成成功处理
            是否删除 = true;
            //肯定成功 = true;
            if (!肯定成功)
            {
                int 五行影响 = 0;
                if (_promotion[pet1.五行].Equals(pet2.五行))
                {
                    五行影响 = 10;
                }
                if (_promotion[pet1.五行].Equals(pet2.五行))
                {
                    五行影响 = -20;
                }
                if (DataProcess.RandomGenerator.Next(1, 101) >
                    10 + 五行影响 + Convert.ToInt32(NumEncrypt.一百() * 成功率加成)) //即不在成功范围内为失败
                {
                    if (!失败不消失)
                    {
                        new DataProcess().AbandonPet(pet2.宠物序号);
                    }
                    _lastHcTime = new DataProcess().GetSystemTS();
                    return "合成失败!";
                }
            }

            PetConfig result = new PetConfig();
            if (固定神)
            {
                string tempId = new DataProcess().GetAppointedSW(pet1, pet2, 1);
                if (tempId == null)
                {
                    return "这样是合成不出来宠物的!";
                }
                else
                {
                    result = new DataProcess().GetAppointedPetType(tempId);
                }
            }
            else if (随机神)
            {
                List<PetConfig> SPList = new DataProcess().GetASPList("神");
                List<PetConfig> SSPList = new DataProcess().GetASPList("神圣");
                int random = DataProcess.RandomGenerator.Next(1, NumEncrypt.一百());
                if (random == 77)
                {
                    random = DataProcess.RandomGenerator.Next(0, SSPList.Count);
                    result = SSPList[random];
                }
                else
                {
                    random = DataProcess.RandomGenerator.Next(0, SPList.Count);
                    result = SPList[random];
                }
            }
            else if (!固定神 && !随机神)
            {
                string tempId = new DataProcess().GetAppointedSW(pet1, pet2, 0);
                if (tempId == null)
                {
                    return "这样是合成不出来宠物的!";
                }
                else
                {
                    result = new DataProcess().GetAppointedPetType(tempId);
                }
            }
            //合成成长计算          
            double 成长限制系数;
            if (Convert.ToDouble(pet1.成长) <= NumEncrypt.六十())
            {
                成长限制系数 = Convert.ToDouble(pet1.成长) * NumEncrypt.十() + NumEncrypt.四百();
            }
            else if (Convert.ToDouble(pet1.成长) <= NumEncrypt.八十())
            {
                成长限制系数 = (Convert.ToDouble(pet1.成长) - NumEncrypt.六十()) * NumEncrypt.三十五() + NumEncrypt.一千();
            }
            else
            {
                成长限制系数 = (Convert.ToDouble(pet1.成长) - NumEncrypt.六十()) * NumEncrypt.六十() + NumEncrypt.五百();
            }

            double 成长加成系数 = 0.0;
            if (Convert.ToDouble(pet1.成长) <= NumEncrypt.五())
            {
                成长加成系数 += NumEncrypt.零点八();
                if (Convert.ToDouble(pet2.成长) <= NumEncrypt.五())
                {
                    成长加成系数 += NumEncrypt.零点八();
                }
            }
            else if (Convert.ToDouble(pet1.成长) <= NumEncrypt.十())
            {
                成长加成系数 += NumEncrypt.零点四();
            }
            else if (Convert.ToDouble(pet1.成长) <= NumEncrypt.五十())
            {
                成长加成系数 += NumEncrypt.零点二();
            }

            var 获得成长 = Convert.ToDouble(pet1.成长) * (1 + (Convert.ToDouble(pet1.等级) - NumEncrypt.三十五()) / 成长限制系数) +
                       Convert.ToDouble(pet2.成长) * (NumEncrypt.零点二() +
                                                    NumEncrypt.零点零零五() * (Convert.ToDouble(pet2.等级) - NumEncrypt.三十五())
                       ) - Convert.ToDouble(pet1.成长);

            double CalcCC1 = (1 + 成长加成系数) * (1 + 效果加成);
            double CalcCC2 = (1 + 成长加成系数 + 效果加成);

            if (CalcCC1 >= CalcCC2)
            {
                获得成长 = 获得成长 * CalcCC2 * (1 + 0.005 * Convert.ToInt16(user.vip));
            }
            else if (CalcCC1 < CalcCC2)
            {
                获得成长 = 获得成长 * CalcCC1 * (1 + 0.005 * Convert.ToInt16(user.vip));
            }
            //成长计算完毕

            获得成长 = 获得成长 * Effect;
            //合成神宠几率计算


            pet1 = PetCalc.CalcPetAttribute(pet1, false);

            if (固定神 || 随机神)
            {
                // 获取主宠的成长上限
                double growthLimit = GetPetGrowthLimit(pet1);

                if (Convert.ToDouble(pet1.成长) + 获得成长 >= growthLimit)
                {
                    pet1.成长 = (growthLimit / 3).ToString(); // 神宠特殊处理，成长上限的1/3
                }
                else if (Convert.ToDouble(pet1.成长) + 获得成长 >= 60)
                {
                    pet1.成长 = (60 + (Convert.ToDouble(pet1.成长) + 获得成长 - 60) / 6.0).ToString(
                        CultureInfo.InvariantCulture);
                }
                else
                {

                    //2025.8.5注释  原代码：pet1.成长 = (Convert.ToDouble(pet1.成长) + 获得成长).ToString(CultureInfo.InvariantCulture);
                    
                    //M2025.8.5.1修改，防止成长溢出
                    doubl newGrowth = Convert.ToDouble(pet1.成长) + 获得成长;
                    pet1.etGrowthSafely(newGrowth);
                }
            }
            if (!固定神 && !随机神)
            {
                // 获取主宠的成长上限
                double growthLimit = GetPetGrowthLimit(pet1);

                if (Convert.ToDouble(pet1.成长) + 获得成长 >= growthLimit)
                {
                    pet1.成长 = growthLimit.ToString();
                }
                else
                {
                    pet1.成长 = (Convert.ToDouble(pet1.成长) + 获得成长).ToString(CultureInfo.InvariantCulture);
                }
            }
            pet1.防御 = ((long)(Convert.ToDouble(pet1.防御) * NumEncrypt.零点二() * (Convert.ToDouble(pet1.等级) - NumEncrypt.二十()) / NumEncrypt.五十())).ToString();//满级是100时就除以50,满级是110时就除以60，依此类推，当连续多次大于90级涅的时候初属性就会一直增长
            pet1.攻击 = ((long)(Convert.ToDouble(pet1.攻击) * NumEncrypt.零点二() * (Convert.ToDouble(pet1.等级) - NumEncrypt.二十()) / NumEncrypt.五十())).ToString();
            pet1.命中 = ((long)(Convert.ToDouble(pet1.命中) * NumEncrypt.零点二() * (Convert.ToDouble(pet1.等级) - NumEncrypt.二十()) / NumEncrypt.五十())).ToString();
            pet1.魔法 = ((long)(Convert.ToDouble(pet1.魔法) * NumEncrypt.零点二() * (Convert.ToDouble(pet1.等级) - NumEncrypt.二十()) / NumEncrypt.五十())).ToString();
            pet1.最大魔法 = ((long)(Convert.ToDouble(pet1.最大魔法) * NumEncrypt.零点二() * (Convert.ToDouble(pet1.等级) - NumEncrypt.二十()) / NumEncrypt.五十())).ToString();
            pet1.生命 = ((long)(Convert.ToDouble(pet1.生命) * NumEncrypt.零点二() * (Convert.ToDouble(pet1.等级) - NumEncrypt.二十()) / NumEncrypt.五十())).ToString();
            pet1.最大生命 = ((long)(Convert.ToDouble(pet1.最大生命) * NumEncrypt.零点二() * (Convert.ToDouble(pet1.等级) - NumEncrypt.二十()) / NumEncrypt.五十())).ToString();
            pet1.闪避 = ((long)(Convert.ToDouble(pet1.闪避) * NumEncrypt.零点二() * (Convert.ToDouble(pet1.等级) - NumEncrypt.二十()) / NumEncrypt.五十())).ToString();
            pet1.速度 = ((long)(Convert.ToDouble(pet1.速度) * NumEncrypt.零点二() * (Convert.ToDouble(pet1.等级) - NumEncrypt.二十()) / NumEncrypt.五十())).ToString();
            pet1.形象 = result.宠物序号;
            pet1.五行 = result.系别;
            pet1.宠物名字 = result.宠物名字;
            pet1.已进化次数 = "0";
            //宠物1.被抽取 = "0";
            pet1.当前经验 = "1";
            if (主宠放入)
            {
                user.主宠物 = pet1.宠物序号;
            }

            new DataProcess().SaveUserDataFile(user);
            if (!new DataProcess().AbandonPet(pet2.宠物序号))
            {
                if (new DataProcess().Update_APDF(pet1.宠物序号, pet1))
                {
                    DataProcess.GameForm.发送游戏公告(user.名字 + "实在是太幸运啦!他成功合成了出【" + pet1.宠物名字 + "】!");
                    if (Convert.ToInt16(user.vip) > 0)
                    {
                        DataProcess.GameForm.发送红色公告("由于您是尊贵的VIP" + user.vip + "玩家，您的宠物在本次合成中多获取了" +
                                                    (0.5 * Convert.ToInt16(user.vip)).ToString() + "%的成长。");
                    }
                    _lastHcTime = new DataProcess().GetSystemTS();
                    return "恭喜您!合成成功!获得宠物:" + pet1.宠物名字;
                }
                return "合成出错,请确定主宠存在!";
            }
            return "合成出错。";
        }*/
        /*internal static void RefreshTalent(PetInfo main, PropInfo htd)
        {
            if (string.IsNullOrEmpty(main.Talent) || )
        }*/
        internal static string Synthesis(PetInfo pet1, PetInfo pet2, string 道具1脚本, string 道具2脚本, out bool 是否删除)
        {
            //new AntiCheat().反作弊();
            是否删除 = false;
            if (_lastHcTime != 0)
            {
                if (new Tools.GetTime().GetSystemTS() - _lastHcTime < 5000)
                {
                    return "合成CD未到!";
                }
            }

            if (Convert.ToInt16(Enum.Parse(typeof(五行序号), pet1.五行)) > 5 ||
                Convert.ToInt16(Enum.Parse(typeof(五行序号), pet2.五行)) > 5)
            {
                return "合成失败,不能放入非五系宠!";
            }

            /*if (宠物1.被抽取 == "1" || 宠物2.被抽取 == "1")
            {

                return "所选宠物已被抽取过CC,无法再进行合成!";
            }*/
            if (Convert.ToInt32(pet1.等级) < NumEncrypt.四十())
            {
                return "主宠等级没有达到40级!";
            }

            if (Convert.ToInt32(pet2.等级) < NumEncrypt.四十())
            {
                return "副宠等级没有达到40级!";
            }

            bool 主宠放入 = false;
            UserInfo user = new DataProcess().ReadUserInfo();
            if (user.主宠物.Equals(pet1.宠物序号) || user.主宠物.Equals(pet2.宠物序号))
            {
                主宠放入 = true;
            }

            if (Convert.ToInt64(user.金币) < NumEncrypt.五万())
            {
                return "金币不足!合成一次需要50000金币噢!";
            }
            string formulaResult = new DataProcess().seekFormula(pet1, pet2);
            // new 数据处理().修改主宠(宠物1.宠物序号);
            user.主宠物 = pet1.宠物序号;
            bool 随机神宠 = false;
            new DataProcess().Undress_Pet(pet1.宠物序号);
            new DataProcess().Undress_Pet(pet2.宠物序号);
            ///以下为材料处理代码
            bool 失败不消失 = false;
            bool 肯定成功 = false;
            bool 变神 = false;
            bool 随机神 = false;
            bool 指定神 = false;
            double 效果加成 = 0.0;
            double 成功率加成 = 0.0;
            user.金币 = (Convert.ToInt64(user.金币) - NumEncrypt.五万()).ToString();
            new DataProcess().SaveUserDataFile(user);
            string[] 脚本组 = 道具1脚本.Split('|');
            string[] 小脚本组;
            foreach (string 脚本 in 脚本组)
            {
                if (脚本.Equals("守护"))
                {
                    失败不消失 = true;
                }

                

                if (脚本.Equals("成神"))
                {
                    变神 = true;
                }
                if (脚本.Equals("残缺守护"))
                {
                    //为了让道具不百分出神，然后有几率失败
                    失败不消失 = true;
                    肯定成功 = false;
                }
                if (脚本.Equals("完美守护"))
                {
                    失败不消失 = true;
                    肯定成功 = true;
                }

                if (脚本.Equals("随机神"))
                {
                    随机神 = true;
                }
                if (脚本.Equals("指定神"))
                {
                    指定神 = true;
                    随机神 = false;
                }
                

                else
                {
                    小脚本组 = 脚本.Split('*');
                    if (小脚本组.Length > 1)
                    {
                        if (小脚本组[0].Equals("成长加成"))
                        {
                            效果加成 += Convert.ToDouble(小脚本组[1]);
                        }

                        if (小脚本组[0].Equals("成功加成"))
                        {
                            成功率加成 += Convert.ToDouble(小脚本组[1]);
                        }
                    }
                }
            }

            脚本组 = 道具2脚本.Split('|');
            foreach (string 脚本 in 脚本组)
            {
                if (脚本.Equals("守护"))
                {
                    失败不消失 = true;
                }
                if (脚本.Equals("残缺守护"))
                {
                    失败不消失 = true;
                    肯定成功 = false;
                }
                if (脚本.Equals("完美守护"))
                {
                    失败不消失 = true;
                    肯定成功 = true;
                }

                if (脚本.Equals("成神"))
                {
                    变神 = true;
                }

                if (脚本.Equals("随机神"))
                {
                    随机神 = true;
                    指定神 = false;
                }
                if (脚本.Equals("指定神"))
                {
                    指定神 = true;
                    随机神 = false;
                }
                

                else
                {
                    小脚本组 = 脚本.Split('*');
                    if (小脚本组.Length > 1)
                    {
                        if (小脚本组[0].Equals("成长加成"))
                        {
                            效果加成 += Convert.ToDouble(小脚本组[1]);
                        }

                        if (小脚本组[0].Equals("成功加成"))
                        {
                            成功率加成 += Convert.ToDouble(小脚本组[1]);
                        }
                    }
                }
            }

            //以上是添加物品效果处理
            //以下是合成成功处理
            是否删除 = true;
            //肯定成功 = true;
            if (!肯定成功)
            {
                int 合成总数 = Convert.ToInt32(NumEncrypt.一百() * (1 - 成功率加成));
                long 合成 = DataProcess.RandomGenerator.Next(1, 合成总数 + 1);
                if (合成 >= NumEncrypt.五十())
                {
                    if (!失败不消失)
                    {
                        new DataProcess().AbandonPet(pet2.宠物序号);
                    }

                    _lastHcTime = new Tools.GetTime().GetSystemTS();
                    return "合成失败!";
                }
            }

            //合成成长计算          
            double 成长限制系数;
            if (Convert.ToDouble(pet1.成长) <= NumEncrypt.六十())
            {
                成长限制系数 = Convert.ToDouble(pet1.成长) * NumEncrypt.十() + NumEncrypt.四百();
            }
            else if (Convert.ToDouble(pet1.成长) <= NumEncrypt.八十())
            {
                成长限制系数 = (Convert.ToDouble(pet1.成长) - NumEncrypt.六十()) * NumEncrypt.三十五() + NumEncrypt.一千();
            }
            else
            {
                成长限制系数 = (Convert.ToDouble(pet1.成长) - NumEncrypt.六十()) * NumEncrypt.六十() + NumEncrypt.五百();
            }

            double 成长加成系数 = 0.0;
            if (Convert.ToDouble(pet1.成长) <= NumEncrypt.五())
            {
                成长加成系数 += NumEncrypt.零点八();
                if (Convert.ToDouble(pet2.成长) <= NumEncrypt.五())
                {
                    成长加成系数 += NumEncrypt.零点八();
                }
            }
            else if (Convert.ToDouble(pet1.成长) <= NumEncrypt.十())
            {
                成长加成系数 += NumEncrypt.零点四();
            }
            else if (Convert.ToDouble(pet1.成长) <= NumEncrypt.五十())
            {
                成长加成系数 += NumEncrypt.零点二();
            }

            var 获得成长 = Convert.ToDouble(pet1.成长) * (1 + (Convert.ToDouble(pet1.等级) - NumEncrypt.三十五()) / 成长限制系数) +
                       Convert.ToDouble(pet2.成长) * (NumEncrypt.零点二() +
                                                    NumEncrypt.零点零零五() * (Convert.ToDouble(pet2.等级) - NumEncrypt.三十五())
                       ) - Convert.ToDouble(pet1.成长);
            double 成长计算法a = (1 + 成长加成系数) * (1 + 效果加成);
            double 成长计算法b = 1 + 成长加成系数 + 效果加成;
            double hcBuff = 1 + 0.005 * Convert.ToInt16(user.vip);
            if(user.至尊VIP) hcBuff = 1 + 0.01 * Convert.ToInt16(user.vip);
            if (成长计算法a >= 成长计算法b)//VIP特权  合成加成
            {
                获得成长 = 获得成长 * 成长计算法b * hcBuff;
            }
            else if (成长计算法a < 成长计算法b)
            {
                获得成长 = 获得成长 * 成长计算法a * hcBuff;
            }
            
            //成长计算完毕

            获得成长 = 获得成长 * Effect;
            //合成神宠几率计算
            pet1 = PetCalc.CalcPetAttribute(pet1, false);
           
            //合成神宠几率计算
            if (!变神)
            {
                if (Convert.ToDouble(pet1.成长) > NumEncrypt.四十五())
                {
                    int 神宠几率;
                    if (Convert.ToDouble(pet1.成长) > NumEncrypt.一百八十五())
                    {
                        神宠几率 = NumEncrypt.三十五();
                    }
                    else
                    {
                        神宠几率 = (int) ((Convert.ToDouble(pet1.成长) - NumEncrypt.四十五()) * NumEncrypt.零点二五());
                    }

                    long 神宠骰子 = DataProcess.RandomGenerator.Next(1, NumEncrypt.一百零五());
                    if (神宠几率 >= 神宠骰子)
                    {
                        随机神宠 = true;
                    }
                }
            }
            else
            {
                随机神宠 = true;
            }
            //以下为合成新宠代码

            //new 数据处理().保存用户存档(用户);
            List<PetConfig> 宠物列表 = new List<PetConfig>();
            List<PetConfig> 列表2 = new List<PetConfig>();
            if (随机神宠)
            {
                宠物列表 = new DataProcess().GetSPList();
                列表2 = new DataProcess().GetASPList(DataProcess.GetCharacter("A32C6A19"));
            }
            else
            {
                宠物列表 = new DataProcess().GetOPList();
            }

            PetConfig 结果宠物 = new PetConfig();
            if (随机神宠 || 指定神)
            {
                if (!随机神 && !指定神)
                {
                    结果宠物 = new DataProcess().GetAppointedPetType("79");//不加百变
                }
                else
                {
                    if (指定神)
                    {
                        HCPet hpet = new HCPet();
                        if(hpet.ShowDialog() == DialogResult.OK)
                        {
                            PetConfig pet_ = new PetConfig();
                            if (hpet.value == "")
                            {
                                结果宠物 = new DataProcess().GetAppointedPetType("79");//不加百变
                            }
                            else
                            {
                                pet_ = JsonConvert.DeserializeObject<PetConfig>(SkRC4.DES.DecryptRC4(hpet.value, new DataProcess().GetKey(1)));
                                结果宠物 = pet_;
                                if (pet_ == null)
                                {
                                    结果宠物 = new DataProcess().GetAppointedPetType("79");//不加百变
                                }
                                if (!new DataProcess().GetSPList().Concat(new DataProcess().GetASPList(DataProcess.GetCharacter("A32C6A19"))).ToList().Exists(c => c.宠物序号 == pet_.宠物序号))//是否修改了宠物序号
                                {
                                    //修改序号
                                    结果宠物 = new DataProcess().GetAppointedPetType("79");//不加百变
                                }
                                if (SkRC4.DES.EncryptRC4(pet_.系别, new DataProcess().GetKey(1)) != "A32C" && SkRC4.DES.EncryptRC4(pet_.系别, new DataProcess().GetKey(1)) != "A32C6A19")
                                {
                                    //修改种族
                                    结果宠物 = new DataProcess().GetAppointedPetType("79");//不加百变
                                }
                                
                            }
                            
                        }
                        else
                        {
                            结果宠物 = new DataProcess().GetAppointedPetType("79");//不加百变
                        }
                    }
                    else
                    {
                        int 随机数 = DataProcess.RandomGenerator.Next(1, NumEncrypt.一百());
                        if (随机数 == 77)
                        {
                            //神圣
                            随机数 = DataProcess.RandomGenerator.Next(0, 列表2.Count);
                            结果宠物 = 列表2[随机数];
                        }
                        else
                        {
                            随机数 = DataProcess.RandomGenerator.Next(0, 宠物列表.Count);
                            结果宠物 = 宠物列表[随机数];
                        }
                    }
                    
                }
            }
            else
            {
                PetConfig 宠物类型1 = new DataProcess().GetAppointedPetType(pet1.形象);
                PetConfig 宠物类型2 = new DataProcess().GetAppointedPetType(pet2.形象);
                int 新宠物阶数 = Convert.ToInt32(宠物类型1.阶数);
                int 随机数 = DataProcess.RandomGenerator.Next(4, 9);
                if (随机数 < Convert.ToInt32(宠物类型2.阶数))
                {
                    新宠物阶数 = Convert.ToInt32(新宠物阶数) + 2;
                }
                else
                {
                    新宠物阶数 = Convert.ToInt32(新宠物阶数) + 1;
                }

                if (新宠物阶数 >= 10)
                {
                    新宠物阶数 = 10;
                }

                List<PetConfig> 同系宠物 = new List<PetConfig>();
                foreach (PetConfig 宠物 in 宠物列表)
                {
                    if (宠物.系别.Equals(pet1.五行) && 宠物.阶数.Equals(新宠物阶数.ToString()))
                    {
                        同系宠物.Add(宠物);
                    }
                }

                if (同系宠物.Count > 1)
                {
                    随机数 = DataProcess.RandomGenerator.Next(0, 同系宠物.Count);
                    结果宠物 = 同系宠物[随机数];
                }
                else
                {
                    结果宠物 = 同系宠物[0];
                }
            }
            if (formulaResult != null)
            {
                PetConfig petType = new DataProcess().GetAppointedPetType(formulaResult);
                结果宠物 = petType;
                if (Convert.ToInt16(Enum.Parse(typeof(五行序号), petType.系别)) > 5)
                {
                    随机神宠 = true;
                }
                else
                {
                    随机神宠 = false;
                }
                
            }
            //测试
            //DataProcess.GameForm.发送游戏公告("合成宠物【" + 结果宠物.宠物名字 + "】!");
            //return "合成结果：" + 结果宠物.宠物名字;
            if (随机神宠)
            {
                // 获取主宠的成长上限
                double growthLimit = GetPetGrowthLimit(pet1);

                if (Convert.ToDouble(pet1.成长) + 获得成长 >= growthLimit)
                {
                    pet1.成长 = (growthLimit / 3).ToString(); // 神宠特殊处理，成长上限的1/3
                }
                else if (Convert.ToDouble(pet1.成长) + 获得成长 >= 60)
                {
                    pet1.成长 = (NumEncrypt.六十() + (Convert.ToDouble(pet1.成长) + 获得成长 - NumEncrypt.六十()) / NumEncrypt.六())
                        .ToString(CultureInfo.InvariantCulture);

                    //heguangpin 2025.8.5.1修改，防止成长溢出
                    pet1.成长 = pet1.SetGrowthSafely(Convert.ToDouble(pet1.成长));
                }
                else
                {
                    pet1.成长 = (Convert.ToDouble(pet1.成长) + 获得成长).ToString(CultureInfo.InvariantCulture);
                    
                    //heguangpin 2025.8.5.1修改，防止成长溢出
                    pet1.成长 = pet1.SetGrowthSafely(Convert.ToDouble(pet1.成长));
                   
                }
            }
            else
            {
                // 获取主宠的成长上限
                double growthLimit = GetPetGrowthLimit(pet1);

                if (Convert.ToDouble(pet1.成长) + 获得成长 >= growthLimit)
                {
                    pet1.成长 = growthLimit.ToString();
                }
                else
                {
                    pet1.成长 = (Convert.ToDouble(pet1.成长) + 获得成长).ToString(CultureInfo.InvariantCulture);
                }
            }

            pet1.防御 = ((long) (Convert.ToDouble(pet1.防御) * NumEncrypt.零点二() *
                               (Convert.ToDouble(pet1.等级) - NumEncrypt.二十()) / NumEncrypt.五十()))
                .ToString(); //满级是100时就除以50,满级是110时就除以60，依此类推，当连续多次大于90级涅的时候初属性就会一直增长
            pet1.攻击 = ((long) (Convert.ToDouble(pet1.攻击) * NumEncrypt.零点二() *
                               (Convert.ToDouble(pet1.等级) - NumEncrypt.二十()) / NumEncrypt.五十())).ToString();
            pet1.命中 = ((long) (Convert.ToDouble(pet1.命中) * NumEncrypt.零点二() *
                               (Convert.ToDouble(pet1.等级) - NumEncrypt.二十()) / NumEncrypt.五十())).ToString();
            pet1.魔法 = ((long) (Convert.ToDouble(pet1.魔法) * NumEncrypt.零点二() *
                               (Convert.ToDouble(pet1.等级) - NumEncrypt.二十()) / NumEncrypt.五十())).ToString();
            pet1.最大魔法 = ((long) (Convert.ToDouble(pet1.最大魔法) * NumEncrypt.零点二() *
                                 (Convert.ToDouble(pet1.等级) - NumEncrypt.二十()) / NumEncrypt.五十())).ToString();
            pet1.生命 = ((long) (Convert.ToDouble(pet1.生命) * NumEncrypt.零点二() *
                               (Convert.ToDouble(pet1.等级) - NumEncrypt.二十()) / NumEncrypt.五十())).ToString();
            pet1.最大生命 = ((long) (Convert.ToDouble(pet1.最大生命) * NumEncrypt.零点二() *
                                 (Convert.ToDouble(pet1.等级) - NumEncrypt.二十()) / NumEncrypt.五十())).ToString();
            pet1.闪避 = ((long) (Convert.ToDouble(pet1.闪避) * NumEncrypt.零点二() *
                               (Convert.ToDouble(pet1.等级) - NumEncrypt.二十()) / NumEncrypt.五十())).ToString();
            pet1.速度 = ((long) (Convert.ToDouble(pet1.速度) * NumEncrypt.零点二() *
                               (Convert.ToDouble(pet1.等级) - NumEncrypt.二十()) / NumEncrypt.五十())).ToString();
            pet1.形象 = 结果宠物.宠物序号;
            pet1.五行 = 结果宠物.系别;
            pet1.宠物名字 = 结果宠物.宠物名字;
            pet1.已进化次数 = "0";
            //宠物1.被抽取 = "0";
            pet1.当前经验 = "1";
            if (主宠放入)
            {
                user.主宠物 = pet1.宠物序号;
            }

            new DataProcess().SaveUserDataFile(user);
            
            if (new DataProcess().Update_APDF(pet1.宠物序号, pet1))
            {
                if (formulaResult != null)
                {
                 //   DataProcess.GameForm.SendData("gg|玩家" + DZ.Name + "成功利用公式合成出了【" + pet1.宠物名字 + "】!");
                }
                DataProcess.GameForm.发送游戏公告(user.名字 + "实在是太幸运啦!他成功合成了出【" + pet1.宠物名字 + "】!");
                if (user.至尊VIP)
                {
                    string vip = "至尊VIP";
                    if (user.星辰VIP) vip = "星辰VIP";
                    DataProcess.GameForm.发送红色公告($"由于您是尊贵的{vip}玩家，您的宠物在本次合成中多获取了" +
                                                1 * Convert.ToInt16(user.vip) + "%的成长。");
                }
                if (Convert.ToInt16(user.vip) > 0 && !user.至尊VIP)//VIP特权 合成加成公告
                {
                    DataProcess.GameForm.发送红色公告("由于您是尊贵的VIP" + user.vip + "玩家，您的宠物在本次合成中多获取了" +
                                                0.5 * Convert.ToInt16(user.vip) + "%的成长。");
                }

                new DataProcess().AbandonPet(pet2.宠物序号);
                _lastHcTime = new Tools.GetTime().GetSystemTS();

                return "恭喜您!合成成功!获得宠物:" + pet1.宠物名字;
            }    

            return "合成出错。";
        }

        internal static PetInfo AutoNirvana(string mainxx, int mainlv, string vice, int vicecc, int vicelv, double buff,
            string prop1, string prop2, string jhprop)//玩家助手自动涅槃
        {
            UserInfo user = new DataProcess().ReadUserInfo();
            PetInfo pet1 = new DataProcess().ReadAppointedPet(mainxx);
            new DataProcess().Undress_Pet(pet1.宠物序号);
            pet1.当前经验 = new DataProcess().GetSumExp(mainlv - 1);
            new DataProcess().Update_APDF(pet1.宠物序号, pet1);
            PetInfo pet = new DataProcess().ReadAppointedPet(mainxx);
            PropInfo p1 = new DataProcess().GetAP_ID(prop1);
            {
                new DataProcess().ReviseOrDeletePP(p1, 1);
            }
            if (!string.IsNullOrEmpty(prop2))
            {
                PropInfo p2 = new DataProcess().GetAP_ID(prop2);
                {
                    new DataProcess().ReviseOrDeletePP(p2, 1);
                }
            }

            string jhcs = "0";
            if (!string.IsNullOrEmpty(jhprop))
            {
                PropInfo jh = new DataProcess().GetAP_ID(jhprop);
                new DataProcess().ReviseOrDeletePP(jh, 10);
                jhcs = "10";    
            }
            PropInfo nps = new DataProcess().GetAP_ID("20");
            {
                new DataProcess().ReviseOrDeletePP(nps, 1);
            }
            PropInfo fc = new DataProcess().GetAP_ID(vice);
            {
                new DataProcess().ReviseOrDeletePP(fc, 1);
            }
            new DataProcess().Undress_Pet(pet.宠物序号);
            double 成长限制系数;
            double 成长加成系数;
            if (Convert.ToDouble(pet.成长) > 10)
            {
                成长限制系数 = (NumEncrypt.二十() - Math.Log10(Convert.ToDouble(pet.成长))) * NumEncrypt.五() *
                         Convert.ToDouble(pet.成长);
            }
            else
            {
                成长限制系数 = NumEncrypt.二百();
            }

            if (Convert.ToDouble(pet.成长) < NumEncrypt.七十())
            {
                成长加成系数 = NumEncrypt.零点三();
                if (Convert.ToDouble(vicecc) < NumEncrypt.七十())
                {
                    成长加成系数 += NumEncrypt.零点二();
                }
                else
                {
                    成长加成系数 += NumEncrypt.零点一();
                }
            }
            else if (Convert.ToDouble(pet.成长) < NumEncrypt.九十())
            {
                成长加成系数 = NumEncrypt.零点三() - (Convert.ToDouble(pet.成长) - NumEncrypt.七十()) * NumEncrypt.零点零一();
            }
            else if (Convert.ToDouble(pet.成长) < 10000)
            {
                成长加成系数 = NumEncrypt.零点一();
            }
            else
            {
                成长加成系数 = 0;
            }

            var 获得成长 =
                Convert.ToDouble(pet.成长) *
                (1 + (Convert.ToDouble(mainlv) - NumEncrypt.五十()) / 成长限制系数 * NumEncrypt.零点零二五()) +
                Convert.ToDouble(vicecc) *
                (NumEncrypt.零点一() + NumEncrypt.零点零零一() * (Convert.ToDouble(vicelv) - NumEncrypt.五十())) -
                Convert.ToDouble(pet.成长);
            double 成长计算法a = (1 + 成长加成系数) * (1 + buff);
            double 成长计算法b = 1 + 成长加成系数 + buff;
            double npBuff = 1 + 0.0025 * Convert.ToInt16(user.vip);
            if(user.至尊VIP) npBuff = 1 + 0.005 * Convert.ToInt16(user.vip);
            if (成长计算法a >= 成长计算法b)//VIP特权 涅槃加成[玩家助手]
            {
                获得成长 = 获得成长 * 成长计算法b * npBuff;
            }
            else if (成长计算法a < 成长计算法b)
            {
                获得成长 = 获得成长 * 成长计算法a * npBuff;
            }

            if (pet.五行.Equals("神圣"))
            {
                获得成长 = 获得成长 * NumEncrypt.零点九();
            }

            if (pet.五行.Equals("佛") || pet.五行.Equals("聖"))
            {
                获得成长 = 获得成长 * NumEncrypt.零点九();
            }
            if (pet1.五行 == "次元")//助手次元涅槃加成
            {
                获得成长 = 获得成长 * (NumEncrypt.零点零三() + NumEncrypt.零点九());
            }
            PlayerHelper.平均CC_涅槃 += 获得成长;
            var 进化成长 = 0;
            if (!string.IsNullOrEmpty(jhprop))
            {
                if (jhprop == "2016110545" || jhprop == "2016110512")
                {
                    进化成长 = DataProcess.RandomGenerator.Next(1, 4);
                    获得成长 += 进化成长;
                }
                else if (jhprop == "2016110546" || jhprop == "2016110513")
                {
                    进化成长 = DataProcess.RandomGenerator.Next(3, 7);
                    获得成长 += 进化成长;
                }
            }
            PlayerHelper.平均CC_进化 += 进化成长;
            pet = PetCalc.CalcPetAttribute(pet, false);
            pet.成长 = (Convert.ToDouble(pet.成长) + 获得成长).ToString(CultureInfo.InvariantCulture);
            pet.防御 = ((long) (Convert.ToDouble(pet.防御) * NumEncrypt.零点二五五() *
                              (Convert.ToDouble(mainlv) - NumEncrypt.四十()) / NumEncrypt.五十()))
                .ToString(); //满级是100时就除以50,满级是110时就除以60，依此类推，当连续多次大于90级涅的时候初属性就会一直增长
            pet.攻击 = ((long) (Convert.ToDouble(pet.攻击) * NumEncrypt.零点二五五() *
                              (Convert.ToDouble(mainlv) - NumEncrypt.四十()) / NumEncrypt.五十())).ToString();
            pet.命中 = ((long) (Convert.ToDouble(pet.命中) * NumEncrypt.零点二五五() *
                              (Convert.ToDouble(mainlv) - NumEncrypt.四十()) / NumEncrypt.五十())).ToString();
            pet.魔法 = ((long) (Convert.ToDouble(pet.魔法) * NumEncrypt.零点二五五() *
                              (Convert.ToDouble(mainlv) - NumEncrypt.四十()) / NumEncrypt.五十())).ToString();
            pet.最大魔法 = ((long) (Convert.ToDouble(pet.最大魔法) * NumEncrypt.零点二五五() *
                                (Convert.ToDouble(mainlv) - NumEncrypt.四十()) / NumEncrypt.五十())).ToString();
            pet.生命 = ((long) (Convert.ToDouble(pet.生命) * NumEncrypt.零点二五五() *
                              (Convert.ToDouble(mainlv) - NumEncrypt.四十()) / NumEncrypt.五十())).ToString();
            pet.最大生命 = ((long) (Convert.ToDouble(pet.最大生命) * NumEncrypt.零点二五五() *
                                (Convert.ToDouble(mainlv) - NumEncrypt.四十()) / NumEncrypt.五十())).ToString();
            pet.闪避 = ((long) (Convert.ToDouble(pet.闪避) * NumEncrypt.零点二五五() *
                              (Convert.ToDouble(mainlv) - NumEncrypt.四十()) / NumEncrypt.五十())).ToString();
            pet.速度 = ((long) (Convert.ToDouble(pet.速度) * NumEncrypt.零点二五五() *
                              (Convert.ToDouble(mainlv) - NumEncrypt.四十()) / NumEncrypt.五十())).ToString();
            if (Convert.ToInt64(pet.防御) <0)
            {
                pet.防御 = long.MaxValue.ToString();
            }

            if (Convert.ToInt64(pet.攻击) <0)
            {
                pet.攻击 = long.MaxValue.ToString();
            }

            if (Convert.ToInt64(pet.命中) <0)
            {
                pet.命中 = long.MaxValue.ToString();
            }

            if (Convert.ToInt64(pet.魔法) <0)
            {
                pet.魔法 = long.MaxValue.ToString();
            }

            if (Convert.ToInt64(pet.最大魔法) <0)
            {
                pet.最大魔法 = long.MaxValue.ToString();
            }

            if (Convert.ToInt64(pet.生命) <0)
            {
                pet.生命 = long.MaxValue.ToString();
            }

            if (Convert.ToInt64(pet.最大生命) <0)
            {
                pet.最大生命 = long.MaxValue.ToString();
            }

            if (Convert.ToInt64(pet.闪避) <0)
            {
                pet.闪避 = long.MaxValue.ToString();
            }

            if (Convert.ToInt64(pet.速度) <0)
            {
                pet.速度 = long.MaxValue.ToString();
            }

            pet.当前经验 = "1";
            pet.已进化次数 = jhcs;
            
            new DataProcess().Update_APDF(pet.宠物序号, pet);
        
            PlayerHelper.addCC(获得成长,pet.成长);
            return pet;
        }


        //分割线---
        internal static string Nirvana(PetInfo pet1, PetInfo pet2, string script1, string script2, out bool 是否删除,
            PetInfo 涅槃兽)//涅槃
        {   
            UserInfo user = new DataProcess().ReadUserInfo();

            if (涅槃兽.宠物名字.IndexOf("涅槃重生", StringComparison.Ordinal) != -1)
            {
                是否删除 = false;

                if (Convert.ToInt64(user.金币) < 2000000000)
                {
                    return "变脸需要20e金币!";
                }
                if (Convert.ToInt32(pet1.等级) < 130)
                {
                    return "主宠等级没有达到130级!";
                }

                if (Convert.ToInt32(pet2.等级) < 130)
                {
                    return "副宠等级没有达到130级!";
                }

                if (Convert.ToInt32(涅槃兽.等级) < 120)
                {
                    return "变脸用涅槃兽等级没有达到120级!";
                }

                是否删除 = true;

                
                if (script1.Equals("转移非天赋技能") || script2.Equals("转移非天赋技能"))
                {

                    string r = 变脸(pet1, pet2, 涅槃兽, true);
                    if (r == "变脸失败,玩家取消了变脸操作!" || r == "变脸失败,玩家取消了变脸操作!"||r== "请放弃一部分技能后再执行这个操作！变脸后技能超过上限！")
                    {
                        是否删除 = false;
                    }
                    return r;
                }
                else
                {   
                    是否删除 = false;
                    return 变脸(pet1, pet2, 涅槃兽,false);
                }           
            }

            //new AntiCheat().反作弊();
            if (pet1.五行 == "巫") {

                if (pet2.五行 != "巫") {
                    是否删除 = false;
                    return "巫系宠物只能和巫系宠物涅槃！";
                }
                //不需要特定的涅槃兽
                //if (!涅槃兽.宠物名字.Equals("巫·涅槃兽")) {
                //    是否删除 = false;
                //    return "巫系宠物只能使用特定的涅槃兽！";
                //}
            }
            是否删除 = false;
            if (_lastNpTime != 0)
            {
                if (new Tools.GetTime().GetSystemTS() - _lastNpTime < 10000)
                {
                    return "涅槃CD未到!";
                }
            }

            if (pet1.宠物名字.Contains("涅槃兽") || pet2.宠物名字.Contains("涅槃兽") || pet1.形象=="577" || pet1.形象 == "578" || pet1.形象 == "581")
            {
                return "当前主宠或副宠无法参与涅槃!";
            }

            if (pet1.形象 == "610")
            {
                if (pet2.形象 != "609")
                {
                    return "当前主宠只能和亓玥·宝宝转生!";
                }
            }

            if (pet1.形象 == "609")
            {
                return "当前主宠只能作为副宠!";
            }

            if (!涅槃兽.宠物名字.Contains("涅槃兽"))
            {
                return "请放入涅槃兽!";
            }

            if (Convert.ToInt16(Enum.Parse(typeof(五行序号), pet1.五行)) <= 5 ||
                Convert.ToInt16(Enum.Parse(typeof(五行序号), pet2.五行)) <= 5)
            {
                return "不能放入普通宠物!";
            }
            /*if (宠物1.五行.Equals("神圣") || 宠物2.五行.Equals("神圣"))
            {
                return "神圣宠物无法涅槃!";
            }*/
            /*if (宠物1.被抽取 == "1" || 宠物2.被抽取 == "1" || 涅槃兽.被抽取 == "1")
            {
                return "所选宠物已被抽取过CC,无法再进行涅槃!";
            }*/
            if(pet1.形象=="609")//涅槃主宠限制
            {
                return "该宠物无法作为主宠涅槃!";
            }


            if (Convert.ToInt32(pet1.等级) < 60)
            {
                return "主宠等级没有达到60级!";
            }

            if (Convert.ToInt32(pet2.等级) < 60)
            {
                return "副宠等级没有达到60级!";
            }

            if (Convert.ToInt32(涅槃兽.等级) < 60)
            {
                return "涅槃兽等级没有达到60级!";
            }

            bool 主宠放入 = user.主宠物.Equals(pet1.宠物序号) || user.主宠物.Equals(pet2.宠物序号) || user.主宠物.Equals(涅槃兽.宠物序号);

            if (Convert.ToInt64(user.金币) < NumEncrypt.五十万())
            {
                return "金币不足!涅槃一次需要500000金币噢!";
            }
            String formulaResult = new DataProcess().seekFormula(pet1, pet2);
            new DataProcess().ChangeMainPet(pet1.宠物序号);
            //bool 随机神宠 = false;
            new DataProcess().Undress_Pet(pet1.宠物序号);
            new DataProcess().Undress_Pet(pet2.宠物序号);
            new DataProcess().Undress_Pet(涅槃兽.宠物序号);
            ///以下为材料处理代码
            ///添加物的效果分为守护效果，必定成功，成长加成，成功率加成
            user.金币 = (Convert.ToInt64(user.金币) - NumEncrypt.五十万()).ToString();
            new DataProcess().SaveUserDataFile(user);
            bool 失败不消失 = false;
            bool 肯定成功 = false;
            double 效果加成 = 0.0;
            double 成功率加成 = 0.0;
            string[] 脚本组 = script1.Split('|');
            string[] 小脚本组;
            foreach (string 脚本 in 脚本组)
            {
                if (脚本.Equals("守护"))
                {
                    失败不消失 = true;
                }
                if (脚本.Equals("巫族守护"))
                {
                    是否删除 = true;
                    失败不消失 = true;
                    肯定成功 = true;
                }
                if (脚本.Equals("完美守护"))
                {
                    失败不消失 = true;
                    肯定成功 = true;
                }
                else
                {
                    小脚本组 = 脚本.Split('*');
                    if (小脚本组.Length > 1)
                    {
                        if (小脚本组[0].Equals("成长加成"))
                        {
                            效果加成 += Convert.ToDouble(小脚本组[1]);
                        }

                        if (小脚本组[0].Equals("成功加成"))
                        {
                            成功率加成 += Convert.ToDouble(小脚本组[1]);
                        }
                    }
                }
            }

            脚本组 = script2.Split('|');
            foreach (string 脚本 in 脚本组)
            {
                if (脚本.Equals("守护"))
                {
                    失败不消失 = true;
                }
                if (脚本.Equals("巫族守护"))
                {
                    是否删除 = true;
                    失败不消失 = true;
                    肯定成功 = true;
                }
                if (脚本.Equals("完美守护"))
                {
                    失败不消失 = true;
                    肯定成功 = true;
                }
                else
                {
                    小脚本组 = 脚本.Split('*');
                    if (小脚本组.Length > 1)
                    {
                        if (小脚本组[0].Equals("成长加成"))
                        {
                            效果加成 += Convert.ToDouble(小脚本组[1]);
                        }

                        if (小脚本组[0].Equals("成功加成"))
                        {
                            成功率加成 += Convert.ToDouble(小脚本组[1]);
                        }
                    }
                }
            }

            //以上是添加物品效果整理
            //以下是涅槃成功与否处理
            if (!肯定成功)
            {
                long 合成 = DataProcess.RandomGenerator.Next(1, Convert.ToInt32(NumEncrypt.一百() * (1 - 成功率加成)));
                if (合成 >= NumEncrypt.三十())
                {
                    if (!失败不消失)
                    {
                        new DataProcess().AbandonPet(pet2.宠物序号);
                    }

                    _lastNpTime = new Tools.GetTime().GetSystemTS();
                    return "涅槃失败!";
                }
            }

            //计算成长，以主宠成长为基础成长，主宠等级的加成为（主宠等级-60）/240
            //副宠等级的加成，60级时为5%，此后每增长一级加0.12%
            //涅槃后成长=主宠成长*主宠等级加成+副宠成长*副宠等级加成
            是否删除 = true;
            double 成长限制系数;
            double 成长加成系数;
            if (Convert.ToDouble(pet1.成长) > 10)
            {
                成长限制系数 = (NumEncrypt.二十() - Math.Log10(Convert.ToDouble(pet1.成长))) * NumEncrypt.五() *
                         Convert.ToDouble(pet1.成长);
            }
            else
            {
                成长限制系数 = NumEncrypt.二百();
            }

            if (Convert.ToDouble(pet1.成长) < NumEncrypt.七十())
            {
                成长加成系数 = NumEncrypt.零点三();
                if (Convert.ToDouble(pet2.成长) < NumEncrypt.七十())
                {
                    成长加成系数 += NumEncrypt.零点二();
                }
                else
                {
                    成长加成系数 += NumEncrypt.零点一();
                }
            }
            else if (Convert.ToDouble(pet1.成长) < NumEncrypt.九十())
            {
                成长加成系数 = NumEncrypt.零点三() - (Convert.ToDouble(pet1.成长) - NumEncrypt.七十()) * NumEncrypt.零点零一();
            }
            else if (Convert.ToDouble(pet1.成长) < 10000)
            {
                成长加成系数 = NumEncrypt.零点一();
            }
            else
            {
                成长加成系数 = 0;
            }

            var 获得成长 =
                Convert.ToDouble(pet1.成长) *
                (1 + (Convert.ToDouble(pet1.等级) - NumEncrypt.五十()) / 成长限制系数 * NumEncrypt.零点零二五()) +
                Convert.ToDouble(pet2.成长) *
                (NumEncrypt.零点一() + NumEncrypt.零点零零一() * (Convert.ToDouble(pet2.等级) - NumEncrypt.五十())) -
                Convert.ToDouble(pet1.成长);
            double 成长计算法a = (1 + 成长加成系数) * (1 + 效果加成);
            double 成长计算法b = 1 + 成长加成系数 + 效果加成;
            double npBuff = 1 + 0.0025 * Convert.ToInt16(user.vip);
            if (user.至尊VIP) npBuff = 1 + 0.005 * Convert.ToInt16(user.vip);
            if (成长计算法a >= 成长计算法b)//VIP特权 涅槃加成  
            {
                获得成长 = 获得成长 * 成长计算法b * npBuff;
            }
            else if (成长计算法a < 成长计算法b)
            {
                获得成长 = 获得成长 * 成长计算法a * npBuff;
            }
            if (pet1.五行.Equals("神"))//五行神
            {
                获得成长 = 获得成长 * NumEncrypt.零点九();
            }

            if (pet1.五行.Equals("神圣"))
            {
                获得成长 = 获得成长 * NumEncrypt.零点九();
            }
            
            if (pet1.五行.Equals("佛") || pet1.五行.Equals("聖"))
            {
                获得成长 = 获得成长 * NumEncrypt.零点九();
            }
            if (pet1.五行.Equals("次元"))//次元涅槃加成
            {
                获得成长 = 获得成长 * (NumEncrypt.零点零三() + NumEncrypt.零点九());
            }
            获得成长 = 获得成长 * Effect;
            pet1 = PetCalc.CalcPetAttribute(pet1, false);
            pet1.成长 = (Convert.ToDouble(pet1.成长) + 获得成长).ToString(CultureInfo.InvariantCulture);

            //heguangpin 2015-8-5 涅槃后成长上限
            pet1.成长 = pet1.SetGrowthSafely(Convert.ToDouble(pet1.成长));

            pet1.防御 = ((long) (Convert.ToDouble(pet1.防御) * NumEncrypt.零点二五五() *
                               (Convert.ToDouble(pet1.等级) - NumEncrypt.四十()) / NumEncrypt.五十()))
                .ToString(); //满级是100时就除以50,满级是110时就除以60，依此类推，当连续多次大于90级涅的时候初属性就会一直增长
            pet1.攻击 = ((long) (Convert.ToDouble(pet1.攻击) * NumEncrypt.零点二五五() *
                               (Convert.ToDouble(pet1.等级) - NumEncrypt.四十()) / NumEncrypt.五十())).ToString();
            pet1.命中 = ((long) (Convert.ToDouble(pet1.命中) * NumEncrypt.零点二五五() *
                               (Convert.ToDouble(pet1.等级) - NumEncrypt.四十()) / NumEncrypt.五十())).ToString();
            pet1.魔法 = ((long) (Convert.ToDouble(pet1.魔法) * NumEncrypt.零点二五五() *
                               (Convert.ToDouble(pet1.等级) - NumEncrypt.四十()) / NumEncrypt.五十())).ToString();
            pet1.最大魔法 = ((long) (Convert.ToDouble(pet1.最大魔法) * NumEncrypt.零点二五五() *
                                 (Convert.ToDouble(pet1.等级) - NumEncrypt.四十()) / NumEncrypt.五十())).ToString();
            pet1.生命 = ((long) (Convert.ToDouble(pet1.生命) * NumEncrypt.零点二五五() *
                               (Convert.ToDouble(pet1.等级) - NumEncrypt.四十()) / NumEncrypt.五十())).ToString();
            pet1.最大生命 = ((long) (Convert.ToDouble(pet1.最大生命) * NumEncrypt.零点二五五() *
                                 (Convert.ToDouble(pet1.等级) - NumEncrypt.四十()) / NumEncrypt.五十())).ToString();
            pet1.闪避 = ((long) (Convert.ToDouble(pet1.闪避) * NumEncrypt.零点二五五() *
                               (Convert.ToDouble(pet1.等级) - NumEncrypt.四十()) / NumEncrypt.五十())).ToString();
            pet1.速度 = ((long) (Convert.ToDouble(pet1.速度) * NumEncrypt.零点二五五() *
                               (Convert.ToDouble(pet1.等级) - NumEncrypt.四十()) / NumEncrypt.五十())).ToString();
            if (Convert.ToInt64(pet1.防御) <0)
            {
                pet1.防御 = long.MaxValue.ToString();
            }

            if (Convert.ToInt64(pet1.攻击) <0)
            {
                pet1.攻击 = long.MaxValue.ToString();
            }

            if (Convert.ToInt64(pet1.命中) <0)
            {
                pet1.命中 = long.MaxValue.ToString();
            }

            if (Convert.ToInt64(pet1.魔法) <0)
            {
                pet1.魔法 = long.MaxValue.ToString();
            }

            if (Convert.ToInt64(pet1.最大魔法) <0)
            {
                pet1.最大魔法 = long.MaxValue.ToString();
            }

            if (Convert.ToInt64(pet1.生命) <0)
            {
                pet1.生命 = long.MaxValue.ToString();
            }

            if (Convert.ToInt64(pet1.最大生命) <0)
            {
                pet1.最大生命 = long.MaxValue.ToString();
            }

            if (Convert.ToInt64(pet1.闪避) <0)
            {
                pet1.闪避 = long.MaxValue.ToString();
            }

            if (Convert.ToInt64(pet1.速度) <0)
            {
                pet1.速度 = long.MaxValue.ToString();
            }

            pet1.当前经验 = "1";
            pet1.已进化次数 = "0";
            //宠物1.被抽取 = "0";
            if (主宠放入)
            {
                user.主宠物 = pet1.宠物序号;
            }
            if (formulaResult != null)
            {
                pet1.形象 = formulaResult;
            }
            new DataProcess().SaveUserDataFile(user);
            if (new DataProcess().Update_APDF(pet1.宠物序号, pet1))
            {
                if (formulaResult != null)
                {
                    DataProcess.GameForm.发送游戏公告(user.名字 + "成功使用公式将宠物转生为【" + pet1.宠物名字 + "】!");
                }
                else
                {
                    DataProcess.GameForm.发送游戏公告(user.名字 + "的宠物转生成了【" + pet1.宠物名字 + "】!");
                }
                if (user.至尊VIP)
                {
                    string vip = "至尊VIP";
                    if (user.星辰VIP) vip = "星辰VIP";
                    DataProcess.GameForm.发送红色公告($"由于您是尊贵的{vip}玩家，您的宠物在本次涅槃中多获取了" +
                                                0.5 * Convert.ToInt16(user.vip) + "%的成长。");
                }
                if (Convert.ToInt16(user.vip) > 0 && !user.至尊VIP)//VIP特权 涅槃加成公告
                {
                    DataProcess.GameForm.发送红色公告("由于您是尊贵的VIP" + user.vip + "玩家，您的宠物在本次涅槃中多获取了" +
                                                0.25 * Convert.ToInt16(user.vip) + "%的成长。");
                }

                new DataProcess().AbandonPet(pet2.宠物序号);
                new DataProcess().AbandonPet(涅槃兽.宠物序号);
                _lastNpTime = new Tools.GetTime().GetSystemTS();
                return "恭喜您!涅槃成功!获得宠物:" + pet1.宠物名字;
            }

            return "涅槃出错,请确定主宠存在!";
        }

        public static string Evolution(string 宠物序号, string ew)
        {
            //new AntiCheat().反作弊();
            UserInfo user = new DataProcess().ReadUserInfo();
            if (string.IsNullOrEmpty(宠物序号))
            {
                宠物序号 = user.主宠物;
            }

            PetInfo 信息 = new DataProcess().ReadAppointedPet(宠物序号);
            EvolutionWay 进化 = new DataProcess().GetAppointedEW(信息.形象);
            //System.Windows.Forms.MessageBox.Show(信息.已进化次数);
            if (Convert.ToDouble(user.金币) < 1000)
            {
                return "金币不足！";
            }

            if (Convert.ToDouble(信息.已进化次数) >= 10)
            {
                return "宠物已进化十次！无法继续进化";
            }

            user.金币 = (Convert.ToDouble(user.金币) - 1000).ToString(CultureInfo.InvariantCulture);
            double 增加成长;
            if (ew.Equals("1"))
            {
                if (!new DataProcess().OwnOrNot_PropID(进化.AP))
                {
                    return "进化道具不足！";
                }

                if (Convert.ToDouble(信息.等级) < Convert.ToDouble(进化.ALV))
                {
                    return "进化等级不足！";
                }

                if (Convert.ToInt16(Enum.Parse(typeof(五行序号), 信息.五行)) > 5)
                {
                    增加成长 = DataProcess.RandomGenerator.Next(1, 4) / 10.0 * Effect;
                }
                else
                {
                    增加成长 = DataProcess.RandomGenerator.Next(1, 6) / 10.0 * Effect;
                }

                信息.成长 = (Convert.ToDouble(信息.成长) + 增加成长).ToString(CultureInfo.InvariantCulture);
               
                //heguangpin 2025.8.5.1修改，防止成长溢出
                信息.成长 =信息.SetGrowthSafely(Convert.ToDouble(信息.成长));
                

                信息.已进化次数 = (Convert.ToInt32(信息.已进化次数) + 1).ToString();
                PropInfo 道具 = new DataProcess().GetAP_ID(进化.AP);

                new DataProcess().ReviseOrDeletePP(道具, 1);

                信息.形象 = 进化.AI;
                
            }
            else
            {
                if (!new DataProcess().OwnOrNot_PropID(进化.BP))
                {
                    return "进化道具不足！";
                }

                if (Convert.ToDouble(信息.等级) < Convert.ToDouble(进化.BLV))
                {
                    return "进化等级不足！";
                }

                if (Convert.ToInt16(Enum.Parse(typeof(五行序号), 信息.五行)) > 5)
                {
                    增加成长 = DataProcess.RandomGenerator.Next(3, 7) / 10.0 * Effect;
                }
                else
                {
                    增加成长 = DataProcess.RandomGenerator.Next(5, 11) / 10.0 * Effect;
                }

                信息.成长 = (Convert.ToDouble(信息.成长) + 增加成长).ToString(CultureInfo.InvariantCulture);
                信息.已进化次数 = (Convert.ToInt32(信息.已进化次数) + 1).ToString();
                PropInfo 道具 = new DataProcess().GetAP_ID(进化.BP);
                
                new DataProcess().ReviseOrDeletePP(道具, 1);

                信息.形象 = 进化.BI;
               
            }
            new DataProcess().Update_APDF(信息.宠物序号, 信息);
            new DataProcess().SaveUserDataFile(user);
            new DataProcess().ChangeMainPet(宠物序号);
            return "ss";
        }

        public static string 变脸(PetInfo pet1, PetInfo pet2, PetInfo 变脸宠, bool bljd = false)
        {
            //413 60%
            //414 75%
            //415 95%
            //new AntiCheat().反作弊();
            if (bljd)
            {
                var re = GetNewSkillCfg(pet1, pet2);
                if (re == "skillMax")
                {
                    return "请放弃一部分技能后再执行这个操作！变脸后技能超过上限！";
                }
               
            }            
            DialogResult dr = MessageBox.Show(Res.RM.GetString("变脸提示"), Res.RM.GetString("提示"),
                MessageBoxButtons.OKCancel);
            if (dr == DialogResult.Cancel)
            {
                return "变脸失败,玩家取消了变脸操作!";
            }

            double 比例 = 0.0;
            if (变脸宠.形象 == "413")
            {
                比例 = 0.6;
            }
            else if (变脸宠.形象 == "414")
            {
                比例 = 0.75;
            }
            else if (变脸宠.形象 == "415")
            {
                比例 = 0.95;
            }
            else if (变脸宠.形象 == "416")
            {
                比例 = 1;
            }

            if (pet1.五行.Equals("佛"))
            {
                if (变脸宠.形象.Equals("413") || 变脸宠.形象.Equals("414"))
                {
                    return "佛系宠物只能用涅槃重生·三阶和涅槃重生·四阶进行变脸!";
                }

                if (变脸宠.形象 == "415")
                {
                    比例 = 0.7;
                }
            }
            else if (pet1.五行.Equals("聖"))
            {
                if (变脸宠.形象.Equals("413") || 变脸宠.形象.Equals("414"))
                {
                    return "聖系宠物只能用涅槃重生·三阶和涅槃重生·四阶进行变脸!";
                }

                if (变脸宠.形象 == "415")
                {
                    比例 = 0.8;
                }
            }

            if (pet2.形象 == "577" || pet2.形象 == "578"|| pet2.形象 == "581" || pet2.形象 == "609" || pet1.形象 == "609" || pet1.形象 == "170" || pet1.形象 == "595" || pet1.形象 == "610" || pet1.形象 == "406" || pet1.形象 == "403" || pet1.形象 == "400" || pet1.形象 == "399" || pet1.形象 == "405" || pet1.形象 == "401" || pet1.形象 == "404" || pet1.形象 == "402" || pet1.形象 == "407" || pet1.形象 == "408" || pet1.形象 == "106" || pet1.形象 == "198")//变脸警告
            {
                return "该宠物无法变脸!";
            }

            if((pet1.五行 == "巫" && pet2.五行 != "巫") || (pet1.五行 != "巫" && pet2.五行 == "巫"))
            {
                return "巫族宠物只能和巫族宠物变脸!";
            }
            UserInfo user = new DataProcess().ReadUserInfo();

            user.金币 = (Convert.ToInt64(user.金币) - 2000000000).ToString();

            new DataProcess().SaveUserDataFile(user);


            new DataProcess().ChangeMainPet(pet1.宠物序号);
            new DataProcess().Undress_Pet(pet1.宠物序号);
            new DataProcess().Undress_Pet(pet2.宠物序号);
            new DataProcess().Undress_Pet(变脸宠.宠物序号);
            //new DataProcess().ToLv1(pet2.宠物序号);
            pet1.成长 = (Convert.ToDouble(pet2.成长) * 比例).ToString(CultureInfo.InvariantCulture);
            pet1.防御 = ((long) (Convert.ToInt64(pet2.防御) * 比例)).ToString();
            pet1.攻击 = ((long) (Convert.ToInt64(pet2.攻击) * 比例)).ToString();
            pet1.命中 = ((long) (Convert.ToInt64(pet2.命中) * 比例)).ToString();
            pet1.魔法 = ((long) (Convert.ToInt64(pet2.魔法) * 比例)).ToString();
            pet1.闪避 = ((long) (Convert.ToInt64(pet2.闪避) * 比例)).ToString();
            pet1.生命 = ((long) (Convert.ToInt64(pet2.生命) * 比例)).ToString();
            pet1.速度 = ((long) (Convert.ToInt64(pet2.速度) * 比例)).ToString();
            pet1.最大魔法 = ((long) (Convert.ToInt64(pet2.最大魔法) * 比例)).ToString();
            pet1.最大生命 = ((long) (Convert.ToInt64(pet2.最大生命) * 比例)).ToString();
            pet1.当前经验 = "1";

            if (bljd)
            {
                var re = GetNewSkillCfg(pet1, pet2);
                if (re == "skillMax")
                {
                    return "请放弃一部分技能后再执行这个操作！";
                }
                pet1.技能列表 = re;
            }
            if (pet2.五行 == "萌" || pet2.五行 == "灵" || pet2.五行 == "次元")//重生-重生种族转移
            {
                pet1.指定五行 = pet2.五行;
                pet1.境界 = pet2.境界;
            }
            else
            {
                pet1.境界 = pet2.境界;
            }
            if (new DataProcess().Update_APDF(pet1.宠物序号, pet1))
            {
                DataProcess.GameForm.发送红色公告(
                    user.名字 + "实在是太厉害了!他将宠物 <" + pet2.宠物名字 + "> 的魂魄成功调换到了 <" + pet1.宠物名字 + "> 身上!");
                new DataProcess().AbandonPet(pet2.宠物序号);
                new DataProcess().AbandonPet(变脸宠.宠物序号);
                return "变脸成功!";
            }

            return "变脸失败,请确定主宠存在!";
        }

        public static List<string> GetSkillList(string skillCfg)
        {   
            List<string> tmpList = new List<string>();

            string[] allSkills = skillCfg.Split(',');

            foreach (string skill in allSkills)
            {
                if (!string.IsNullOrEmpty(skill))
                {
                    string[] cfg = skill.Split('|');
                    if (cfg.Length >= 2)
                    {
                        tmpList.Add(cfg[1]);
                    }
                }
            }

            return tmpList;

        }


        private static string GetNewSkillCfg(PetInfo pet1, PetInfo pet2)
        {   
            List<string> pet1SkillList = GetSkillList(pet1.技能列表);

            List<string> pet2SkillList = GetSkillList(pet2.技能列表);
            if (pet2SkillList.Count + pet1SkillList.Count > 15) {
                return "skillMax";
            }
            List<string> newPet2Sklit = new List<string>();
            for (var i = 0; i < pet2SkillList.Count; i++) {
                if (new DataProcess().判断可否转移(pet2SkillList[i])) {
                    newPet2Sklit.Add(pet2SkillList[i]);
                }
            }
            pet2SkillList = newPet2Sklit;
            List<string> pet2CongenitalSkills = new DataProcess().GetAppointedPetType(pet2.形象).默认技能;

            List<string> pet2AcquiredSkills = pet2SkillList.Except(pet2CongenitalSkills).ToList();

            List<string> result = pet1SkillList.Union(pet2AcquiredSkills).ToList();

            string newCfg = string.Empty;

            foreach (string skill in result)
            {     
                newCfg += ",|" + skill + "|0";      
            }

            return newCfg;
        }


        /*public static String 成长抽取(宠物信息 宠物1, 道具信息 道具1, 道具信息 道具2)
        {
            //AntiCheat.反作弊();
            if (String.IsNullOrEmpty(宠物1.被抽取))
            {
                宠物1.被抽取 = "0";
            }
            if (宠物1.被抽取 == "1")
            {
                return "所选宠物已被抽取过CC!";
            }         
            if (Convert.ToDouble(宠物1.成长) < 数值加密.四十() && 普通五行.Contains(宠物1.五行))
            {
                return "五系宠CC达到40才可抽取!";
            }
            if (Convert.ToDouble(宠物1.成长) < 数值加密.三十() && !普通五行.Contains(宠物1.五行))
            {
                return "神、神圣和聖宠CC达到30才可抽取!";
            }
            if (Convert.ToInt64(宠物1.等级) < 数值加密.四十())
            {
                return "所选宠物等级没有达到40级!";
            }
            if (道具1.道具序号 != "2017052501" || Convert.ToInt64(道具1.道具数量) < 1 )
            {
                return "请选择正确的道具并确保拥有足够数量!";
            }
            if (道具2.道具序号 != "2017052502" || Convert.ToInt64(道具2.道具数量) < 1 )
            {
                return "请选择正确的道具并确保拥有足够数量!";
            }
            

            用户信息 用户 = new 数据处理().读取用户信息();
            
            if (Convert.ToInt64(用户.金币) < Convert.ToInt64(宠物1.成长) * 数值加密.五百() * 数值加密.十())
            {
                return "金币不足!此次抽取需要" + (Convert.ToInt64(宠物1.成长) * 数值加密.五百() * 数值加密.十()).ToString() + "金币噢!";
            }

            
            用户.金币 = (Convert.ToInt64(用户.金币) - Convert.ToInt64(宠物1.成长) * 数值加密.五百() * 数值加密.十()).ToString();
            if (Convert.ToInt32(道具1.道具数量) <= 1)
            {
                new 数据处理().删除道具(道具1);
            }
            else
            {
                道具1.道具数量 = (Convert.ToInt32(道具1.道具数量) - 1).ToString();
                new 数据处理().修改道具(道具1);
            }
            if (Convert.ToInt32(道具2.道具数量) <= 1)
            {
                new 数据处理().删除道具(道具2);
            }
            else
            {
                道具2.道具数量 = (Convert.ToInt32(道具2.道具数量) - 1).ToString();
                new 数据处理().修改道具(道具2);
            }
            int 抽取获得成长 = 0;
            double 种族比例 = 0.0;
            double 成长比例 = 0.0;
            double 波动比例 = Math.Sin(数据处理.随机数产生器.Next(0, 11) / 10 * Math.PI + Math.PI / 数值加密.零点零二() * 数值加密.一百()) / 数值加密.二十();
            //String[] 普通系别 = { "金", "木", "水", "火", "土"};
            //String[] 特殊系别 = {"神", "神圣", "聖"}
            if (普通五行.Contains(宠物1.五行))
            {
                种族比例 = 数值加密.零点六();
            }
            else if (!普通五行.Contains(宠物1.五行))
            {
                种族比例 = 数值加密.零点九();
            }
            if (Convert.ToDouble(宠物1.成长) >= 数值加密.三十() && Convert.ToDouble(宠物1.成长) < 数值加密.六十())
            {
                成长比例 = Convert.ToInt64(宠物1.成长) / 数值加密.二百() - 数值加密.零点零零五() * 数值加密.十();
            }
            else if (Convert.ToDouble(宠物1.成长) >= 数值加密.六十() && Convert.ToDouble(宠物1.成长) <= 数值加密.二百())
            {
                成长比例 = Math.Round(Math.Pow(Convert.ToInt64(宠物1.成长), 0.18828) - 1.911675, 2);
            }
            else if (Convert.ToDouble(宠物1.成长) > 数值加密.二百())
            {
                成长比例 = 数值加密.零点八();
            }

            抽取获得成长 = (int)Math.Round(Convert.ToInt64(宠物1.成长) * 种族比例 * 成长比例 * (1 + 波动比例));

            宠物1.成长 = "1";
            宠物1.防御 = "1";
            宠物1.攻击 = "1";
            宠物1.命中 = "1";
            宠物1.魔法 = "1";
            宠物1.闪避 = "1";
            宠物1.生命 = "1";
            宠物1.速度 = "1";
            宠物1.最大魔法 = "1";
            宠物1.最大生命 = "1";
            宠物1.等级 = "1";
            宠物1.当前经验 = "1";
            宠物1.被抽取 = "1";
 
            if (new 数据处理().Update_APDF(宠物1.宠物序号, 宠物1))
            {
                数据处理._游戏窗口.发送红色公告("抽取CC成功!");
                用户.已抽取cc = (Convert.ToInt64(用户.已抽取cc) + 抽取获得成长).ToString();
                new 数据处理().保存用户存档(用户);
                return "抽取CC成功!";
            }
            else
            {
                new 数据处理().保存用户存档(用户);
                return "抽取CC失败,请确定所选宠物存在!";
            }
        }
        public static String 成长转化(宠物信息 宠物1, String 要转化成长)
        {
            //AntiCheat.反作弊();
            if (Convert.ToInt64(宠物1.等级) < 数值加密.四十())
            {
                return "主宠等级没有达到40级!";
            }
            if (宠物1.五行 != "神圣")
            {
                return "只有神圣宠物才能转化成长!";
            }
            if (String.IsNullOrEmpty(要转化成长)|| 要转化成长.Contains(" "))
            {
                return "请指定正确的要转化CC量!";
            }
            if (要转化成长.Contains("."))
            {
                return "所转化CC量必须为整数!";
            }
            if (Convert.ToInt64(要转化成长) <= 0)
            {
                return "待转化CC量必须大于0!";
            }

            用户信息 用户 = new 数据处理().读取用户信息();
            if (String.IsNullOrEmpty(用户.已抽取cc))
            {
                用户.已抽取cc = "0";
            }
            if (Convert.ToInt64(要转化成长) > Convert.ToInt64(用户.已抽取cc))
            {
                return "没有这么多CC可供转化!";
            }
            if (Convert.ToInt64(用户.金币) < Convert.ToInt64(要转化成长) * 数值加密.五百() * 数值加密.十())
            {
                return "金币不足!此次抽取需要" + (Convert.ToInt64(要转化成长) * 数值加密.五百() * 数值加密.十()).ToString() + "金币噢!";;
            }

            用户.金币 = (Convert.ToInt64(用户.金币) - Convert.ToInt64(要转化成长) * 数值加密.五百() * 数值加密.十()).ToString();

            宠物1.成长 = (Convert.ToDouble(宠物1.成长) + Convert.ToInt64(要转化成长)).ToString();

            if (new 数据处理().Update_APDF(宠物1.宠物序号, 宠物1))
            {
                数据处理._游戏窗口.发送红色公告("抽取CC成功!");
                new 数据处理().保存用户存档(用户);
                return "转化CC成功!";
            }
            else
            {
                new 数据处理().保存用户存档(用户);
                return "转化CC失败,请确定所选宠物存在!";
            }

        }*/
    }
}
