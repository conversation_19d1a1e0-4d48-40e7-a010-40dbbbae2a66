﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace 测试工具.GameData
{
    public class MapData
    {
        public static Dictionary<string, string> MapList = new Dictionary<string, string>
        {
            {"新手基地", "1"},
            {"妖精森林", "2"},
            {"潮汐海崖", "3"},
            {"巨石山脉", "4"},
            {"黄金陵", "5"},
            {"炽热沙滩", "6"},
            {"尤玛火山", "7"},
            {"死亡沙漠", "8"},
            {"海市蜃楼", "9"},
            {"冰滩", "10"},
            {"海底世界", "11"},
            {"圣诞小屋", "12"},
            {"石阵", "13"},
            {"平原", "14"},
            {"绿荫林", "15"},
            {"五指石印", "16"},
            {"鬼屋", "17"},
            {"天空之城", "18"},
            {"天之路", "19"},
            {"危之路", "20"},
            {"幽冥之镜", "21"},
            {"异界深渊", "22"},
            {"时空冰岛", "23"},
            {"圣兽云殿", "102"},
            {"埋骨之地", "201"},
            {"孢子林", "202"},
            {"迷雾森林", "203"},
            {"汐愿之海", "205"},
            {"巨石荒野", "206"},
            {"蓝泪之泉", "207"},
            {"伊苏王的神墓", "FB9001"},
            {"火龙王的宫殿", "FB9002"},
            {"史芬克斯密穴", "FB9003"},
            {"玲珑城", "FB9004"},
            {"BOSS集中营", "FB9010"},
            {"楼兰古城的遗迹", "FB9011"},
            {"赫拉神殿", "201901"},
            {"阿尔提密林", "FB9012"},
            {"幻魔之境", "FB204"},
            {"地狱之门", null},
            {"通天塔", null}
        };
    
    
    
    }
}
