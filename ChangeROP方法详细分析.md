# ChangeROP方法详细分析 - 地图层数控制核心

## 1. 方法定义和功能

### 1.1 ChangeROP方法源码

<augment_code_snippet path="WindowsFormsApplication7/DataProcess.cs" mode="EXCERPT">
````csharp
internal bool ChangeROP(string mapId, string floor)
{
    FBROP fbInfo = GetFBROP(mapId);           // 获取指定地图的当前进度
    List<FBROP> rop = GetFBROPList();         // 获取所有副本进度列表
    
    if (fbInfo == null)
    {
        // 地图进度不存在，创建新记录
        fbInfo = new FBROP() { id = mapId, num = floor };
        rop.Add(fbInfo);
    }
    else
    {
        // 地图进度已存在，更新层数
        foreach (FBROP t in rop)
        {
            if (t.id != null && t.id.Equals(mapId))
            {
                t.num = floor;  // 直接设置新的层数值
            }
        }
    }
    
    return SaveFBROPList(rop);  // 保存更新后的进度列表
}
````
</augment_code_snippet>

### 1.2 方法功能

**ChangeROP** = **Change** **R**eset **O**r **P**rogress (改变重置或进度)

- **直接设置**: 可以将地图层数设置为任意指定值
- **创建记录**: 如果地图进度不存在，会自动创建
- **立即生效**: 修改后立即保存到存档文件

## 2. 调用场景分析

### 2.1 副本完成标记 (设置为-10)

**场景1: 正常副本完成**
<augment_code_snippet path="WindowsFormsApplication7/Fight.cs" mode="EXCERPT">
````csharp
private static void ChangeFbProgress()
{
    if (FBMap && 地图 != "地狱之门" && 地图 != "通天塔")
    {
        FBROP 当前层数 = new DataProcess().GetFBROP(地图);
        
        // 判断副本是否已刷完所有怪物
        if (当前层数 != null && new DataProcess().GetAMML(地图).Count <= Convert.ToInt32(当前层数.num) + 1)
        {
            new DataProcess().ChangeROP(地图, "-10");  // 标记副本完成
        }
        else
        {
            new DataProcess().PromoteROP(地图);  // 正常推进进度
        }
    }
}
````
</augment_code_snippet>

**场景2: 战斗失败时的副本完成**
<augment_code_snippet path="WindowsFormsApplication7/Fight.cs" mode="EXCERPT">
````csharp
// 宠物死亡时的处理
if (FBMap)
{
    new DataProcess().ChangeROP(地图, "-10");  // 战斗失败也标记为完成
}
````
</augment_code_snippet>

### 2.2 自动重置 (设置为-1)

<augment_code_snippet path="WindowsFormsApplication7/Fight.cs" mode="EXCERPT">
````csharp
if (AutoMap && tmp >= MapFloor)  // 达到自动重置条件
{
    var r = DataProcess.AutoMap.FirstOrDefault(C => C.mapID.ToString() == 地图);
    if (r != null)
    {
        PropInfo ttkey = new DataProcess().GetAP_ID(r.propID);
        
        if (ttkey != null && Convert.ToInt32(ttkey.道具数量) >= 1)
        {
            new DataProcess().ReviseOrDeletePP(ttkey, 1);  // 消耗重置道具
            DataProcess.GameForm.发送红色公告("自动副本功能已帮您自动重置一次副本。");
            
            new DataProcess().ChangeROP(地图, "-1");  // 重置副本
        }
    }
}
````
</augment_code_snippet>

### 2.3 道具脚本重置 (设置为0)

<augment_code_snippet path="WindowsFormsApplication7/DataProcess.cs" mode="EXCERPT">
````csharp
if (directive[0].Equals("重置副本"))
{
    FBROP 副本 = GetFBROP(directive[1]);
    
    if (副本 != null && 副本.num != null && 副本.num == "-10")
    {
        if (副本.num == "0")
        {
            msg = "副本已经开启了!";
            return false;
        }
        msg = "指定副本开启成功!";
        ChangeROP(directive[1], "0");  // 开启已完成的副本
    }
    else
    {
        msg = "指定副本重置成功!";
        ChangeROP(directive[1], "0");  // 重置副本到初始状态
    }
    
    return true;
}
````
</augment_code_snippet>

## 3. 层数值的含义

### 3.1 特殊状态值

| 值 | 含义 | 说明 |
|---|---|---|
| **-10** | 副本完成 | 所有怪物已击败，等待重置 |
| **-1** | 重置状态 | 自动重置触发，准备重新开始 |
| **0** | 初始状态 | 副本刚开始或被重置 |
| **1,2,3...** | 正常进度 | 当前战斗的怪物序号 |

### 3.2 状态转换图

```
初始状态: null
    ↓ (首次进入)
    0 (开始副本)
    ↓ (战斗推进)
1 → 2 → 3 → ... → n (正常进度)
    ↓ (副本完成)
   -10 (等待重置)
    ↓ (自动重置/道具重置)
   -1 或 0 (重置状态)
    ↓ (重新开始)
    0 (新一轮开始)
```

## 4. 与其他方法的区别

### 4.1 ChangeROP vs PromoteROP

| 方法 | 功能 | 使用场景 |
|---|---|---|
| **ChangeROP** | 直接设置层数 | 重置、完成标记、特殊状态 |
| **PromoteROP** | 递增层数(+1) | 正常战斗推进 |

<augment_code_snippet path="WindowsFormsApplication7/DataProcess.cs" mode="EXCERPT">
````csharp
// PromoteROP - 只能递增
internal bool PromoteROP(string mapId)
{
    // ...
    t.num = (Convert.ToInt32(t.num) + Floor).ToString();  // 只能+1
    // ...
}

// ChangeROP - 可以设置任意值
internal bool ChangeROP(string mapId, string floor)
{
    // ...
    t.num = floor;  // 可以设置任意值
    // ...
}
````
</augment_code_snippet>

## 5. 实际使用示例

### 5.1 管理员重置副本

```csharp
// 管理员可以通过道具脚本重置任意副本
// 道具效果: "重置副本|地图ID"
new DataProcess().ChangeROP("101", "0");  // 重置地图101到初始状态
```

### 5.2 自动化系统重置

```csharp
// 自动副本系统达到条件时重置
new DataProcess().ChangeROP("102", "-1");  // 标记地图102为重置状态
```

### 5.3 副本完成标记

```csharp
// 系统检测到副本完成时自动标记
new DataProcess().ChangeROP("103", "-10");  // 标记地图103已完成
```

## 6. 数据持久化

### 6.1 存储位置

副本进度存储在用户存档的第5个分段：
```
基础信息O4F89宠物信息O4F89道具信息O4F89存档版本O4F89装备信息O4F89副本进度O4F89任务信息
```

### 6.2 存储格式

```json
[
    {"id": "101", "num": "5"},     // 地图101当前在第5层
    {"id": "102", "num": "-10"},   // 地图102已完成
    {"id": "103", "num": "0"}      // 地图103在初始状态
]
```

### 6.3 加密保护

- **RC4加密**: 防止玩家直接修改存档
- **密钥组合**: 使用复合密钥增强安全性
- **完整性校验**: 检测数据是否被篡改

## 7. 错误处理

### 7.1 异常情况处理

```csharp
if (fbInfo == null)
{
    // 地图进度不存在时自动创建
    fbInfo = new FBROP() { id = mapId, num = floor };
    rop.Add(fbInfo);
}
```

### 7.2 数据验证

- **mapId验证**: 确保地图ID有效
- **floor验证**: 虽然接受字符串，但通常应为数字
- **存档完整性**: 保存前验证数据结构

## 8. 性能考虑

### 8.1 批量操作

```csharp
// 一次性修改多个地图的层数
List<FBROP> rop = GetFBROPList();
foreach (var map in mapsToReset)
{
    var fbInfo = rop.FirstOrDefault(f => f.id == map.id);
    if (fbInfo != null)
    {
        fbInfo.num = map.newFloor;
    }
}
SaveFBROPList(rop);  // 一次性保存所有修改
```

### 8.2 缓存机制

- **避免重复读取**: 一次获取所有进度数据
- **批量保存**: 减少文件IO操作
- **内存管理**: 及时释放临时对象

## 9. 安全性考虑

### 9.1 权限控制

- **内部方法**: `internal`修饰符限制访问范围
- **参数验证**: 应该验证输入参数的合法性
- **操作日志**: 记录重要的层数修改操作

### 9.2 防作弊机制

- **合理性检查**: 层数变化应该符合游戏逻辑
- **时间验证**: 防止异常快速的进度变化
- **资源消耗**: 重置操作需要消耗道具

---

## 总结

`ChangeROP`方法是副本地图层数控制的核心工具，具有以下特点：

### 🎯 **核心功能**
1. **直接设置**: 可以将地图层数设置为任意值
2. **状态管理**: 支持特殊状态值(-10, -1, 0)
3. **自动创建**: 不存在的地图会自动创建进度记录
4. **立即生效**: 修改后立即保存到存档

### 🔧 **使用场景**
1. **副本完成**: 设置为-10标记完成状态
2. **自动重置**: 设置为-1触发重置流程
3. **手动重置**: 设置为0回到初始状态
4. **特殊操作**: 管理员工具和道具脚本

### 🛡️ **安全特性**
1. **加密存储**: RC4加密保护数据安全
2. **完整性校验**: 防止数据篡改
3. **权限控制**: 内部方法限制访问

这个方法是整个副本系统的核心控制器，为游戏提供了灵活而安全的地图进度管理功能。
