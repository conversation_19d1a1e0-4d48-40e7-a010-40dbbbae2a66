﻿using Newtonsoft.Json;
using Shikong.Pokemon2.PCG;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ShikongPlus.Pokemon2.PCG.龙珠
{
    public class longzhu
    {
        public List<lz_jieduan> 阶段表 { get; set; }
        /// <summary>
        /// 经验表定义了经验值对应的登记，第一个大于当前经验的成员下标即为当前等级
        /// </summary>
        public long[] 经验表 { get; set; }
        public List<lz_qujian> 区间表 { get; set; }
        /// <summary>
        /// 配置缓存
        /// </summary>
        public static string cfgCache = null;

        /// <summary>
        /// 配置缓存
        /// </summary>
        public static string cfgCache_lv = null;
        public const string Path = @"PageMain\equip_4.qingshan"; //龙珠配置的路径
        /// <summary>
        /// 获取龙珠的配置表
        /// </summary>
        /// <param name="getname"></param>
        /// <returns></returns>
        public static longzhu GetLongzhu()
        {
            string cfg = cfgCache;
            if (cfg == null) cfg = new DataProcess().ReadFile(DataProcess.pf + Path);
            if (cfg == null || cfg.Length <= 0)
            {
                return new longzhu();
            }
            cfg = SkRC4.DES.DecryptRC4(cfg, new DataProcess().GetKey(1));
            var way = JsonConvert.DeserializeObject<longzhu>(cfg);
            DataProcess.stringLoadLZJSON = cfg;
            return way;
        }
        public static int calcLV(double exp) {
            int i = 1;
            bool yes = false;
            var info = GetLongzhu();
            foreach (var e in info.经验表) {
                if (e >= exp)
                {
                    yes = true;
                    break;
                }
                i++;
            }
            //已经满级的情况下会找不到，所以就按照满级返回
            if (!yes) return info.经验表.Count() + 1;
            return i;
        }

        public static string AddLzExp(int index,int num)
        {
            UserInfo user = new DataProcess().ReadUserInfo();
            if (user.龙珠信息 == null || user.龙珠信息.name == "")
            {
                return "您当前没有龙珠~无法使用该道具增加龙珠经验哦！";
            }
            //判断最高经验和上限
            string propID = "";//1 五彩 ，2 彩虹，3 幻彩，4 梦想，5 至尊
            if (index == 1)
            {
                propID = "820230302";
            }
            else if (index == 2)
            {
                propID = "820230303";
            }
            else if (index == 3)
            {
                propID = "820230304";
            }
            else if (index == 4)
            {
                propID = "820230305";
            }
            else if (index == 5)
            {
                propID = "820230306";
            }
            if (string.IsNullOrEmpty(propID))
            {
                return "未选择龙珠！";
            }
            string info = new DataProcess().ReadPropScript(propID).道具脚本;

            var thisLZ = GetLongzhu().阶段表.FirstOrDefault(C => C.名称 == user.龙珠信息.name);
            string[] info_sp = info.Split('|');
            int maxExp = calcLV(user.龙珠信息.exp + (Convert.ToDouble(info_sp[info_sp.Length - 1]) * num -500));//这里允许溢出为最大经验值 + 100，既按照最大随机数*数量-100经验值
            if (calcLV(user.龙珠信息.exp) >= thisLZ.最大等级)
            {
                return "当前龙珠已经到达了该阶段的最大等级，请突破后再进行该操作！";
            }
            if (maxExp >= thisLZ.最大等级)
            {
                return "预计可能经验溢出，请减少数量后再试！";
            }
            //判断道具数量是否足够
            var userprop = new DataProcess().GetAP_ID(propID);
            if (userprop == null)
            {
                return $"未持有{new DataProcess().GetPropName(propID)}";
            }
            if (Convert.ToInt32(userprop.道具数量) < num)
            {
                return $"持有{userprop.道具名字}数量：{userprop.道具数量}。<br>没有足够数量炼化！";
            }
            //扣除道具
            userprop.道具数量 = (Convert.ToInt32(userprop.道具数量) - num).ToString();
            if (Convert.ToInt32(userprop.道具数量) <= 0)
            {
                new DataProcess().DeletePP1(userprop.道具序号);
            }
            else
            {
                new DataProcess().RevisePP(userprop);
            }
            //增加经验值
            string[] getExp = info.Split('|');
            double exp1 = user.龙珠信息.exp;//记录初始经验
            double lzjy = 0;//增加的经验
            for (int i = 0; i < num; i++)
            {
                lzjy += DataProcess.RandomGenerator.Next(Convert.ToInt32(getExp[1]), Convert.ToInt32(getExp[2]) + 1);
            }
            user.龙珠信息.exp += lzjy;
            double exp2 = user.龙珠信息.exp;//增加后的经验
            
            new DataProcess().SaveUserDataFile(user);
            string SuccessMsg = "";
            int ranRs = DataProcess.RandomGenerator.Next(0,4);
            if (ranRs == 0)
            {
                SuccessMsg = $"{user.名字}一挥手便将龙珠收于掌中，瞬间炼化。";
            }
            else if (ranRs == 1)
            {
                SuccessMsg = $"只见{user.名字}指尖轻点，便将龙珠摄入体内，刹那炼化。";
            }
            else if (ranRs == 2)
            {
                SuccessMsg = $"一阵风声过后，{user.名字}已将的龙珠吸入体内，刹那间炼化完毕。";
            }
            else if (ranRs == 3)
            {
                SuccessMsg = $"{user.名字}一个念头，便将龙珠吸入心海，顷刻炼化。";
            }
            DataProcess.GameForm.发送红色公告(SuccessMsg+$"获得龙珠经验{exp2 - exp1}。");

            return "";
        }


        public static Dictionary<String, double> calcALL(out string Lvvname)
        {
            var user = new DataProcess().ReadUserInfo();
            var longzhu = GetLongzhu();
            Lvvname = "";
            var calcResult = new Dictionary<String, double>();
            calcResult.Add("攻击", 0);
            calcResult.Add("命中", 0);
            calcResult.Add("防御", 0);
            calcResult.Add("速度", 0);
            calcResult.Add("闪避", 0);
            calcResult.Add("生命", 0);
            calcResult.Add("魔法", 0);
            calcResult.Add("加深", 0);
            calcResult.Add("抵消", 0);
            calcResult.Add("吸血", 0);
            calcResult.Add("吸魔", 0);
            if (user.龙珠信息 == null) return calcResult;
            var thisLV = calcLV(user.龙珠信息.exp);
            //取出所有需要计算的区间
            var calcQJ = longzhu.区间表.Where(C => C.min <= thisLV).OrderByDescending(C => C.min);
            int j = 0;
            foreach (var jie in calcQJ) {
                j++;
                var calcNum = jie.max - jie.min +1;//当前区间需要计算的等级数量
                if (j == 1) calcNum = thisLV - jie.min + 1;
                if (j == 1) Lvvname = jie.name;
                foreach (var sx in jie.sx)
                {
                    calcResult[sx.Key] += sx.Value * calcNum;
                }
            }

            //计算当前龙珠突破阶段的基础属性
            var thisLongzhuType = longzhu.阶段表.FirstOrDefault(C => C.名称.Equals(user.龙珠信息.name));
            if (thisLongzhuType.基础属性 != null && thisLongzhuType.基础属性.Count > 0) {
                foreach (var sx in thisLongzhuType.基础属性) {
                    calcResult[sx.Key] += sx.Value;
                }
            }
            return calcResult;
        }
        ///// <summary>
        ///// 获取一个新的龙珠经验道具的概率组
        ///// </summary>
        //public static double[] randomLongzhuExp() {

        //    List<double> r = new List<double>();
        //    var rd = new Random();
        //    for (var i = 0; i <= 100; i++) {
        //        r.Add(rd.NextDouble());
        //    }
        //    return r.ToArray();
        //}
        ///// <summary>
        ///// 获取当前龙珠经验道具应该获取的比例
        ///// </summary>
        //public static double getLongzhuExpRandomNum()
        //{

        //    var user = new DataProcess().ReadUserInfo();
        //    int index = user.龙珠随机下标;
        //    if (user.龙珠经验随机组.Length <= index) {
        //        user.龙珠随机下标 = 0;
        //        index = 0;
        //        user.龙珠经验随机组 = randomLongzhuExp();
        //        new DataProcess().SaveUserDataFile(user);
        //    }
        //    double rnum = user.龙珠经验随机组[index];
        //    user.龙珠随机下标++;
        //    new DataProcess().SaveUserDataFile(user);
        //    return rnum;
        //}
    }

    /// <summary>
    /// 龙珠区间表：
    /// 1、用来获取当前龙珠等级的等级昵称是什么
    /// 2、计算龙珠属性时需要对不同的区间进行计算
    /// </summary>
    public class lz_qujian
    {
        /// <summary>
        /// 当前区间最小等级
        /// </summary>
        public int min { get; set; }
        /// <summary>
        /// 当前区间最大等级
        /// </summary>
        public int max { get; set; }
        /// <summary>
        /// 当前区间的昵称
        /// </summary>
        public string name { get; set; }
        /// <summary>
        /// 当前区间内的每一级增加多少经验
        /// </summary>
        public Dictionary<String, Double> sx { get; set; }
    }
    /// <summary>
    /// 龙珠阶段表，用来判定该阶段的龙珠最大能到达多少等级
    /// </summary>
    public class lz_jieduan
    {
        /// <summary>
        /// 顺序，决定了突破道具判定时的逻辑，所以一定要写对
        /// </summary>
        public int 顺序 { get; set; }
        /// <summary>
        /// 阶段名称
        /// </summary>
        public string 名称 { get; set; }
        /// <summary>
        /// 当前阶段最大能升多少级
        /// </summary>
        public int 最大等级 { get; set; }
        /// <summary>
        /// 等级之外的基础属性，只要突破到当前阶段就享有的加成
        /// </summary>
        public Dictionary<String,Double> 基础属性 { get; set; }
    }
}
