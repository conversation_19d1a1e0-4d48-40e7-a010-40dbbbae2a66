﻿using Newtonsoft.Json;
using Shikong.Pokemon2.PCG;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Admin
{
    public class Game_Helper
    {
        /// <summary>
        /// 根据道具类型序号获取道具类型
        /// </summary>
        /// <param name="道具序号"></param>
        /// <returns></returns>
        internal PropType GetAPType(string 道具序号) //取指定道具类型
        {
            道具序号 = 道具序号.Replace(" ", "");
            var ptList1 = new List<PropType>();
            if (!string.IsNullOrEmpty(DataProcess.ptList))
            {
                string json = SkRC4.DES.DecryptRC4(DataProcess.ptList, T.GetKey(1));
                ptList1 = JsonConvert.DeserializeObject<List<PropType>>(json);
                foreach (PropType prop in ptList1)
                {
                    if (prop.道具序号.Equals(道具序号))
                    {
                        return prop;
                    }
                }
            }
            var newPopList =new DataProcess().ReadAllPropTypes().Concat(ptList1);
            //List<PropType> 道具 = ReadAllPropTypes();
            var rs = newPopList.FirstOrDefault(信息 => 信息.道具序号.Equals(道具序号));
            if (rs == null)
            {
                return new PropType()
                {
                    道具名字 = "道具不存在！",
                };
            }
            return rs;
        }

        /// <summary>
        /// 取指定怪物类型
        /// </summary>
        /// <param name="怪物序号"></param>
        /// <returns></returns>
        internal MonsterType Get_SMT(string 怪物序号) 
        {
            List<MonsterType> list = new DataProcess().Get_MTList();
            return list.FirstOrDefault(怪物 => 怪物.怪物序号.Equals(怪物序号));
        }


        /// <summary>
        /// 获取指定任务
        /// </summary>
        /// <param name="序号"></param>
        /// <returns></returns>
        internal TaskInfo GetAppointedTaskAim(string 序号)
        {
            List<TaskInfo> 列表 = new DataProcess().GetAllTaskAim();
            return 列表.FirstOrDefault(信息 => 信息.任务序号 == 序号);
        }


    }
}
