成长突破前端集成说明
==================

一、功能说明
成长突破功能已完成与sksd.html页面的对接，实现了完整的前后端交互。

二、实现的功能

1. 信息显示
   - 当前突破等级显示
   - 所需材料数量显示
   - 实时成功率计算显示
   - 自动检测是否满级

2. 交互功能
   - 突破圣石数量输入验证
   - 凤凰晶石选择（最多3个）
   - 成功率实时更新
   - 执行突破操作

3. 用户体验优化
   - 打开弹框时自动刷新信息
   - 执行突破后自动更新显示
   - 清晰的错误提示
   - 材料不足时的友好提醒

三、使用流程

1. 点击"成长突破"按钮
2. 系统自动获取并显示当前突破信息
3. 输入突破圣石数量（系统会验证是否足够）
4. 可选：输入凤凰晶石数量（0-3个）
5. 点击"使用道具"确认凤凰晶石数量
6. 查看当前成功率
7. 点击"成长突破"执行操作
8. 系统自动刷新显示结果

四、关键函数说明

1. updateBreakthroughInfo()
   - 更新所有突破相关信息
   - 在打开弹框时自动调用

2. updatePhoenixStoneDisplay()
   - 更新凤凰晶石显示和成功率
   - 选择凤凰晶石后调用

3. executeBreakthrough()
   - 执行突破操作
   - 调用后端接口并处理结果

五、后端接口调用

1. window.external.GrowthBreakthrough_GetInfo()
   - 获取当前突破信息（JSON格式）

2. window.external.GrowthBreakthrough_TryBreakthrough(phoenixCount)
   - 执行突破操作
   - phoenixCount: 使用的凤凰晶石数量（0-3）

六、注意事项

1. 突破圣石在执行突破时自动消耗，无需单独使用
2. 凤凰晶石需要先选择数量，最多使用3个
3. 成功率上限为100%
4. 突破失败会有相应的惩罚机制
5. 前端会自动验证输入的合法性

七、测试建议

1. 测试各种边界情况（满级、材料不足等）
2. 测试成功率计算的准确性
3. 测试突破成功和失败的不同场景
4. 验证界面刷新的及时性 