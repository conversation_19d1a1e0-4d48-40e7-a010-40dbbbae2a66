﻿namespace Admin
{
    partial class 宠物进化路线
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.dataGridView1 = new System.Windows.Forms.DataGridView();
            this.宠物名字 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.宠物序号 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.系别 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.textBox1 = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.dataGridView2 = new System.Windows.Forms.DataGridView();
            this.button1 = new System.Windows.Forms.Button();
            this.选中的宠物路线 = new System.Windows.Forms.GroupBox();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.label3 = new System.Windows.Forms.Label();
            this.BLV = new System.Windows.Forms.TextBox();
            this.label6 = new System.Windows.Forms.Label();
            this.BPROP = new System.Windows.Forms.TextBox();
            this.label8 = new System.Windows.Forms.Label();
            this.BNAME = new System.Windows.Forms.Label();
            this.BID = new System.Windows.Forms.TextBox();
            this.label10 = new System.Windows.Forms.Label();
            this.BPIC = new System.Windows.Forms.PictureBox();
            this.button2 = new System.Windows.Forms.Button();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.label7 = new System.Windows.Forms.Label();
            this.ALV = new System.Windows.Forms.TextBox();
            this.label5 = new System.Windows.Forms.Label();
            this.APROP = new System.Windows.Forms.TextBox();
            this.label4 = new System.Windows.Forms.Label();
            this.ANAME = new System.Windows.Forms.Label();
            this.AID = new System.Windows.Forms.TextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.APIC = new System.Windows.Forms.PictureBox();
            this.宠物ID = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.A路线宠物 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.A路线道具 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.A路线等级 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.B路线宠物 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.B路线道具 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.B路线等级 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView1)).BeginInit();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView2)).BeginInit();
            this.选中的宠物路线.SuspendLayout();
            this.groupBox3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.BPIC)).BeginInit();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.APIC)).BeginInit();
            this.SuspendLayout();
            // 
            // dataGridView1
            // 
            this.dataGridView1.AllowUserToAddRows = false;
            this.dataGridView1.AllowUserToDeleteRows = false;
            this.dataGridView1.AllowUserToResizeColumns = false;
            this.dataGridView1.AllowUserToResizeRows = false;
            this.dataGridView1.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.dataGridView1.CellBorderStyle = System.Windows.Forms.DataGridViewCellBorderStyle.SingleHorizontal;
            this.dataGridView1.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.宠物名字,
            this.宠物序号,
            this.系别});
            this.dataGridView1.ImeMode = System.Windows.Forms.ImeMode.Katakana;
            this.dataGridView1.Location = new System.Drawing.Point(5, 33);
            this.dataGridView1.Name = "dataGridView1";
            this.dataGridView1.RowHeadersVisible = false;
            this.dataGridView1.RowTemplate.Height = 23;
            this.dataGridView1.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.dataGridView1.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dataGridView1.Size = new System.Drawing.Size(304, 155);
            this.dataGridView1.TabIndex = 1;
            this.dataGridView1.CellContentClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.dataGridView1_CellContentClick);
            this.dataGridView1.CellContentDoubleClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.dataGridView1_CellContentDoubleClick);
            this.dataGridView1.CellMouseDoubleClick += new System.Windows.Forms.DataGridViewCellMouseEventHandler(this.dataGridView1_CellMouseDoubleClick);
            // 
            // 宠物名字
            // 
            this.宠物名字.DataPropertyName = "宠物名字";
            this.宠物名字.HeaderText = "宠物名字";
            this.宠物名字.Name = "宠物名字";
            this.宠物名字.ReadOnly = true;
            this.宠物名字.Width = 102;
            // 
            // 宠物序号
            // 
            this.宠物序号.DataPropertyName = "宠物序号";
            this.宠物序号.HeaderText = "宠物序号";
            this.宠物序号.Name = "宠物序号";
            this.宠物序号.ReadOnly = true;
            this.宠物序号.Width = 101;
            // 
            // 系别
            // 
            this.系别.DataPropertyName = "系别";
            this.系别.HeaderText = "系别";
            this.系别.Name = "系别";
            // 
            // textBox1
            // 
            this.textBox1.Location = new System.Drawing.Point(73, 6);
            this.textBox1.Name = "textBox1";
            this.textBox1.Size = new System.Drawing.Size(126, 21);
            this.textBox1.TabIndex = 2;
            this.textBox1.TextChanged += new System.EventHandler(this.textBox1_TextChanged);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(8, 10);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(59, 12);
            this.label1.TabIndex = 3;
            this.label1.Text = "快速搜索:";
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.dataGridView2);
            this.groupBox1.Location = new System.Drawing.Point(317, 38);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(706, 410);
            this.groupBox1.TabIndex = 4;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "路线表";
            // 
            // dataGridView2
            // 
            this.dataGridView2.AllowUserToAddRows = false;
            this.dataGridView2.AllowUserToDeleteRows = false;
            this.dataGridView2.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.dataGridView2.CellBorderStyle = System.Windows.Forms.DataGridViewCellBorderStyle.SingleHorizontal;
            this.dataGridView2.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.宠物ID,
            this.A路线宠物,
            this.A路线道具,
            this.A路线等级,
            this.B路线宠物,
            this.B路线道具,
            this.B路线等级});
            this.dataGridView2.ImeMode = System.Windows.Forms.ImeMode.Katakana;
            this.dataGridView2.Location = new System.Drawing.Point(7, 18);
            this.dataGridView2.Name = "dataGridView2";
            this.dataGridView2.ReadOnly = true;
            this.dataGridView2.RowHeadersVisible = false;
            this.dataGridView2.RowTemplate.Height = 23;
            this.dataGridView2.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.dataGridView2.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dataGridView2.Size = new System.Drawing.Size(693, 386);
            this.dataGridView2.TabIndex = 2;
            // 
            // button1
            // 
            this.button1.Location = new System.Drawing.Point(213, 7);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(91, 20);
            this.button1.TabIndex = 5;
            this.button1.Text = "打开进化表";
            this.button1.UseVisualStyleBackColor = true;
            this.button1.Click += new System.EventHandler(this.button1_Click);
            // 
            // 选中的宠物路线
            // 
            this.选中的宠物路线.Controls.Add(this.groupBox3);
            this.选中的宠物路线.Controls.Add(this.button2);
            this.选中的宠物路线.Controls.Add(this.groupBox2);
            this.选中的宠物路线.Enabled = false;
            this.选中的宠物路线.Location = new System.Drawing.Point(10, 194);
            this.选中的宠物路线.Name = "选中的宠物路线";
            this.选中的宠物路线.Size = new System.Drawing.Size(287, 282);
            this.选中的宠物路线.TabIndex = 6;
            this.选中的宠物路线.TabStop = false;
            this.选中的宠物路线.Text = "选中的宠物路线";
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.label3);
            this.groupBox3.Controls.Add(this.BLV);
            this.groupBox3.Controls.Add(this.label6);
            this.groupBox3.Controls.Add(this.BPROP);
            this.groupBox3.Controls.Add(this.label8);
            this.groupBox3.Controls.Add(this.BNAME);
            this.groupBox3.Controls.Add(this.BID);
            this.groupBox3.Controls.Add(this.label10);
            this.groupBox3.Controls.Add(this.BPIC);
            this.groupBox3.Location = new System.Drawing.Point(12, 124);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(262, 98);
            this.groupBox3.TabIndex = 3;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "B路线";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(110, 41);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(53, 12);
            this.label3.TabIndex = 11;
            this.label3.Text = "(道具名)";
            // 
            // BLV
            // 
            this.BLV.Location = new System.Drawing.Point(156, 65);
            this.BLV.Name = "BLV";
            this.BLV.Size = new System.Drawing.Size(45, 21);
            this.BLV.TabIndex = 10;
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(111, 68);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(35, 12);
            this.label6.TabIndex = 9;
            this.label6.Text = "等级:";
            // 
            // BPROP
            // 
            this.BPROP.Location = new System.Drawing.Point(156, 15);
            this.BPROP.Name = "BPROP";
            this.BPROP.Size = new System.Drawing.Size(92, 21);
            this.BPROP.TabIndex = 8;
            this.BPROP.Click += new System.EventHandler(this.BPROP_Click);
            this.BPROP.TextChanged += new System.EventHandler(this.BPROP_TextChanged_1);
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(111, 18);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(35, 12);
            this.label8.TabIndex = 7;
            this.label8.Text = "道具:";
            // 
            // BNAME
            // 
            this.BNAME.AutoSize = true;
            this.BNAME.Location = new System.Drawing.Point(9, 41);
            this.BNAME.Name = "BNAME";
            this.BNAME.Size = new System.Drawing.Size(41, 12);
            this.BNAME.TabIndex = 6;
            this.BNAME.Text = "宠物名";
            // 
            // BID
            // 
            this.BID.Location = new System.Drawing.Point(54, 15);
            this.BID.Name = "BID";
            this.BID.ReadOnly = true;
            this.BID.Size = new System.Drawing.Size(45, 21);
            this.BID.TabIndex = 5;
            this.BID.Click += new System.EventHandler(this.BID_Click);
            this.BID.TextChanged += new System.EventHandler(this.BID_TextChanged);
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(9, 18);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(47, 12);
            this.label10.TabIndex = 4;
            this.label10.Text = "宠物ID:";
            // 
            // BPIC
            // 
            this.BPIC.Location = new System.Drawing.Point(54, 44);
            this.BPIC.Name = "BPIC";
            this.BPIC.Size = new System.Drawing.Size(50, 50);
            this.BPIC.TabIndex = 3;
            this.BPIC.TabStop = false;
            // 
            // button2
            // 
            this.button2.Location = new System.Drawing.Point(60, 227);
            this.button2.Name = "button2";
            this.button2.Size = new System.Drawing.Size(164, 51);
            this.button2.TabIndex = 2;
            this.button2.Text = "保存";
            this.button2.UseVisualStyleBackColor = true;
            this.button2.Click += new System.EventHandler(this.button2_Click);
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.label7);
            this.groupBox2.Controls.Add(this.ALV);
            this.groupBox2.Controls.Add(this.label5);
            this.groupBox2.Controls.Add(this.APROP);
            this.groupBox2.Controls.Add(this.label4);
            this.groupBox2.Controls.Add(this.ANAME);
            this.groupBox2.Controls.Add(this.AID);
            this.groupBox2.Controls.Add(this.label2);
            this.groupBox2.Controls.Add(this.APIC);
            this.groupBox2.Location = new System.Drawing.Point(13, 20);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(262, 98);
            this.groupBox2.TabIndex = 0;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "A路线";
            this.groupBox2.Enter += new System.EventHandler(this.groupBox2_Enter);
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(110, 41);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(53, 12);
            this.label7.TabIndex = 11;
            this.label7.Text = "(道具名)";
            // 
            // ALV
            // 
            this.ALV.Location = new System.Drawing.Point(156, 65);
            this.ALV.Name = "ALV";
            this.ALV.Size = new System.Drawing.Size(45, 21);
            this.ALV.TabIndex = 10;
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(111, 68);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(35, 12);
            this.label5.TabIndex = 9;
            this.label5.Text = "等级:";
            // 
            // APROP
            // 
            this.APROP.Location = new System.Drawing.Point(156, 15);
            this.APROP.Name = "APROP";
            this.APROP.Size = new System.Drawing.Size(92, 21);
            this.APROP.TabIndex = 8;
            this.APROP.MouseClick += new System.Windows.Forms.MouseEventHandler(this.APROP_MouseClick);
            this.APROP.TextChanged += new System.EventHandler(this.APROP_TextChanged);
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(111, 18);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(35, 12);
            this.label4.TabIndex = 7;
            this.label4.Text = "道具:";
            // 
            // ANAME
            // 
            this.ANAME.AutoSize = true;
            this.ANAME.Location = new System.Drawing.Point(9, 41);
            this.ANAME.Name = "ANAME";
            this.ANAME.Size = new System.Drawing.Size(41, 12);
            this.ANAME.TabIndex = 6;
            this.ANAME.Text = "宠物名";
            // 
            // AID
            // 
            this.AID.Location = new System.Drawing.Point(54, 15);
            this.AID.Name = "AID";
            this.AID.ReadOnly = true;
            this.AID.Size = new System.Drawing.Size(45, 21);
            this.AID.TabIndex = 5;
            this.AID.MouseClick += new System.Windows.Forms.MouseEventHandler(this.AID_MouseClick);
            this.AID.TextChanged += new System.EventHandler(this.AID_TextChanged);
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(9, 18);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(47, 12);
            this.label2.TabIndex = 4;
            this.label2.Text = "宠物ID:";
            // 
            // APIC
            // 
            this.APIC.Location = new System.Drawing.Point(54, 44);
            this.APIC.Name = "APIC";
            this.APIC.Size = new System.Drawing.Size(50, 50);
            this.APIC.TabIndex = 3;
            this.APIC.TabStop = false;
            // 
            // 宠物ID
            // 
            this.宠物ID.DataPropertyName = "PN";
            this.宠物ID.HeaderText = "宠物ID";
            this.宠物ID.Name = "宠物ID";
            this.宠物ID.ReadOnly = true;
            // 
            // A路线宠物
            // 
            this.A路线宠物.DataPropertyName = "AN";
            this.A路线宠物.HeaderText = "A路线宠物";
            this.A路线宠物.Name = "A路线宠物";
            this.A路线宠物.ReadOnly = true;
            // 
            // A路线道具
            // 
            this.A路线道具.DataPropertyName = "APN";
            this.A路线道具.HeaderText = "A路线道具";
            this.A路线道具.Name = "A路线道具";
            this.A路线道具.ReadOnly = true;
            // 
            // A路线等级
            // 
            this.A路线等级.DataPropertyName = "ALV";
            this.A路线等级.HeaderText = "A路线等级";
            this.A路线等级.Name = "A路线等级";
            this.A路线等级.ReadOnly = true;
            // 
            // B路线宠物
            // 
            this.B路线宠物.DataPropertyName = "BN";
            this.B路线宠物.HeaderText = "B路线宠物";
            this.B路线宠物.Name = "B路线宠物";
            this.B路线宠物.ReadOnly = true;
            // 
            // B路线道具
            // 
            this.B路线道具.DataPropertyName = "BPN";
            this.B路线道具.HeaderText = "B路线道具";
            this.B路线道具.Name = "B路线道具";
            this.B路线道具.ReadOnly = true;
            // 
            // B路线等级
            // 
            this.B路线等级.DataPropertyName = "BLV";
            this.B路线等级.HeaderText = "B路线等级";
            this.B路线等级.Name = "B路线等级";
            this.B路线等级.ReadOnly = true;
            // 
            // 宠物进化路线
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(318, 480);
            this.Controls.Add(this.选中的宠物路线);
            this.Controls.Add(this.button1);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.textBox1);
            this.Controls.Add(this.dataGridView1);
            this.Name = "宠物进化路线";
            this.Text = "宠物进化路线";
            this.Load += new System.EventHandler(this.宠物进化路线_Load);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView1)).EndInit();
            this.groupBox1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView2)).EndInit();
            this.选中的宠物路线.ResumeLayout(false);
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.BPIC)).EndInit();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.APIC)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.DataGridView dataGridView1;
        private System.Windows.Forms.DataGridViewTextBoxColumn 宠物名字;
        private System.Windows.Forms.DataGridViewTextBoxColumn 宠物序号;
        private System.Windows.Forms.DataGridViewTextBoxColumn 系别;
        private System.Windows.Forms.TextBox textBox1;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.DataGridView dataGridView2;
        private System.Windows.Forms.Button button1;
        private System.Windows.Forms.GroupBox 选中的宠物路线;
        private System.Windows.Forms.Button button2;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label ANAME;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.PictureBox APIC;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.Label BNAME;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.PictureBox BPIC;
        public System.Windows.Forms.TextBox ALV;
        public System.Windows.Forms.TextBox APROP;
        public System.Windows.Forms.TextBox AID;
        public System.Windows.Forms.TextBox BLV;
        public System.Windows.Forms.TextBox BPROP;
        public System.Windows.Forms.TextBox BID;
        private System.Windows.Forms.DataGridViewTextBoxColumn 宠物ID;
        private System.Windows.Forms.DataGridViewTextBoxColumn A路线宠物;
        private System.Windows.Forms.DataGridViewTextBoxColumn A路线道具;
        private System.Windows.Forms.DataGridViewTextBoxColumn A路线等级;
        private System.Windows.Forms.DataGridViewTextBoxColumn B路线宠物;
        private System.Windows.Forms.DataGridViewTextBoxColumn B路线道具;
        private System.Windows.Forms.DataGridViewTextBoxColumn B路线等级;
    }
}