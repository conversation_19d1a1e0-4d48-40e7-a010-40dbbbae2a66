﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using 怪物信息 = Shikong.Pokemon2.PCG.MonsterInfo;
using 用户信息 = Shikong.Pokemon2.PCG.UserInfo;
using 宠物信息 = Shikong.Pokemon2.PCG.PetInfo;
using 宠物类型 = Shikong.Pokemon2.PCG.PetConfig;
using 道具信息 = Shikong.Pokemon2.PCG.PropInfo;
using 地图信息 = Shikong.Pokemon2.PCG.MapInfo;
using 装备类型 = Shikong.Pokemon2.PCG.EquipmentType;
using 装备信息 = Shikong.Pokemon2.PCG.EquipmentInfo;
using 道具类型 = Shikong.Pokemon2.PCG.PropType;
using 道具具体信息 = Shikong.Pokemon2.PCG.PropConfig;
using 数据处理 = Shikong.Pokemon2.PCG.DataProcess;
using Shikong.Pokemon2.PCG;
using PetShikongTools;
using Newtonsoft.Json;
using System.Diagnostics;

namespace Admin
{
    public partial class editor : Form
    {
        public editor()
        {
            InitializeComponent();
        }
        List<道具类型> 所有道具=new List<道具类型>();
        string 道具使用类型;
        道具类型 现编辑道具 = new 道具类型();
        道具具体信息 现编辑道具具体信息 = new 道具具体信息();
        private void textBox1_TextChanged(object sender, EventArgs e)
        {

        }

        private void button1_Click(object sender, EventArgs e)
        {
            dataGridView1.Rows.Clear();
            所有道具 = new 数据处理().ReadAllPropTypes();
            foreach(道具类型 道具 in 所有道具)
            {
                string[] str = { 道具.道具序号, 道具.道具名字,道具.道具图标 };
                dataGridView1.Rows.Add(str);
            }
            label4.Text = "道具数：" + dataGridView1.Rows.Count;


        }

        private void button2_Click(object sender, EventArgs e)
        {
            string[] str = { dataGridView1.Rows[dataGridView1.CurrentRow.Index].Cells["num"].Value.ToString(), dataGridView1.Rows[dataGridView1.CurrentRow.Index].Cells["name"].Value.ToString(),"1"};
            dataGridView2.Rows.Add(str);
            
        }

        private void button3_Click(object sender, EventArgs e)
        {
            dataGridView2.Rows.Remove(dataGridView2.CurrentRow);
        }

        private void button5_Click(object sender, EventArgs e)
        {
            现编辑道具.道具名字 = dataGridView1.Rows[dataGridView1.CurrentRow.Index].Cells["name"].Value.ToString();
            现编辑道具.道具序号 = dataGridView1.Rows[dataGridView1.CurrentRow.Index].Cells["num"].Value.ToString();
            现编辑道具具体信息 = ReadPropScript(现编辑道具.道具序号);
            textBox1.Text = 现编辑道具具体信息.道具脚本;
            label2.Text="现编辑道具："+ 现编辑道具.道具名字 + "("+ 现编辑道具.道具序号 + ")";


        }

        private void button7_Click(object sender, EventArgs e)
        {
            textBox1.Text = textBox1.Text.Replace("\r\n", "");
            if (textBox1.Text != "")
            {
                string[] array = textBox1.Text.Split('|');
                string[] array2;
                
                int i = 0;
                int 脚本类型 = 0;
                int jc = 0;
                int nextNum = 1;
                int jbi = 0;
                int counti = 0;
                foreach(string 字符 in array)
                {
                    
                    if (i == 0)
                    {
                        道具使用类型 = 字符;
                        if (字符 == "一定概率获得" || 字符 == "随机获得" || 字符== "抽奖获得")
                        {
                            脚本类型 = 1;
                            dataGridView2.Rows.Clear();
                            i++;

                        }
                        else if (字符 == "获得多个道具")
                        {
                            脚本类型 = 2;
                            dataGridView2.Rows.Clear();
                            i++;
                        }
                        else
                        {
                            return;
                        }
                        
                    }
                    else
                    {
                        if (脚本类型 == 1)
                        {
                            array2 = 字符.Split(',');
                            string id = array2[0];
                            string num = "1";
                            if (id.Contains("*"))
                            {
                                var ids = id.Split('*');
                                id = ids[0];
                                num = ids[1];
                            }
                            string propName = 获取道具名称(id);
                            if (num != "1") propName += "*" + num;
                            if (array2.Length > 1)
                            {
                               
                               
                          
                                string[] str = { array2[0], propName, array2[1] };

                                dataGridView2.Rows.Add(str);
                            }
                            else
                            {
                                string[] str = { array2[0], propName, "1" };

                                dataGridView2.Rows.Add(str);
                            }
                        }
                        else if (脚本类型 == 2)
                        {
                            jbi++;

                            if (jbi== nextNum)
                            {
                                jc++;
                                nextNum = nextNum + 2;
                                string[] str = { 字符 , 获取道具名称(字符), array[counti+1] };

                                dataGridView2.Rows.Add(str);
                            }
                        }

                    }
                    counti++;
                }
            }

            if (道具使用类型 != "一定概率获得物品") calc();
        }
        public static int sum = 0;
        public void calc()
        {
            //计算sum
            sum = 0;
            for (int i = 0; i < dataGridView2.Rows.Count; i++)
            {
                sum = sum + Convert.ToInt32(dataGridView2.Rows[i].Cells[2].Value);
            }
            for (int i = 0; i < dataGridView2.Rows.Count; i++)
            {
                var value = dataGridView2.Rows[i].Cells[2].Value;
                dataGridView2.Rows[i].Cells[3].Value = Math.Round(Convert.ToDouble(value) / sum * 100, 8) + "%";
            }
        }
        public string 获取道具名称(string 道具编号)
        {
            foreach(道具类型 道具 in 所有道具)
            {
                if (道具.道具序号.Equals(道具编号))
                {
                    return 道具.道具名字;
                }
            }
            return "";
        }

        private void button6_Click(object sender, EventArgs e)
        {
            if (道具使用类型 != "" && (道具使用类型=="一定概率获得" || 道具使用类型 == "随机获得" || 道具使用类型== "抽奖获得"))
            {
                string 文本=道具使用类型;
                foreach(DataGridViewRow dgvr in dataGridView2.Rows)
                {
                    if (dgvr.Cells[2].Value.ToString() == "1")
                    {
                        文本 = 文本 + "|" + dgvr.Cells[0].Value.ToString();
                    }else
                    {
                        文本 = 文本 + "|" + dgvr.Cells[0].Value.ToString()+","+ dgvr.Cells[2].Value.ToString();
                    }
                    


                }
                textBox1.Text = 文本;
            }
            if (道具使用类型 != "" && (道具使用类型 == "获得多个道具"))
            {
                string 文本 = 道具使用类型;
                foreach (DataGridViewRow dgvr in dataGridView2.Rows)
                {

                    文本 = 文本 + "|" + dgvr.Cells[0].Value.ToString() + "|" + dgvr.Cells[2].Value.ToString();




                }
                textBox1.Text = 文本;
            }
        }

        private void dataGridView1_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            现编辑道具.道具名字 = dataGridView1.Rows[dataGridView1.CurrentRow.Index].Cells["name"].Value.ToString();
            现编辑道具.道具序号 = dataGridView1.Rows[dataGridView1.CurrentRow.Index].Cells["num"].Value.ToString();
            现编辑道具具体信息 = ReadPropScript(现编辑道具.道具序号);
            textBox1.Text = 现编辑道具具体信息.道具脚本;
            textBox3.Text = 现编辑道具具体信息.道具说明;
            label2.Text = "现编辑道具：" + 现编辑道具.道具名字 + "(" + 现编辑道具.道具序号 + ")";
        }
        private PropConfig ReadPropScript(string propId)
        {
            String path
                = 数据处理.PSC_Path + SkCryptography.GetHash.GetStringHash(SkRC4.DES.EncryptRC4((Convert.ToInt32(propId) + 14).ToString(),
                                     T.GetKey(2))) + ".data";
            string 存档 = new 数据处理().ReadFile(path);
            if (string.IsNullOrEmpty(存档)) return null;
            存档 = SkRC4.DES.DecryptRC4(存档, T.GetKey(1)); //这里调用了RC4解密,存档我们是用的RC4进行加密的
            var 信息 = JsonConvert.DeserializeObject<PropConfig>(存档);
            return 信息;
        }
        private void button4_Click(object sender, EventArgs e)
        {
            道具具体信息 具体信息 = new 道具具体信息();
            具体信息.道具序号 = 现编辑道具.道具序号;
            具体信息.道具脚本 = textBox1.Text;
            具体信息.道具说明 = textBox3.Text;
    //        new 数据处理().保存文件(SkRC4.DES.EncryptRC4(new ConvertJson().EntityToJSON(具体信息), @"qiqiwan.2016.2017.2018.2020.2021.2022"), @"PageMain\propTable\"+ GetHash.GetStringHash(SkRC4.DES.EncryptRC4((Int32.Parse(现编辑道具.道具序号) + 14).ToString(), 数据处理.获取密钥(2))) + ".data");
            现编辑道具具体信息 =ReadPropScript(现编辑道具.道具序号);
            textBox1.Text = 现编辑道具具体信息.道具脚本;
            textBox3.Text = 现编辑道具具体信息.道具说明;

        }

        private void button8_Click(object sender, EventArgs e)
        {
            dataGridView1.Rows.Clear();
            foreach (道具类型 道具 in 所有道具)
            {
                if(道具.道具名字 .IndexOf(textBox2.Text)>-1 || 道具.道具序号.IndexOf(textBox2.Text) > -1)
                {
                    string[] str = { 道具.道具序号, 道具.道具名字 ,道具.道具图标};
                    dataGridView1.Rows.Add(str);
                    continue;
                }
                if (checkBox_搜索时包含道具脚本.Checked)
                {
                    if (道具.道具脚本 == null)
                    {
                        if (ReadPropScript(道具.道具序号).道具脚本.Contains(textBox2.Text))
                        {
                            string[] str = { 道具.道具序号, 道具.道具名字 };
                            dataGridView1.Rows.Add(str);
                            continue;
                        }
                    }
                    else if (道具.道具脚本.Contains(textBox2.Text))
                    {
                        string[] str = { 道具.道具序号, 道具.道具名字 };
                        dataGridView1.Rows.Add(str);
                        continue;
                    }

                }
                if (checkBox_搜索时包含道具说明.Checked)
                {
                    if (道具.道具说明 == null)
                    {
                        if (ReadPropScript(道具.道具序号).道具说明.Contains(textBox2.Text))
                        {
                            string[] str = { 道具.道具序号, 道具.道具名字 };
                            dataGridView1.Rows.Add(str);
                            continue;
                        }

                    }
                    else if (道具.道具说明.Contains(textBox2.Text))//这里是在线道具
                    {
                        string[] str = { 道具.道具序号, 道具.道具名字 };
                        dataGridView1.Rows.Add(str);
                        continue;
                    }

                }

            }
            label4.Text = "道具数：" + dataGridView1.Rows.Count;
        }

        private void editor_Load(object sender, EventArgs e)
        {
            道具使用类型 = "一定概率获得";
        }

        private void comboBox1_SelectedIndexChanged(object sender, EventArgs e)
        {
            道具使用类型 = comboBox1.Text;
        }

        private void button_更新道具说明_Click(object sender, EventArgs e)
        {
            if (现编辑道具具体信息.道具序号 != null)
            {
                现编辑道具具体信息.道具说明 = textBox3.Text;
                new DataProcess().SaveFile(
                SkRC4.DES.EncryptRC4(new ConvertJson().EntityToJson(现编辑道具具体信息), "qiqiwan.2016.2017.2018.2020.2021.2022"),
                DataProcess.pf + DataProcess.PSC_Path + @"\" +
                SkCryptography.GetHash.GetStringHash(SkRC4.DES.EncryptRC4((int.Parse(现编辑道具具体信息.道具序号) + 14).ToString(), "cankusb.1234.5678.9012.3456.7890.abcd")) +
                ".data");
                MessageBox.Show("更新成功");
            }
            
        }

        private void button_更新道具脚本_Click(object sender, EventArgs e)
        {
            if (现编辑道具具体信息.道具序号 != null)
            {
                if (MessageBox.Show("是否更新脚本?","更新",MessageBoxButtons.OKCancel)==DialogResult.OK)
                {
                    现编辑道具具体信息.道具脚本 = textBox1.Text;
                    new DataProcess().SaveFile(
                    SkRC4.DES.EncryptRC4(new ConvertJson().EntityToJson(现编辑道具具体信息), "qiqiwan.2016.2017.2018.2020.2021.2022"),
                    DataProcess.pf + DataProcess.PSC_Path + @"\" +
                    SkCryptography.GetHash.GetStringHash(SkRC4.DES.EncryptRC4((int.Parse(现编辑道具具体信息.道具序号) + 14).ToString(), "cankusb.1234.5678.9012.3456.7890.abcd")) +
                    ".data");
                    MessageBox.Show("更新成功");
                }


            }
        }

        private void checkBox_搜索时包含道具说明_CheckedChanged(object sender, EventArgs e)
        {

        }

        private void checkBox_搜索时包含道具脚本_CheckedChanged(object sender, EventArgs e)
        {

        }
    }
}
