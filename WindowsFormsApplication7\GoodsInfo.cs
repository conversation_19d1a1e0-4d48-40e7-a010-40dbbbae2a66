﻿namespace Shikong.Pokemon2.PCG
{
    public class GoodsInfo
    {
        public string 商品序号 { get; set; }
        public string 商品价格 { get; set; }
        public string 货币类型 { get; set; }
        public string 道具名字 { get; set; }
        public string 商品数量 { get; set; }
        public string 道具图标 { get; set; }
        /// <summary>
        /// 注意，该字段是给结晶商店的礼盒道具使用的
        /// </summary>
        public string 道具脚本 { get; set; }
        public string 道具介绍 { get; set; }
        /// <summary>
        /// 购买所需的道具ID，购买时扣除。道具ID,扣除数量
        /// </summary>
        public string 需求道具 { get; set; }
        /// <summary>
        /// 购买时赠送的道具，用于结晶礼盒保底，为空不管他。道具ID,赠送数量
        /// </summary>

        public string 附赠道具 { get; set; }

    }
}
