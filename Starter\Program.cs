﻿using System;
using System.Threading;
using System.Windows.Forms;
using download;
using PetShikongTools;

namespace PetShikongMonitor
{
    static class Program
    {
        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>

        

        [STAThread]
        static void Main(string[] args)
        {
            if (args != null && args.Length == 1 && args[0].Equals("ErrorStart"))
            {
                Form1.ErrorStart = true;
            }

            if (args != null && args.Length == 1 && args[0].Equals("StartGameFromTool"))
            {
                Form1.StartGameFromTool = true;
            }

            SkTools.CheckSystemInfos.CheckRuntimeVersion();
            SkTools.CheckSystemInfos.CheckFipsSetting();
          

            using (new Mutex(true, "ShiKongMonitor", out bool createNew))
            {
                if (createNew)
                {
                    try
                    {
                        Application.EnableVisualStyles();
                        Application.SetCompatibleTextRenderingDefault(false);
                        Application.Run(new Form1());

                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show(ex.Message);
                    }
                }
                else
                {
                    // 程序已经运行,显示提示后退出
                    MessageBox.Show("游戏已运行");
                }
            }               
        }

        
    }
}
