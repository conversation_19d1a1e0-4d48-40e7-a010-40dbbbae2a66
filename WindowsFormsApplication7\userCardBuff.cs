﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shikong.Pokemon2.PCG
{
    public class userCardBuff
    {
        /*
         * -人物信息-
         * 金币上限
         * 元宝上限
         * 水晶上限
         * -挂机-
         * 赫拉神殿小怪额外伤害
         * 赫拉神殿BOSS额外伤害
         * 必遇BOSS次数减少
         * 转换战斗胜利经验
         * -其他-
         * 批量开包
         * 追龙任务
         * 自动涅槃间隔减短
         */


        //最后要对数值加密，以及改变变量名字
        //人物信息
        public string 金币上限 { get; set; } = "0";
        public string 元宝上限 { get; set; } = "0";
        public string 水晶上限 { get; set; } = "0";
        //挂机类
        public string 赫拉神殿额外伤害1 {get;set;}="0";//小怪 - 此特权前置要求：星辰VIP
        public string 赫拉神殿额外伤害2 { get; set; } = "0";//BOSS - 此特权前置要求：星辰VIP
        public string 必遇BOSS次数减少 { get; set; } = "0";
        public string 转换战斗胜利经验 { get; set; } = "0.0";//获得x%的经验添加到自动涅槃经验里
        //商店-先不弄

        //其他
        public bool 批量开包 { get; set; } = false;
        public bool 追龙任务 { get; set; } = false;
        public string 自动涅槃间隔减短 { get; set; } = "0";
        public string 幸运值 { get; set; } = "0";//提高中奖概率，默认为0

    }
}
