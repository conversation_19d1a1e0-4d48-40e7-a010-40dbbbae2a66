﻿using Newtonsoft.Json;
using Shikong.Pokemon2.PCG;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Windows.Forms;
using 怪物信息 = Shikong.Pokemon2.PCG.MonsterInfo;
using 用户信息 = Shikong.Pokemon2.PCG.UserInfo;
using 宠物信息 = Shikong.Pokemon2.PCG.PetInfo;
using 宠物类型 = Shikong.Pokemon2.PCG.PetConfig;
using 道具信息 = Shikong.Pokemon2.PCG.PropInfo;
using 地图信息 = Shikong.Pokemon2.PCG.MapInfo;
using 装备类型 = Shikong.Pokemon2.PCG.EquipmentType;
using 装备信息 = Shikong.Pokemon2.PCG.EquipmentInfo;
using Shikong.Pokemon2.PCG.占卜屋;
using static PetShikongTools.SkCryptography;
using PetShikongTools;
using System.Management;
using Shikong.Pokemon2.PCG.时空屋;
using Shikong.Pokemon2.PCG.装备宝石;
using static System.Net.Mime.MediaTypeNames;
using ShikongPlus.Pokemon2.PCG.时空屋;
using System.ComponentModel;
using System.Diagnostics;

namespace Admin
{
    public partial class 管理 : Form
    {
        static List<long> LevelExpList = new List<long>();//宠物等级配置
        //key1 = qiqiwan.2016.2017.2018.2020.2021.2022
        //key2 = cankusb.1234.5678.9012.3456.7890.abcd
        public 管理()
        {
            InitializeComponent();
        }
        String 文件名 = "";
        int i = -1;
        string key = @"qiqiwan.2016.2017.2018.2020.2021.2022";
        private void comboBox1_SelectedIndexChanged(object sender, EventArgs e)
        {

            /***
             宠物配置
             怪物配置
             道具配置
             人物存档
             宠物存档
             道具存档
             **/
            if (comboBox1.Text.Equals("宠物配置"))
            {

                文件名 = DataProcess.PDC_Path;
                i = -1;
            }
            else if (comboBox1.Text.Equals("怪物配置"))
            {

                文件名 = DataProcess.MTDC_Path;
                i = -1;
            }
            else if (comboBox1.Text.Equals("道具配置"))
            {

                文件名 = DataProcess.PPDC_Path;
                i = -1;
            }
            else if (comboBox1.Text.Equals("卡牌配置"))
            {

                文件名 = "PageMain/pet_ci.dat";
                i = -1;
            }
            else if (comboBox1.Text.Equals("宝石配置"))
            {
                文件名 = "PageMain/pet_g.dat";
                i = -1;
            }
            else if (comboBox1.Text.Equals("人物存档"))
            {

                文件名 = DataProcess.PF_Path;
                i = 0;
            }

            else if (comboBox1.Text.Equals("道具存档"))
            {
                i = 1;
                文件名 = DataProcess.PF_Path;
            }


            else if (comboBox1.Text.Equals("宠物存档"))
            {

                文件名 = DataProcess.PF_Path;
                i = 2;
            }
            else if (comboBox1.Text.Equals("装备存档"))
            {

                文件名 = DataProcess.PF_Path;
                i = 4;
            }
            else if (comboBox1.Text.Equals("副本进度存档"))
            {

                文件名 = DataProcess.PF_Path;
                i = 5;
            }
            else if (comboBox1.Text.Equals("任务存档"))
            {

                文件名 = DataProcess.PF_Path;
                i = 6;
            }
            else if (comboBox1.Text.Equals("存档版本号"))
            {

                文件名 = DataProcess.PF_Path;
                i = 3;
            }
            else if (comboBox1.Text.Equals("地图定义存档"))
            {
                i = -1;
                文件名 = DataProcess.MDC_Path + "_" + 地图ID.Text + "map_.data";
            }
            else if (comboBox1.Text.Equals("地图怪物定义存档"))
            {
                i = -1;
                文件名 = DataProcess.MDC_Path + "_" + 地图ID.Text + "pet_.data";
            }
            else if (comboBox1.Text.Equals("技能配置"))
            {
                i = -1;
                文件名 = DataProcess.SDC_Path;
            }
            else if (comboBox1.Text.Equals("神兵设定配置"))
            {

                文件名 = shenbing.SB_Path;
                i = -1;
            }
            else if (comboBox1.Text.Equals("魂器设定配置"))
            {

                文件名 = hunqi.HQ_Path;
                i = -1;
            }
            else if (comboBox1.Text.Equals("皮肤设定配置"))
            {

                文件名 = pifu.PF_Path; ;
                i = -1;
            }
            else if (comboBox1.Text.Equals("侍灵设定配置"))
            {
                文件名 = shiling.ZL_Path ;
                i = -1;
            }

            文件.Text = 文件名;
            string 配置 = "";
            if (文件名 == DataProcess.PF_Path)
            {
                配置 = new DataProcess().GetStr();
            }
            else
            {
                配置 = new DataProcess().ReadFile(文件名);
            }
            if (i != -1)
            {
                //存档版本号key="qiqiwan.2016.2017.2018.2020.2021.2022ZNQMCK14FC10B4591C265E"
                string[] st = 配置.Split(new string[] { "O4F89" }, StringSplitOptions.None);
                配置 = 配置.Split(new string[] { "O4F89" }, StringSplitOptions.None)[i];
                string key = @"qiqiwan.2016.2017.2018.2020.2021.2022";
                //qiqiwan.2016.2017.2018.2020.2021.2022ZNQMCK
                if (!DataProcess.old)
                {
                    key = key + "ZNQMCK";
                }
                配置 = SkRC4.DES.DecryptRC4(配置, key + new DataProcess().GetKey1(i));
              


            }
            else
            {
                配置 = SkRC4.DES.DecryptRC4(配置, @"qiqiwan.2016.2017.2018.2020.2021.2022");

            }

            if (i == 1)
            {
                配置 = new CompressJson().UncompressPropJson(配置);
            }

            if (i == 2)
            {
                配置 = new CompressJson().UncompressPetJson(配置);
            }
            if (i != -1) {
                配置 = ConvertJsonString(配置);
            }
            textBox1.Text = 配置;

        }
        private string ConvertJsonString(string str)
        {
            if (comboBox1.Text.Equals("装备存档"))
            {
                //return str;
                str = str.Replace("'", "\": \"");
                //return str;
            }
            if (str == null)
            {
                return "";
            }
            //格式化json字符串
            JsonSerializer serializer = new JsonSerializer();
            TextReader tr = new StringReader(str);
            JsonTextReader jtr = new JsonTextReader(tr);
            object obj = serializer.Deserialize(jtr);
            if (obj != null)
            {
                StringWriter textWriter = new StringWriter();
                JsonTextWriter jsonWriter = new JsonTextWriter(textWriter)
                {
                    Formatting = Formatting.Indented,
                    Indentation = 4,
                    IndentChar = ' '
                };
                serializer.Serialize(jsonWriter, obj);
                return textWriter.ToString();
            }
            else
            {
                return str;
            }
        }
        static string ChangeLan(string text)
        {
            byte[] bs = Encoding.GetEncoding("UTF-8").GetBytes(text);
            bs = Encoding.Convert(Encoding.GetEncoding("GB2312"), Encoding.GetEncoding("UTF-8"), bs);
            return Encoding.GetEncoding("UTF-8").GetString(bs);
        }
      
   
        private void Form1_Load(object sender, EventArgs e)
        {
            DataProcess.AdminMode = 1;
            //MessageBox.Show(DataProcess.GetStringHash(SkRC4.DES.EncryptRC4((Convert.ToInt32("2016101806") + 14).ToString(), DataProcess.获取密钥(2))));
            FORM.Main = this;
            DataProcess.old = false;
            textBox3.Text = new DataProcess().ABC()+"|"+new DataProcess().ABCD();
            ReadLvConfig();
            readGemstne();
            // button6.Location = new Point(0, 0);
        }
        public static List<Gemstone> bsList;
       void readGemstne()
        {
            try
            {
                bsList = Gemstone.getGemstoneList();
                string classType="|";
                foreach(var s in bsList)
                {
                    if (!bs_typeClass.Items.Contains(s.typeClass))
                    {
                        bs_typeClass.Items.Add(s.typeClass);
                    }
                    bs_dataList.Rows.Add(s.typeName);
                }
            }
            catch
            {
                bsList = new List<Gemstone>();
            }
        }

        private void button1_Click(object sender, EventArgs e)
        {
            save();
        }
        public void save() {
            if (i != -1)
            {
                string 总配置 = new DataProcess().GetStr();
                string[] 配置 = 总配置.Split(new string[] { "O4F89" }, StringSplitOptions.None);
                string 存档编辑文本 = textBox1.Text;
                if (comboBox1.Text.Equals("装备存档"))
                {
                    存档编辑文本 = 存档编辑文本.Replace("\": \"", "'");
                }
                配置[i] = SkRC4.DES.EncryptRC4(存档编辑文本, @"qiqiwan.2016.2017.2018.2020.2021.2022ZNQMCK");
                string 配置文本 = new DataProcess().JointDataFile(配置, true);


                new DataProcess().SaveFile(配置文本, DataProcess.PF_Path);
            }
            else
            {
                string 配置 = textBox1.Text;
                配置 = SkRC4.DES.EncryptRC4(配置, @"qiqiwan.2016.2017.2018.2020.2021.2022");
                new DataProcess().SaveFile(配置, 文件.Text);
            }
        }
        private void tabControl1_TabIndexChanged(object sender, EventArgs e)
        {
              
        }

        private void tabControl1_Selected(object sender, TabControlEventArgs e)
        {
          //  comboBox1.Text = e.TabPage.Text;
        }

        private void 道具配置_Click(object sender, EventArgs e)
        {
         
        }

        private void button2_Click(object sender, EventArgs e)
        {
            if (道具序号.Text.Contains("992")) {
                MessageBox.Show("添加失败,道具ID中不能包含992!");
                return;
            }
            if (string.IsNullOrEmpty(道具分类.Text))
            {
                MessageBox.Show("选择道具分类!");
                return;
            }
            PropType 道具 = new PropType { 
                道具名字 = 道具名字.Text, 道具图标 = 道具图标.Text, 道具序号 = 道具序号.Text, 道具价格 = 出售价格.Text,
                道具脚本 = 道具脚本.Text, 道具说明 = 道具说明.Text,道具分类 = 道具分类.Text
            };
            if (!string.IsNullOrEmpty(数量上限.Text))
            {
                try {
                    int _max = Convert.ToInt32(数量上限.Text);
                    if (_max < 0)
                    {
                        MessageBox.Show("数量上限不能小于0!");
                        return;
                    }
                    道具.数量上限 = 数量上限.Text;
                }
                catch
                {
                    MessageBox.Show("数量上限非整数!");
                    return;
                }
            }

            if (new DataProcess().AddNewProp(道具))
            {
                MessageBox.Show("添加成功!");
            }
            else
            {
                MessageBox.Show("添加失败,道具序号已经存在!");
            }
        }

        private void linkLabel1_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            道具列表 列表 = new 道具列表();
            列表.TEXT = 道具脚本;
            列表.Show();
        }

        private void linkLabel2_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            道具列表 列表 = new 道具列表();
            列表.类型 = "道具名字";
            列表.TEXT = 道具说明;
            列表.Show();
        }

        private void button3_Click(object sender, EventArgs e)
        {
            if(!new DataProcess().ExistOrNot_PropType(this.道具序号2.Text)){
              //  MessageBox.Show("该道具不存在");
             //   return;
            }
            if (道具序号2.Text.IndexOf("|") != -1)
            {
                string[] popList = 道具序号2.Text.Split('|');
                string msg = "";
                for(int i = 0; i < popList.Length; i++)
                {
                    道具信息 信息 = new 道具信息();
                    信息.道具类型ID = popList[i];
                    信息.道具位置 = "1";
                    信息.道具数量 = 添加道具数量.Text;
                    if (new DataProcess().AddPlayerProp(信息,true))
                    {
                        msg += $"添加道具[{new DataProcess().GetPropName(信息.道具类型ID)}]*{添加道具数量.Text} 成功!\r\n";
                    }
                    else
                    {
                        msg += $"添加道具ID{信息.道具类型ID}失败!\r\n";
                    }
                }
                MessageBox.Show(msg,"添加道具");
            }
            else
            {
                道具信息 信息 = new 道具信息();
                信息.道具类型ID = this.道具序号2.Text;
                信息.道具位置 = "1";
                信息.道具数量 = 添加道具数量.Text;
                if (new DataProcess().AddPlayerProp(信息, true))
                {
                    MessageBox.Show("添加成功!");
                }
                else
                {
                    MessageBox.Show("玩家背包格子不够!");
                }
            }
            
        }

        private void linkLabel3_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            道具列表 列表 = new 道具列表();
            列表.类型 = "道具序号";
            列表.TEXT = 道具序号2;
            列表.Show();
        }

        private void linkLabel4_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            宠物列表 列表 = new 宠物列表();
            列表.类型 = "宠物序号";
            列表.TEXT = 宠物序号;
            列表.ShowDialog();
            pictureBox1.ImageLocation = Environment.CurrentDirectory.ToString() + "\\PageMain\\Content\\PetPhoto\\z" + 宠物序号.Text + ".gif";
        }
        internal void ReadLvConfig()
        {
            string cfg =new DataProcess().ReadFile(@"PageMain\shikong.mask");
            cfg = SkRC4.DES.DecryptRC4(cfg, "qiqiwan.2016.2017.2018.2020.2021.2022");
            var 经验 = cfg.Split(new[] { "\r\n" }, StringSplitOptions.None);
            long 当前经验 = 0;
            foreach (string exp in 经验)
            {
                当前经验 = 当前经验 + Convert.ToInt64(exp);
                LevelExpList.Add(当前经验);
            }
        }
        internal string 获取等级经验(int j)
        {
            if (j > LevelExpList.Count)
            {
                j = LevelExpList.Count;
            }

            if (j <= 1)
            {
                return j.ToString();
            }

            return LevelExpList[j - 1].ToString();
        }
        private void button4_Click(object sender, EventArgs e)
        {
            try
            {
                宠物信息 宠物 = new 宠物信息();
                宠物类型 类型 = new DataProcess().GetAppointedPetType(宠物序号.Text);
                宠物.宠物名字 = 类型.宠物名字;
                宠物.形象 = 类型.宠物序号;
                宠物.五行 = 类型.系别;
                宠物.当前经验 = 获取等级经验(Convert.ToInt32(宠物等级.Value) -1);
                if((宠物.当前经验 == "0" || 宠物.当前经验=="") && 宠物等级.Value>1)
                {
                    MessageBox.Show("未获取到等级配置!");
                    return;
                }
                宠物.宠物序号 ="1";
                宠物 = new DataProcess().SetDefaultAttribute(宠物);
                宠物.成长 = 宠物成长.Text;
                var t = new DataProcess().AddPet(宠物, true);
                if (t=="-1")
                {
                    MessageBox.Show("已经满了！");
                }

            }
            catch (Exception ex) {
                MessageBox.Show(ex.Message);
            }
        }

        private void 宠物序号_Leave(object sender, EventArgs e)
        {
            string picpath = Environment.CurrentDirectory.ToString() + "\\PageMain\\Content\\PetPhoto\\z" + 宠物序号.Text + ".gif";
            if (File.Exists(picpath))
            {
                pictureBox1.ImageLocation = picpath;
            }
            else
            {
                picpath= Environment.CurrentDirectory.ToString() + "\\PageMain\\Content\\PetPhoto\\q" + 宠物序号.Text + ".png";
                if (File.Exists(picpath))
                {
                    pictureBox1.ImageLocation = picpath;
                }
                else
                {
                    pictureBox1.ImageLocation = Environment.CurrentDirectory.ToString() + "\\PageMain\\Content\\PetPhoto\\q" + 宠物序号.Text + ".gif";
                }
            }
                
            
        }

        private void tabControl1_SelectedIndexChanged(object sender, EventArgs e)
        {

        }

        private void tabPage2_Click(object sender, EventArgs e)
        {

        }

        private void linkLabel5_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            
            道具列表 列表 = new 道具列表();
            列表.TEXT = 地图掉落列表;
            列表.ShowDialog();
        }

        private void linkLabel8_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            FORM.怪物信息的列表 = 怪物信息列表;
            怪物加入列表 列表 = new 怪物加入列表();
            列表.ShowDialog();
        }
        
        private void button5_Click(object sender, EventArgs e)
        {
            if (new DataProcess().ReadMapInfo(地图序号.Text, true) != null)
            {
                MessageBox.Show("该地图序号已经存在!");
            }
            else
            {

                MapInfo 地图 = new MapInfo();
                地图.地图ID = 地图序号.Text;
                地图.掉落道具 = 地图掉落列表.Text;
                地图.最大掉落 = 地图掉落最大数量.Text;
                地图.最小掉落 = 地图掉落最小数量.Text;
                地图.最小金币 = 地图最小金币.Text;
                地图.最大金币 = 地图最大金币.Text;
                地图.最大元宝 = 地图最大元宝.Text;
                地图.最小元宝 = 地图最小元宝.Text;

                地图.ICO = textBox5.Text;
                if (checkBox2.Checked)
                {
                    地图.Type = "1";
                }
                string 配置 = new ConvertJson().EntityToJson(地图);
                配置 = SkRC4.DES.EncryptRC4(配置, @"qiqiwan.2016.2017.2018.2020.2021.2022");
                new DataProcess().SaveFile(配置, DataProcess.MDC_Path + "_" + 地图.地图ID + "map_.data");
                MessageBox.Show("保存成功!");
            }

        }

        private void linkLabel6_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            怪物列表 列表 = new 怪物列表();
            列表.类型 = "怪物序号";
            列表.TEXT = 怪物序号 ;
            列表.ShowDialog();
        }

        private void button7_Click(object sender, EventArgs e)
        {
            FORM.输入内容 = null;
            输入框 输入 = new 输入框();
            输入.ShowDialog();
            if (怪物信息列表 == null || 怪物信息列表.Count == 0)
            {
                MessageBox.Show("请至少添加一个宠物后再保存!");
                return;
            }
            if (string.IsNullOrEmpty(FORM.输入内容))
            {
                MessageBox.Show("请输入地图编号!否则无法继续!");

            }
            else
            {

                if (new DataProcess().GetAMML(FORM.输入内容, true) == null)
                {
                    string 配置 = new ConvertJson().ListToJson(怪物信息列表);
                    配置 = SkRC4.DES.EncryptRC4(配置, @"qiqiwan.2016.2017.2018.2020.2021.2022");
                    new DataProcess().SaveFile(配置, DataProcess.MDC_Path + "_" + FORM.输入内容 + "pet_.data");
                    MessageBox.Show("保存成功!");
                }
                else
                {
                    MessageBox.Show("该地图已经存在怪物配置!");
                }
            }
        }
        bool 修改 = false;
        int index = -1;
        public List<怪物信息> 怪物信息列表 = new List<怪物信息>();
        private void linkLabel7_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            
             if (new DataProcess().GetMonsterName(怪物序号.Text) == "Null")
             {
                 MessageBox.Show("您填写的怪物序号不存在,请检查!\r\n小提示:最好点击旁边的选择链接来选择序号!");
                 return;
             }
             if (怪物成长.Text.Length == 0) {
                 MessageBox.Show("成长是必填的噢!");
                 return;
             }
             if (怪物序号.Text.Length == 0)
             {
                 MessageBox.Show("序号是必填的噢!");
                 return;
             }
             if (怪物最大等级.Text.Length == 0)
             {
                 MessageBox.Show("最大等级是必填的噢!");
                 return;
             }
             if (怪物最小等级.Text.Length == 0)
             {
                 MessageBox.Show("最小等级是必填的噢!");
                 return;
             }
             if (Convert.ToInt32(怪物最小等级.Text) > Convert.ToInt32(怪物最大等级.Text))
             {
                 MessageBox.Show("最小等级不能比最大等级大!");
                 return;
             }
             if (怪物经验.Text.Length == 0) {
                 怪物经验.Text = "0";
             
             }
             if (怪物掉落.Text.Length == 0)
             {
                 怪物掉落.Text = "无";

             }
            怪物信息 信息 = new 怪物信息();
            信息.掉落道具 = 怪物掉落.Text;
            信息.怪物成长 = 怪物成长.Text;
            信息.最大等级 = 怪物最大等级.Text;
            信息.最小等级 = 怪物最小等级.Text;
            信息.最大掉落 = 怪物最大掉落.Text;
            信息.序号 = 怪物序号.Text;
            信息.经验值 = 怪物经验.Text;
            怪物序号.Text = "";
            if (!修改)
            {
                foreach (怪物信息 怪物 in 怪物信息列表)
                {
                    if (怪物.序号.Equals(怪物序号.Text))
                    {
                        MessageBox.Show("这只怪物已经存在于列表中,同一个地图不能出现两个相同的怪物!");
                        return;
                    }
                }
                怪物信息列表.Add(信息);
                MessageBox.Show("已经加入到列表,请继续编辑!");
            }
            else
            {
                修改 = false;
                怪物信息列表[index] = 信息;
                linkLabel7.Text = "加入列表";
                MessageBox.Show("已经修改到列表,请继续编辑!");

            }
        }
        public void ediInfo(int i) {
            怪物掉落.Text = 怪物信息列表[i].掉落道具;
            怪物成长.Text = 怪物信息列表[i].怪物成长.ToString();
            怪物最大等级.Text = 怪物信息列表[i].最大等级;
            怪物最小等级.Text = 怪物信息列表[i].最小等级;
            怪物最大掉落.Text = 怪物信息列表[i].最大掉落;
            怪物序号.Text = 怪物信息列表[i].序号;
            怪物经验.Text = 怪物信息列表[i].经验值;
            index = i;
            修改 = true;
            linkLabel7.Text = "保存到列表";

        }
        private void button6_Click(object sender, EventArgs e)
        {
            怪物信息列表 = new List<怪物信息>();
            button6.Visible = false;
            修改 = false;
            linkLabel7.Text = "加入列表";
        }

        private void button8_Click(object sender, EventArgs e)
        {
            button6.Visible = true;
        }

        private void tabPage3_Click(object sender, EventArgs e)
        {

        }

        private void linkLabel9_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            道具列表 列表 = new 道具列表();
            列表.TEXT = 怪物掉落;
            列表.ShowDialog();
        }

        private void 怪物序号_TextChanged(object sender, EventArgs e)
        {

        }

        private void linkLabel10_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            宠物列表 列表 = new 宠物列表();
            列表.类型 = "宠物序号";
            列表.TEXT = 道具脚本;
            列表.ShowDialog();
        }

        private void linkLabel11_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            宠物列表 列表 = new 宠物列表();
            列表.类型 = "宠物名字";
            列表.TEXT = 道具说明;
            列表.ShowDialog();
        }

        private void textBox1_DragDrop(object sender, DragEventArgs e)
        {
            i = -1;

            var filenames = (string[])e.Data.GetData(DataFormats.FileDrop);

            using (StreamReader sr = new StreamReader(filenames[0], Encoding.Default))
            {
                文件名 = filenames[0];
                //textBox1.Text = 文件名;
                //根据文件名判断是否为存档文件，若为配置文件则应更改i
                FileInfo fi = new FileInfo(文件名);
                文件.Text = 文件名;
                Text = fi.Name;
                //只要包含Main*.dat就是存档文件，主要是懒得改文件名
                if (文件名.Contains(@"\Main") && 文件名.Contains(".dat"))
                {
                    DataProcess.PF_Path = 文件名;
                    textBox1.Text = "已切换到目录：" + 文件名;
                   
                }
                else
                {
                    textBox1.Text = SkRC4.DES.DecryptRC4(sr.ReadToEnd(), @"qiqiwan.2016.2017.2018.2020.2021.2022");
                }


            }
        }

        private void textBox1_DragEnter(object sender, DragEventArgs e)
        {

            i = -1;
            if (e.Data.GetDataPresent(DataFormats.FileDrop, false) == true)
            {

                var filenames = (string[])e.Data.GetData(DataFormats.FileDrop);

                var hz = filenames[0].LastIndexOf('.') + 1;

                var houzhui = filenames[0].Substring(hz);//文件后缀名 

               

                    e.Effect = DragDropEffects.All;

               



            }
        }

        private void button9_Click(object sender, EventArgs e)
        {
            textBox1.Text = SkRC4.DES.EncryptRC4(textBox1.Text, @"qiqiwan.2016.2017.2018.2020.2021.2022");
        }

        private void button10_Click(object sender, EventArgs e)
        {
            if (textBox3.Text == "") {
                MessageBox.Show("请填写版本号！");
                return;
            }
            string json = "";
            json = textBox3.Text;//版本号
            json += "**\r\n" + 取哈希(@"\PageMain\propTable\", "data");
            json += "\r\n" + 取哈希(@"\PageMain\propTable\e\", "dat");
            json += "\r\n" + 取哈希(@"\PageMain\propTable\e\s\", "dat");
            json += "\r\n" + 取哈希(@"\PageMain\propTable\e\s\", "petshikong");
            json += "\r\n" + 取哈希(@"\PageMain\Malls\", "mall");
            json += "\r\n" + 取哈希(@"\PageMain\map\", "data"); ;
            json += "\r\n" + 取哈希(@"\PageMain\task\", "dat");
            json += "\r\n" + 取哈希(@"\PageMain\task\", "task");
            json += "\r\n" + 取哈希(@"\PageMain\Synthesis\", "ini");
            json += "\r\n" + 取哈希(@"\PageMain\", "html");
            json += "\r\n" + 取哈希(@"\PageMain\", "qingshan");
            json += "\r\n" + 取哈希(@"\PageMain\", "canku");
            json += "\r\n" + 取哈希(@"\PageMain\", "mask");
            json += "\r\n" + 取哈希(@"\PageMain\", "config");
            json += "\r\n" + 取哈希(@"\PageMain\", "js");
            json += "\r\n" + 取哈希(@"\PageMain\", "wad");
            json += "\r\n" + 取哈希(@"\PageMain\map\", "config");
            json += "\r\n" + 取哈希(@"\PageMain\", "dat");//目录下dat的配置
            json += "\r\n" + 取哈希(@"\PageMain\Content\resources\styles\images\4ie\", "cab");
            json += "\r\n" + 取哈希(@"\PageMain\Content\resources\", "db");
            json += "\r\n" + 取哈希(@"\PageMain\Content\Javascript\", "js");
            json += "\r\n" + 取哈希(@"\PageMain\Content\Img\prop\prop\ddt\", "png");
            textBox2.Text = json;
            json = SkRC4.DES.EncryptRC4(json,T.GetKey(1));
            new DataProcess().SaveFile(json, 程序路径 + @"\PageMain\Content\resources\styles\images\4ie\Shikong.Pokemon2.PCG.cfg");
            textBox9.Text = GetFileHash(程序路径 + @"\PageMain\Content\resources\styles\images\4ie\Shikong.Pokemon2.PCG.cfg");

        }
        private string 取哈希(string 路径, string 类型)
        {
            DirectoryInfo dir = new DirectoryInfo(Environment.CurrentDirectory + 路径);
            //path为某个目录，如： “D:\Program Files”
            FileInfo[] inf = dir.GetFiles();
            var 文件 = new List<SkFileCheck.FileCheck>();
            foreach (FileInfo finf in inf)
            {
                if (finf.Extension.Equals("." + 类型))
                {
                    if (finf.Name.Equals("Main.dat")) continue;//碰到存档跳过
                    var 验证 = new SkFileCheck.FileCheck
                    {
                        特征 = GetFileHash(finf.FullName),
                        文件名 = 路径 + finf.Name
                    };
                    文件.Add(验证);
                }
                //如果扩展名为“.xml”

                //读取文件的完整目录和文件名
            }
            String json = "|";
            foreach (SkFileCheck.FileCheck 文 in 文件)
            {
                if (!文.文件名.Contains("test"))
                {
                    json += "\r\n" + 文.文件名 + "|" + 文.特征;
                }
            }
            json = json.Replace("|\r\n", "");
            return json;
        }
        public static String 程序路径 = Environment.CurrentDirectory.ToString();
        public static MD5CryptoServiceProvider md5 = new MD5CryptoServiceProvider();
        public static string GetFileHash(string filePath)
        {
            if (!File.Exists(filePath))
            {
                return "-1";
            }
            using (FileStream fs = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.Read))
            {
                return BitConverter.ToString(md5.ComputeHash(fs)).Replace("-", "");
            }
        }

        private void button11_Click(object sender, EventArgs e)
        {
            var json =  取哈希(@"\PageMain\Content\PetPhoto\", "png");
            json = SkRC4.DES.EncryptRC4(json, T.GetKey(1));
            new DataProcess().SaveFile(json, 程序路径 + @"\PageMain\Content\resources\styles\images\4ie\Shikong.Pokemon3.PCG.cfg");
            textBox2.Text = json;

            textBox9.Text = GetFileHash(程序路径 + @"\PageMain\Content\resources\styles\images\4ie\Shikong.Pokemon3.PCG.cfg");
        }


        private void button12_Click(object sender, EventArgs e)
        {
            装备类型 装备 = new 装备类型();
            装备.ID = 装备_ID.Text;
            装备.抵消 = 装备抵消.Text;
            装备.防御 = 装备防御.Text;
            装备.攻击 = 装备攻击.Text;
            装备.加深 = 装备加深.Text;
            装备.类型 = 装备类型.Text;
            装备.名字 = 装备名字.Text;
            装备.魔法 = 装备魔法.Text;
            装备.闪避 = 装备闪避.Text;
            装备.生命 = 装备生命.Text;
            装备.速度 = 装备速度.Text;
            装备.吸魔 = 装备吸魔.Text;
            装备.吸血 = 装备吸血.Text;
            装备.说明 = textBox4.Text;
            装备.ICO = 装备图标.Text;
            装备.命中 = 装备命中.Text;
            装备.主属性 = 主属性.Text;
            装备.五行限制 = 五行限制.Text;
            if (new DataProcess().AddNewEquipment(装备))
            {
                MessageBox.Show("增加装备成功!");
            }
            else {
                MessageBox.Show("增加装备失败,序号已存在!");
            }

        }

        private void linkLabel12_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            装备列表 列表 = new 装备列表();
            列表.类型 = "ID";
            列表.TEXT = 存档装备ID;
            列表.Show();
        }

        private void tabPage6_Click(object sender, EventArgs e)
        {

        }

        private void button13_Click(object sender, EventArgs e)
        {
            string lv = "0",msg="";
            try
            {
                lv = Convert.ToInt32(存档装备等级.Text).ToString();
            }
            catch
            {
                lv = "0";
            }
            if (存档装备ID.Text.IndexOf("|") == -1)
            {
                装备信息 装备 = new 装备信息();
                装备.ID = "0";
                装备.类ID = 存档装备ID.Text;
                装备.强化 = lv;
                if (new DataProcess().AddPlayerEquipment(装备, true))
                {
                    MessageBox.Show("增加装备成功!");
                }
                else
                {
                    MessageBox.Show("增加装备失败 ,只能拥有120个装备!");
                }
            }
            else
            {
                string[] ed = 存档装备ID.Text.Split('|');
                for(int i = 0; i < ed.Length; i++)
                {
                    if (ed[i] == "" || ed[i] == " ") continue;
                    装备信息 装备 = new 装备信息();
                    装备.ID = "0";
                    装备.类ID = ed[i];
                    装备.强化 = lv;
                    if (new DataProcess().AddPlayerEquipment(装备, true))
                    {
                        msg+=$"添加装备[{ed[i]}]成功!\r\n";
                    }
                    else
                    {
                        msg += $"添加装备[{ed[i]}]失败!\r\n";
                    }
                }
                MessageBox.Show(msg,"增加装备");
            }
            
        }

        private void linkLabel13_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            装备列表 列表 = new 装备列表();
            列表.类型 = "ID";
            列表.TEXT = 道具脚本;
            列表.ShowDialog();
        }

        private void linkLabel14_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            装备列表 列表 = new 装备列表();
            列表.类型 = "装备名字";
            列表.TEXT = 道具说明;
            列表.ShowDialog();
        }

        private void button14_Click(object sender, EventArgs e)
        {
            宠物进化路线 窗口 = new 宠物进化路线();
            窗口.Show();
        }

        private void 宠物成长_TextChanged(object sender, EventArgs e)
        {

        }

        private void button15_Click(object sender, EventArgs e)
        {
            任务管理 窗口 = new 任务管理();
            窗口.Show();
        }

        private void textBox1_TextChanged(object sender, EventArgs e)
        {
        }

        private void button16_Click(object sender, EventArgs e)
        {
            套装管理 窗口 = new 套装管理();
            窗口.Show();
        }

        private void button17_Click(object sender, EventArgs e)
        {
            List<string> 信息 = new DataProcess().GetMapList();
            foreach (string m in 信息)
            {
                string html = new DataProcess().ReadFile(Environment.CurrentDirectory + @"\PageMain\MapInfo\mapInfo.html", true);

                MapInfo 地图 = new DataProcess().ReadMapInfo(m, true);
                if (地图 == null)
                {
                    continue;
                }
                html = html.Replace("{最小元宝}", 地图.最小元宝);

                html = html.Replace("{最大元宝}", 地图.最大元宝);
                html = html.Replace("{地图序号}", 地图.地图ID);
                List<MonsterInfo> 怪物 = new DataProcess().GetAMML(m, true);
                string 怪物列表 = "";
                foreach (MonsterInfo g in 怪物)
                {
                    怪物列表 += "<span>" + g.怪物名字 + "</span>、";
                }
                怪物列表 += "|";
                怪物列表 = 怪物列表.Replace("、|", "");
                html = html.Replace("{怪物列表}", 怪物列表);
                new DataProcess().SaveFile(html, @"PageMain\MapInfo\t" + m + ".html");
            }
        }
        DataProcess 当前玩家 = new DataProcess();
        private void button18_Click(object sender, EventArgs e)
        {
            if (textBox7.Text == "")
                return;
            string 配置 = "";
            文件名 = DataProcess.PSC_Path + GetHash.GetStringHash(SkRC4.DES.EncryptRC4((Convert.ToInt32(textBox7.Text) + 14).ToString(), T.GetKey(2))) + ".data";
            配置 = new DataProcess().ReadFile(文件名);
            配置 = SkRC4.DES.DecryptRC4(配置, T.GetKey(1));
            textBox1.Text = 配置;
            文件.Text = 文件名;
        }

        private void editor_Click(object sender, EventArgs e)
        {
            editor editor = new editor();
            editor.Show();
        }

        private void button18_Click_1(object sender, EventArgs e)
        {
            合成公式编辑 窗口 = new 合成公式编辑();
            窗口.Show();
        }

        private void button19_Click(object sender, EventArgs e)
        {
            装备管理 装备窗口 = new 装备管理();
            装备窗口.Show();
        }

        private void button20_Click(object sender, EventArgs e)
        {
            string tex = "2016092504,30|2016092304,30|2016102301,10|2016110545,15|2016110546,8|2016112102,30|2016120302,20|943025,30|2017061105|2017072203,5|2017082101,10|2016110512,30|2016110513,20|943083|2017061101|943039,20";
            
            System.Diagnostics.Stopwatch sw = new System.Diagnostics.Stopwatch();
            sw.Start();

            int ling = 0;
            int yi = 0;
            int er = 0;
            int san = 0;
            
            for (int i = 0; i < 10000; ++i)
            {
                //int 地图掉落数量 = DataProcess.取人工概率数(0, 2);
                List<string> s = new List<string>();
                获取掉落物品(tex, 1, ref s);
                if (s.Count == 0)
                {
                    ++ling;
                }else if(s.Count == 1)
                {
                    ++yi;
                }else
                {
                    ++er;
                }
                foreach(string si in s)
                {
                    if (si == "943083")
                        ++san;
                    
                }
            }



            Console.WriteLine(san);
            sw.Stop();
            Console.WriteLine(24 * 60 * 60 / 5+"次消耗时间是:" + sw.ElapsedMilliseconds + "MS");
            Console.WriteLine(24 * 60 * 60 / 5+"次的结果是：" +ling+"、"+yi+"、"+ er);
        }
        public Random RandomGenerator = new Random();
        public void 获取掉落物品(string 掉落脚本, int 掉落数, ref List<string> 物品列表)
        {
            

            string[] 指令组 = 掉落脚本.Split('|');
            List<String> 道具 = new List<String>();
            List<String> 道具数 = new List<String>();
            int 总道具数 = 0;
            string[] 小指令组;
            for (int i = 0; i < 指令组.Length; i++)
            {
                小指令组 = 指令组[i].Split(',');
                if (小指令组.Length > 1)
                {
                    道具.Add(小指令组[0]);
                    道具数.Add(小指令组[1]);
                    总道具数 += Convert.ToInt32(小指令组[1]);
                }
                else
                {
                    道具.Add(小指令组[0]);
                    道具数.Add("1");
                    总道具数++;
                }

            }
            string ID="";
            for (int jici = 0; jici < 掉落数; ++jici)
            {
                int 随机数 = RandomGenerator.Next(1, 总道具数+1);
                int 计数 = 0;
                for (int i = 1; i <= 指令组.Length; i++)
                {
                    计数 += Convert.ToInt32(道具数[i - 1]);
                    if (计数 >= 随机数)
                    {
                        ID = 道具[i - 1];
                        break;
                    }
                }

                if (ID == "!@#")
                {
                    //空道具
                    //Console.WriteLine("第"+jici+"次的结果是：无道具");
                }
                else
                {
                    物品列表.Add(ID);
                    //Console.WriteLine("第" + jici + "次的结果是："+ ID);
                }
                


            }

        }

        private void button21_Click(object sender, EventArgs e)
        {
            地图掉落管理 地图窗口 = new 地图掉落管理();
            地图窗口.Show();
        }

        private void button22_Click(object sender, EventArgs e)
        {
            //cell();
        }

        private void button23_Click(object sender, EventArgs e)
        {
            getPROPJSON 窗口 = new getPROPJSON();
            窗口.Show();
        }

        private void button24_Click(object sender, EventArgs e)
        {
            //管理称号 窗口 = new 管理称号();
           // 窗口.Show();
        }

        private void button25_Click(object sender, EventArgs e)
        {
            textBox1.Text = "正在获取";
            textBox1.Text += getRec();
        }
        public String getRec()
        {
            DirectoryInfo dir = new DirectoryInfo(@"C:\log");
            //path为某个目录，如： “c:\Program Files”
            FileInfo[] inf = dir.GetFiles();
            //List<文件验证> 文件 = new List<文件验证>();
            List<DataProcess> allUser = new List<DataProcess>();
            String text = "";
         
            foreach (FileInfo finf in inf)
            {
               
                if (finf.Name.ToLower().Contains("Recharge".ToLower()))
                {
                    try
                    {
                        text += new DataProcess().ReadFile(finf.FullName);
                        
                    }
                    catch (Exception ex)
                    {
                    
                    }
                }

                //如果扩展名为“.xml”

                //读取文件的完整目录和文件名
            }

            return "结果为："+text;

        }

        private void button26_Click(object sender, EventArgs e)
        {
            if (textBox1.Dock == DockStyle.None) {
                textBox1.Dock = DockStyle.Fill;
             
            }
            else {
                textBox1.Dock = DockStyle.None;
            }
            
        }

        private void button27_Click(object sender, EventArgs e)
        {
            if (textBox1.Text.Contains("|"))
            {
                textBox1.Text = SkRC4.DES.EncryptRC4(textBox1.Text, @"qiqiwan.2016.2017.2018.2020.2021.2022CKYY");
            }
            else {
                textBox1.Text = SkRC4.DES.DecryptRC4(textBox1.Text, @"qiqiwan.2016.2017.2018.2020.2021.2022CKYY");

            }
        }

        private void button28_Click(object sender, EventArgs e)
        {

            var dialog = new System.Windows.Forms.FolderBrowserDialog();
            DialogResult result = dialog.ShowDialog();
            if (result == DialogResult.OK)
            {
                getDirectory(dialog.SelectedPath, 2);
                String json = "";
                foreach (SkFileCheck.FileCheck 文 in 文件1)
                {
                    if (!文.文件名.Contains("test") && !文.文件名.ToLower().Contains("main.dat"))
                    {
                        json += "\r\n" + 文.文件名 + "|" + 文.特征;
                    }
                }
                json = json.Replace("|\r\n", "");
                json = json.Replace(dialog.SelectedPath, "");
                json = SkRC4.DES.EncryptRC4(json, T.GetKey(1));
                new DataProcess().SaveFile(json, dialog.SelectedPath + @"\hlist.dat");
                //   textBox2.Text = json;
                MessageBox.Show("完毕");

                textBox9.Text = GetFileHash(dialog.SelectedPath + @"\hlist.dat");
            }
           
        }
        /*
       * 获得指定路径下所有文件名
       * StreamWriter sw  文件写入流
       * string path      文件路径
       * int indent       输出时的缩进量
       */
        public static List<SkFileCheck.FileCheck> 文件1 = new List<SkFileCheck.FileCheck>();
        public static void getFileName(string path, int indent)
        {
           

            DirectoryInfo root = new DirectoryInfo(path);
            foreach (FileInfo f in root.GetFiles())
            {
                var 验证 = new SkFileCheck.FileCheck
                {
                    特征 = GetFileHash(f.FullName),
                    文件名 = f.FullName
                };
                文件1.Add(验证);

            }
            String json = "|";
         
            
        }

        //获得指定路径下所有子目录名
        public static void getDirectory(string path, int indent)
        {
            getFileName(path, indent);
            DirectoryInfo root = new DirectoryInfo(path);
            foreach (DirectoryInfo d in root.GetDirectories())
            {
                getDirectory(d.FullName, indent + 2);
                
            }
        }
        private string 取哈希1(string 路径, string 类型)
        {
            DirectoryInfo dir = new DirectoryInfo(路径);
            //path为某个目录，如： “D:\Program Files”
            FileInfo[] inf = dir.GetFiles();
            var 文件 = new List<SkFileCheck.FileCheck>();

            foreach (FileInfo finf in inf)
            {

                var 验证 = new SkFileCheck.FileCheck
                {
                    特征 = GetFileHash(finf.FullName),
                    文件名 = 路径 + finf.Name
                };
                文件.Add(验证);

                //如果扩展名为“.xml”

                //读取文件的完整目录和文件名
            }
            String json = "|";
            foreach (SkFileCheck.FileCheck 文 in 文件)
            {
                if (!文.文件名.Contains("test"))
                {
                    json += "\r\n" + 文.文件名 + "|" + 文.特征;
                }
            }
            json = json.Replace("|\r\n", "");
            return json;

        }

        private void button29_Click(object sender, EventArgs e)
        {
            textBox1.Text = SkRC4.DES.DecryptRC4(textBox1.Text, @"qiqiwan.2016.2017.2018.2020.2021.2022");
        }

        private void 存档装备ID_TextChanged(object sender, EventArgs e)
        {
            if (存档装备ID.Text[0] == '|' && 存档装备ID.Text.Length>1)
            {
                存档装备ID.Text = 存档装备ID.Text.Substring(1);
            }
        }

        private void button30_Click(object sender, EventArgs e)
        {
            //ManagementObjectSearcher managementObjectSearcher = new ManagementObjectSearcher("SELECT * FROM Win32_PhysicalMedia");
            string text = DataProcess.GetBindingId();
            //using (ManagementObjectCollection.ManagementObjectEnumerator enumerator = managementObjectSearcher.Get().GetEnumerator())
            //{
            //    if (enumerator.MoveNext())
            //    {
            //        text = ((ManagementObject)enumerator.Current)["SerialNumber"].ToString().Trim();
            //    }
            //}
            if(comboBox1.Text != "人物存档")
            {
                int index = 0;
                foreach (var item in comboBox1.Items)
                {
                    if (item.ToString() == "人物存档") break;
                    index++;
                }
                comboBox1.SelectedIndex = index;
            }
            
            var user = JsonConvert.DeserializeObject<用户信息>(textBox1.Text);
            user.NB1 = text;
            user.password = "skong.qs.2022";
            user.论坛ID = "青衫";
            user.版本号 = (Convert.ToInt32(new DataProcess().ABC())-1).ToString();
            user.小版本号 = "1";
            textBox1.Text = JsonConvert.SerializeObject(user);
            save();
        }

        private void 添加魔法卡_Click(object sender, EventArgs e)
        {
            try
            {
                List<CardInfo> cl = new List<CardInfo>();
                cl=JsonConvert.DeserializeObject<List<CardInfo>>(SkRC4.DES.DecryptRC4(new DataProcess().ReadFile(@"PageMain\pet_ci.dat"), "qiqiwan.2016.2017.2018.2020.2021.2022"));

                //string[] sx = { "攻击", "生命", "速度", "防御", "加深", "吸血", "抵消", "魔法", "闪避", "命中" };
                //foreach (var card in cl)
                //{
                //    if(card.upNum <= 1 && sx.Contains(card.upType))
                //    {
                //        card.isPercent = true;
                //    }
                //}
                //string cfg1 = JsonConvert.SerializeObject(cl);
                //cfg1 = SkRC4.DES.EncryptRC4(cfg1, "qiqiwan.2016.2017.2018.2020.2021.2022");
                //new DataProcess().SaveFile(cfg1, @"PageMain\pet_ci.dat");
                //return;
                //==
                CardInfo c = new CardInfo();
                c.name = 卡片名字.Text;
                c.upType = 卡片属性.Text;
                c.upNum = Convert.ToDouble(卡片属性数值.Text);
                c.ICO = 卡片图片.Text+".png";
                
                foreach(var cc in cl)
                {
                    if (cc.name == c.name)
                    {
                        MessageBox.Show("卡片已存在!");
                        return;
                    }
                }
                cl.Add(c);
                string cfg = JsonConvert.SerializeObject(cl);
                cfg = SkRC4.DES.EncryptRC4(cfg, "qiqiwan.2016.2017.2018.2020.2021.2022");
                new DataProcess().SaveFile(cfg, @"PageMain\pet_ci.dat");
                MessageBox.Show("添加成功!");
            }
            catch(Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }


        private void 卡片图片_Leave(object sender, EventArgs e)
        {
            pictureBox2.ImageLocation = @"PageMain\Content\Img\Magic\pai\" + 卡片图片.Text + ".png";
        }

        private void button31_Click(object sender, EventArgs e)
        {
            textBox1.Text = ConvertJsonString(textBox1.Text);
        }

        private void button32_Click(object sender, EventArgs e)
        {
            if (File.Exists("PetShikongPlus.exe"))
            {
                if (File.Exists("PetShikong.exe"))
                {
                    File.Delete("PetShikong.exe");
                }
                File.Move("PetShikongPlus.exe", "PetShikong.exe");
                MessageBox.Show("完成");
            }
            else MessageBox.Show("原文件不存在或者已改名");
        }

        private void button33_Click(object sender, EventArgs e)
        {
            textBox9.Text = GetFileHash(程序路径 + @"\PageMain\Content\resources\styles\images\4ie\Shikong.Pokemon2.PCG.cfg");
        }

        private void 读取ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            string cfg="";
            List<Gemstone> l;

            if (bsList.Count == 0)
            {
                cfg = new DataProcess().ReadFile("PageMain/pet_g.dat");

                cfg = SkRC4.DES.DecryptRC4(cfg, @"qiqiwan.2016.2017.2018.2020.2021.2022");
                l = JsonConvert.DeserializeObject<List<Gemstone>>(cfg);
            }
            else l = bsList;
            
            
            bs_dataList.Rows.Clear();
            foreach(Gemstone s in l)
            {
                bs_dataList.Rows.Add(s.typeName);
            }
        }

        private void bs_add_Click(object sender, EventArgs e)
        {
            if(bsList.FirstOrDefault(c => c.typeName == bs_typeName.Text) != null)
            {
                MessageBox.Show("宝石名称已存在");
                return;
            }
            Gemstone b = new Gemstone();
            b.prop = bs_pid.Text;
            b.typeName = bs_typeName.Text;
            b.upType = bs_upType.Text;
            b.upNum = Convert.ToDouble(bs_upNum.Text);
            b.LV = Convert.ToInt32(bs_LV.Text);
            b.color = bs_color.Text;
            b.typeClass= bs_typeClass.Text;
            b.eType = bs_eType_str.Text.Substring(0, bs_eType_str.TextLength - 1).Split('、');
            string[] s = bs_wxz.Text.Split('|');
            int[] x = new int[s.Length];
            for(int i = 0; i < s.Length; i++) x[i] = Convert.ToInt32(s[i]);
            b.wzs= x;
            bsList.Add(b);
            //MessageBox.Show(SkRC4.DES.EncryptRC4(JsonConvert.SerializeObject(bsList), key));
            //return;
            new DataProcess().SaveFile(SkRC4.DES.EncryptRC4(JsonConvert.SerializeObject(bsList),key), "PageMain/pet_g.dat");
            MessageBox.Show("添加成功");
            读取ToolStripMenuItem_Click(null, null);
        }

        private void bs_eType_SelectedIndexChanged(object sender, EventArgs e)
        {
            bs_eType_str.Text += bs_eType.Text + "、";
        }

        private void label62_Click(object sender, EventArgs e)
        {
            ColorDialog color = new ColorDialog();
            if(color.ShowDialog() == DialogResult.OK)
            {
                bs_color.Text = "#"+color.Color.Name;
            }
        }

        private void button34_Click(object sender, EventArgs e)
        {
            if (comboBox1.Text != "人物存档")
            {
                int index = 0;
                foreach (var item in comboBox1.Items)
                {
                    if (item == "人物存档") break;
                    index++;
                }
                comboBox1.SelectedIndex = index;
            }

            var user = JsonConvert.DeserializeObject<用户信息>(textBox1.Text);
            user.版本号 = (Convert.ToInt32(new DataProcess().ABC()) - 1).ToString();
            user.小版本号 = new DataProcess().ABCD();
            textBox1.Text = JsonConvert.SerializeObject(user);
            save();
        }

        private void label41_Click(object sender, EventArgs e)
        {
            存档装备ID.Text = "";
        }

        private void label7_Click(object sender, EventArgs e)
        {
            道具序号2.Text = "";
        }

        private void 宠物列表_270_Click(object sender, EventArgs e)
        {
            var pet = 获取指定技能倍数的宠物("1.7");
            Clipboard.SetText(pet);
            MessageBox.Show("已复制");

        }
        string 获取指定技能倍数的宠物(string 倍数)
        {
            string pet = "";
            List<PetConfig> cfgs_ = new DataProcess().ReadPetTypeList();
            List<SkillConfig> skill = GetSCList();
            foreach(var p in cfgs_)
            {
                //宠物名字/t技能效果  | 金波姆\t绮梦剑阵：伤害270%、绮梦花语：命中+40%
                string txt = p.宠物名字+"\t、";
                bool _add = false;
                foreach(var k in p.默认技能)
                {
                    var sk = skill.FirstOrDefault(s => s.技能ID.Equals(k));
                    if (sk != null)
                    {
                        if (sk.技能百分比.Equals(倍数))
                        {
                            //满足，添加
                            _add = true;
                        }
                        if (sk.BUFF.Equals("false"))//主动技能
                        {
                            txt += "、" + sk.技能名字 + "：伤害" + 属性内容处理(sk.技能百分比,1);
                        }
                        else//被动技能
                        {
                            txt += "、" + sk.技能名字 + "："+sk.技能附带效果+"+" + 属性内容处理(sk.附带效果增量);
                        }
                    }
                }
                if (_add)
                {
                    pet += txt.Replace("、、", "") + "\r\n";
                }
            }
            return pet;
        }

        /// <summary>
        /// 如果传入的属性不包含小数点则返回整数，否则返回百分比
        /// </summary>
        /// <param name="sx"></param>
        /// <returns></returns>
        string 属性内容处理(string sx,int add=0)
        {
            if (sx == null)
            {
                return null;
            }
            if (sx.IndexOf(".") == -1)
            {
                return sx;
            }
            else
            {
                double num = (Convert.ToDouble(sx)+add) * 100;
                return num.ToString() + "%";
            }

        }
        /// <summary>
        /// 获取技能列表
        /// </summary>
        /// <returns></returns>
        private List<SkillConfig> GetSCList()
        {
            string dat = SkRC4.DES.DecryptRC4(new DataProcess().ReadFile(DataProcess.SDC_Path), GetKey(1));
            List<SkillConfig> cfg = new List<SkillConfig>();
            if (dat != null)
            {
                string[] dats = dat.Split(new[] { "\r\n" }, StringSplitOptions.None);
                foreach (string d in dats)
                {
                    string[] datss = d.Split('，');
                    if (datss.Length < 5) continue;
                    SkillConfig 技能 = new SkillConfig()
                    {
                        技能ID = datss[0],
                        技能名字 = datss[1],
                        技能百分比 = datss[2],
                        技能附带效果 = datss[3],
                        附带效果增量 = datss[4],
                        耗蓝量 = datss[5],

                    };
                    if (datss.Length >= 7) 技能.限制五行 = datss[6];
                    技能.BUFF = 技能.技能附带效果 != "null" ? "true" : "false";
                    cfg.Add(技能);
                    /*else
                    {
                        //Console.Write("配置出错,内容为:" + d);
                    }*/
                }
            }

            return cfg;
        }

        static int abc = 0;
        string GetKey(int keyIndex, bool dat = false,bool old = true)
        {
            abc++;
            string VKey = "HYFMWZS";
        string Key11 = "XGVUSZF";
        string Key12 = "LjIwMTYuMjAxNy4yMDE4LjIwMjAuMjAyMS4yMDIy";
        string Key21 = "JYSWQRT";
        string Key22 = "LjEyMzQuNTY3OC45MDEyLjM0NTYuNzg5MC5hYmNk";
            //Console.WriteLine(abc);
            if (keyIndex == 1)
            {
                if (dat && !old)
                {
                    return SkCryptography.Vigenere.de(Key11, VKey).ToLower() + SkCryptography.Base64.DecodeBase64(Key12) + "ZNQMCK";

                }
                return SkCryptography.Vigenere.de(Key11, VKey).ToLower() + SkCryptography.Base64.DecodeBase64(Key12);
            }

            if (keyIndex == 2)
            {
                return SkCryptography.Vigenere.de(Key21, VKey).ToLower() + SkCryptography.Base64.DecodeBase64(Key22);
            }

            return null;
        }

        private void 宠物列表_280_Click(object sender, EventArgs e)
        {
            var pet = 获取指定技能倍数的宠物("1.8");
            Clipboard.SetText(pet);
            MessageBox.Show("已复制");
        }

        private void 宠物列表_300_Click(object sender, EventArgs e)
        {
            var pet = 获取指定技能倍数的宠物("2.0");
            Clipboard.SetText(pet);
            MessageBox.Show("已复制");
        }

        private void button35_Click(object sender, EventArgs e)
        {
            new 道具配置管理().Show();
        }
    }
}