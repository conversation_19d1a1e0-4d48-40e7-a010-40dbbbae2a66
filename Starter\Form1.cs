﻿using System;
using System.ComponentModel;
using System.Diagnostics;
using System.IO;
using System.Windows.Forms;
using IWshRuntimeLibrary;
using System.Timers;
using PetShikongTools;
using Microsoft.Win32;
using Admin;
using static PetShikongMonitor.SkRC4;

namespace PetShikongMonitor
{
    public partial class Form1 : Form
    {
        public Form1()
        {
            InitializeComponent();
        }

        private static System.Timers.Timer _timer1;
        private static System.Timers.Timer _timer2;
        internal static bool ErrorStart = false;
        /// <summary>
        /// 为true时直接启动游戏
        /// </summary>
        internal static bool StartGameFromTool = false;


        private static void SetTimer1()
        {
            _timer1 = new System.Timers.Timer(300000);
            _timer1.Elapsed += OnTimedEvent1;
            _timer1.AutoReset = true;
            _timer1.Enabled = true;
        }

        private static void SetTimer2()
        {
            _timer2 = new System.Timers.Timer(5000);
            _timer2.Elapsed += OnTimedEvent2;
            _timer2.AutoReset = true;
            _timer2.Enabled = true;
        }

        private static void OnTimedEvent1(object source, ElapsedEventArgs e)
        {
            try
            {
                if (!new Form1().ProveHash()) //|| !GetFileHash(@"支付宝红包.jpg").Equals("CCF37E2891C0790EB2E8EECE237AEEFE"))
                {
                    SkTools.AutoClosedMsgBox.Show("请勿篡改游戏文件！", "严正警告", 2000);
                    Process[] jclb = Process.GetProcesses();
                    foreach (Process jc in jclb)
                    {
                        if (jc.ProcessName.Equals("PetShikongPlus"))
                        {
                            jc.Kill();
                            Environment.Exit(0);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        private static void OnTimedEvent2(object source, ElapsedEventArgs e)
        {
            try
            {
                Process[] jclb = Process.GetProcesses();
                int c = 0;
                foreach (Process jc in jclb)
                {
                    if (jc.ProcessName.Equals("PetShikongPlus"))
                    {
                        c = 1;
                        break;
                    }
                }

                if (c != 1)
                {
                    Environment.Exit(0);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        internal string ReadFile(string name, bool utf8)
        {
            if (!System.IO.File.Exists(name))
            {
                return null;
            }

            string text;
            using (StreamReader sr = new StreamReader(name))
            {
                text = sr.ReadToEnd();
            }

            return text;
        }
        static string err = "";
        internal bool ProveHash()
        {
            string hashtable = @"PageMain\Content\resources\styles\images\4ie\Shikong.Pokemon2.PCG.cfg";
            string hash配置 = ReadFile(hashtable, true);
            if (string.IsNullOrEmpty(hash配置))
            {
                return false;//没有数据返回false
            }
            hash配置 = SkRC4.DES.DecryptRC4(hash配置, T.GetKey(1));
            string[] 临时配置 = hash配置.Split(new[] { "**\r\n" }, StringSplitOptions.None);

            string[] 配置 = 临时配置[1].Split(new[] { "\r\n" }, StringSplitOptions.None);
            foreach (string 节点 in 配置)
            {
                err = "";
                string path = 节点.Split('|')[0];
                string hash = SkCryptography.GetHash.GetFileHash(Environment.CurrentDirectory + path);
                if (!hash.Equals(节点.Split('|')[1]) || hash.Equals("-1"))
                {
                    err = 节点;
                    return false;
                }
            }
            return true;
        }

       

        private void button1_Click(object sender, EventArgs e)
        {
            label1.Text = "正在校验游戏完整性……";
            try
            {
                if (ProveHash()) // && GetFileHash(@"支付宝红包.jpg").Equals("CCF37E2891C0790EB2E8EECE237AEEFE"))
                {
                    label1.Text = "正在检查运行环境……";
                    Process[] jclb = Process.GetProcesses();
                    foreach (Process process in jclb)
                    {
                        if (process.MainWindowTitle.Contains("变速") ||
                            process.MainWindowTitle.ToUpper().Contains("齿轮") ||
                            process.MainWindowTitle.ToUpper().Contains("GEARNT"))
                        {
                            process.Kill();
                            return;
                        }

                        if (process.MainWindowTitle.Contains("加速") && process.MainWindowTitle.Contains("单机"))
                        {
                            process.Kill();
                            return;
                        }
                    }

                    //if (SkTools.CheckSystemInfos.GetSystemDefaultLCID() != 2052)
                    //{
                    //    MessageBox.Show("This Program can only run in the OS which language is zh-CN !");
                    //    return;
                    //}

                    if (!System.IO.File.Exists(@"Newtonsoft.Json.dll"))
                    {
                        label1.Text = "游戏缺失必需动态链接库文件，请重新下载！";
                        return;
                    }

                    try
                    {

                        Process.Start(@"PetShikongPlus.exe", "name:shikong server:danji");
                    }
                    catch (Win32Exception)
                    {
                        MessageBox.Show("找不到游戏主程序！");
                        label1.Text = "游戏启动失败";
                        return;
                    }

                    SetTimer1();//文件Hash检测，这里在游戏主程序里执行了就不在执行
                    SetTimer2();
                    label1.Text = "游戏已启动！";
                    Hide();
                    ShowInTaskbar = false;
                }
                else
                {
                    
                    label1.Text = "游戏文件不是最新的，请更新或者重新下载！";
                    //MessageBox.Show(err);
                }
            }
            catch (Exception e1)
            {
                label1.Text = "游戏文件丢失，请重新下载！";
            }
        }

        //////////////////////////////////////////////////////////

        public void CreateShortCut()
        {
            string desktopPath = Environment.GetFolderPath(Environment.SpecialFolder.Desktop); //得到桌面文件夹
            WshShell shell = new WshShell();
            IWshShortcut shortcut = (IWshShortcut) shell.CreateShortcut(desktopPath + "\\时空单机.lnk");
            shortcut.TargetPath = Process.GetCurrentProcess().MainModule.FileName;
            shortcut.Arguments = ""; // 参数
            shortcut.Description = "时空单机启动器";
            shortcut.WorkingDirectory = AppDomain.CurrentDomain.BaseDirectory; //程序所在文件夹，在快捷方式图标点击右键可以看到此属性
            shortcut.IconLocation = Path.Combine(AppDomain.CurrentDomain.BaseDirectory,
                @"PageMain/Content/resources/favicon.ico"); //图标
            shortcut.WindowStyle = 1;
            shortcut.Save();
        }

        private void BBSLinkLabel_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            const string url = @"http://shikong.info/";

            try
            {
                Process.Start(url);
            }

            catch (Win32Exception)
            {
                Process.Start(url,"iexplore.exe");
            }
        }
        private void ShortCutLinkLabel_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            try
            {
                new Form1().CreateShortCut();
            }
            catch (Exception ex)
            {
                MessageBox.Show("创建快捷方式失败，失败原因：" + ex.Message);
                throw;
            }
        }

        private void UpgradeLogLinkLabel_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            try
            {
                Process.Start(@"更新日志.txt");
            }
            catch
            {
                MessageBox.Show("找不到更新日志!");
            }
        }

        private void HelperLinkLabel_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            try
            {
                Process.Start(@"时空单机新手教程.docx");
            }
            catch (Win32Exception)
            {
                MessageBox.Show("未安装Word文档阅读器！");
            }
        }

        private void SKLJLinkLabel_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            string ApplicationDataPath =  Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
            string TenMinBackup = Path.Combine(ApplicationDataPath, @"PetShikong\Backup\TenMin");
            Process.Start(TenMinBackup);
            return;
        }

        private void SKNewslinkLabel_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            //MessageBox.Show("别戳啊~我知道你想夸青衫~");
            //const string url = @"http://132.232.27.111/";


            //try
            //{
            //    Process.Start(url);
            //}

            //catch (Win32Exception)
            //{
            //    Process.Start(url,"iexplore.exe");
            //}
        }

        private void Form1_Load(object sender, EventArgs e)
        {
            //pictureBox1.ImageLocation = @"PageMain/Content/resources/LuckyDog.dll";
            if (ErrorStart)
            {
                SkTools.AutoClosedMsgBox.Show("下次请从启动器启动游戏哦！", "温馨提示", 5000);
            }
            if (StartGameFromTool)//直接启动游戏
            {
                button1.PerformClick();
            }
        }

        private void button2_Click(object sender, EventArgs e)
        {
            System.Diagnostics.Process.Start("http://game.shikong.info");
        }

        private void button3_Click(object sender, EventArgs e)
        {
            System.Diagnostics.Process.Start("http://pm1.pokegame.net:10000/passport/login.php");

        }
    }
}
