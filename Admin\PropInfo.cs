﻿

public class PropInfo1
{
    /// <summary>
    /// id
    /// </summary>
    public string 道具类型ID { get; set; }
    /// <summary>
    /// xh
    /// </summary>
    public string 道具序号 { get; set; }
    /// <summary>
    /// sl
    /// </summary>
    public string 道具数量 { get; set; }
    /// <summary>
    /// mz
    /// </summary>
    public string 道具名字 { get; set; }
    /// <summary>
    /// tb
    /// </summary>
    public string 道具图标 { get; set; }
    /// <summary>
    /// 1:道具背包 2:仓库
    /// wz
    /// </summary>
    public string 道具位置 { get; set; }

    /// <summary>
    /// jg
    /// </summary>
    public string 道具价格 { get; set; }
    public string 道具脚本 { get; set; }
    public string 道具说明 { get; set; }
}

