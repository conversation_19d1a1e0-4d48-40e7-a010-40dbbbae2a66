﻿using System;
using System.Collections.Generic;
using PetShikongTools;

namespace Shikong.Pokemon2.PCG
{
    internal class Upgrade
    {
    
        internal void UpdateGameData()
        {
            if (string.IsNullOrEmpty(new DataProcess().ReadUserInfo().SID))
            {
                UserInfo user = new DataProcess().ReadUserInfo();
                var guid = Guid.NewGuid().ToString("N");
                user.SID = guid;
                new DataProcess().SaveUserDataFile(user);
            }

            List<PetInfo> allPet = new DataProcess().ReadPlayerPetList();
            foreach (PetInfo pet in allPet)
            {
                if (string.IsNullOrEmpty(pet.境界))
                {
                    pet.境界 = "元神初具";
                }
            }

            new DataProcess().SavePetDateFile(allPet);
        }
        public static String 程序路径 = Environment.CurrentDirectory.ToString();

        internal void CheckVersion()
        {
            UserInfo user = new DataProcess().ReadUserInfo();
            string version = user.版本号;
            string version2 = user.小版本号;
            if (string.IsNullOrEmpty(version))
            {
                SkTools.AutoClosedMsgBox.Show("请找青衫处理存档问题.", "", 2000);
                Tools.ForcedExit("存档版本异常！");
                //user.版本号 = DataProcess.Version;
                //new DataProcess().SaveUserDataFile(user);
            }
            if (string.IsNullOrEmpty(version2))
            {
                user.小版本号 = DataProcess.Version2;
                new DataProcess().SaveUserDataFile(user);
            }
            if (Convert.ToInt16(version)+4 < Convert.ToInt16(new DataProcess().ABC()))//限制版本
            {
                SkTools.AutoClosedMsgBox.Show(Res.RM.GetString("存档版本过低"), Res.RM.GetString("警告"), 2000);
                Tools.ForcedExit("存档版本过低");
            }
            
            if (user.测试版本.Equals("0") && !new DataProcess().ASD())//测试版本
            {
                SkTools.AutoClosedMsgBox.Show(Res.RM.GetString("测试存档"), Res.RM.GetString("警告"), 2000);
                Tools.ForcedExit("该存档为测试存档,无法继续游戏!");
            }
            string onlineVersion = "";
            try
            {
                onlineVersion = new ConvertJson().GetWeb($"{DataProcess.SK_getUrl()}:9696/sk/ver.ini");//检测更新
            }
            catch
            {

            }
            try
            {
                if (!Program.getDebug()) {


                    var hash = new ConvertJson().GetWeb($"{DataProcess.SK_getUrl()}:9696/hash.text");//检测哈希配置的哈希
                    int i = 0;
                    //当没有获取到哈希就重试
                    while (string.IsNullOrEmpty(hash)) {
                        hash = new ConvertJson().GetWeb($"{DataProcess.SK_getUrl()}:9696/hash.text");//检测哈希配置的哈希
                        i++;
                        if (i == 10) {
                            break;//重试十次后跳出循环
                        }
                    }
                    if (string.IsNullOrEmpty(hash)) {
                        SkTools.AutoClosedMsgBox.Show("获取在线配置失败,请检查网络是否正常!", "", 2000);
                        Tools.ForcedExit("获取配置失败（HASH），无法打开。");//加入退出日志
                    }
                    var thisHash = SkCryptography.GetHash.GetFileHash(程序路径 + @"\PageMain\Content\resources\styles\images\4ie\Shikong.Pokemon2.PCG.cfg");
                    if (hash != thisHash && !new DataProcess().getPower())
                    {
                        SkTools.AutoClosedMsgBox.Show("有更新，请更新!\r\n使用小工具下载更新。\r\n若有问题请联系青衫。", "", 2000);
                        Tools.ForcedExit("配置版本(HASH)过低，无法打开。");//加入退出日志
                    }
                }
            }

            catch
            {

            }
            if (onlineVersion == "")
            {
                SkTools.AutoClosedMsgBox.Show(Res.RM.GetString("网络有问题正在重试"), Res.RM.GetString("警告"), 2000);
                try
                {
                    onlineVersion = new ConvertJson().GetWeb($"{DataProcess.SK_getUrl()}:9696/sk/ver.ini");//检测更新
                }
                catch
                {

                }
            }
            string banMap = "";
            try
            {
                banMap = new ConvertJson().GetWeb($"{DataProcess.SK_getUrl()}:9696/sk/banMap.ini");//地图控制

            }
            catch
            {
                banMap = "201901";
            }
            if (banMap == "")
            {
                try
                {
                    banMap = new ConvertJson().GetWeb($"{DataProcess.SK_getUrl()}:9696/sk/banMap.ini");//地图控制
                }
                catch
                {
                    banMap = "23|201901";
                }
            }
            if (banMap == "")
            {
                SkTools.AutoClosedMsgBox.Show(Res.RM.GetString("无法获取活动地图"), Res.RM.GetString("警告"), 2000);
                DataProcess.BanList = DataProcess.banmapID;
                //Tools.ForcedExit("无法获取最新版本");
            }
            DataProcess.BanList = banMap.Split('|');
            if (Convert.ToDouble(new DataProcess().GetDfv()) < 5)//检测旧存档 防止旧版本修改存档
            {
                Tools.ForcedExit("存档版本号过低，无法打开。");//加入退出日志
            }
            if (onlineVersion.Split('|')[0] != DataProcess.Version && !new DataProcess().getPower())
            {
                SkTools.AutoClosedMsgBox.Show(Res.RM.GetString("游戏版本过低"), Res.RM.GetString("警告"), 2000);
                Tools.ForcedExit("检测到新版本！请更新游戏");
            }

            if (onlineVersion.Split('|').Length>=2 && onlineVersion.Split('|')[1] != DataProcess.Version2 && !new DataProcess().getPower())
            {
                SkTools.AutoClosedMsgBox.Show(Res.RM.GetString("主程序有更新"), Res.RM.GetString("警告"), 2000);
                Tools.ForcedExit("检测到有新补丁！请更新主程序");
            }

            if (Convert.ToInt32(version) > Convert.ToInt32(DataProcess.Version) && !new DataProcess().getPower())
            {
                SkTools.AutoClosedMsgBox.Show(Res.RM.GetString("勿回老版"), Res.RM.GetString("警告"), 5000);
                Tools.ForcedExit("尝试回老版本");
            }
            else if (Convert.ToInt32(version) < Convert.ToInt32(DataProcess.Version))
            {
                
                user.版本号 = DataProcess.Version;
                user.小版本号 = DataProcess.Version2;
                new DataProcess().SaveUserDataFile(user);
                //ReceiveUpgradeGift();//每次更新获得一个修炼仙册
            }
            //  先判断大版本在判断小版本号
            if (((DataProcess.Version + "|"+ DataProcess.Version2) != onlineVersion) && !new DataProcess().getPower())
            {
                SkTools.AutoClosedMsgBox.Show("请使用最新的主程序启动游戏!", Res.RM.GetString("警告"), 5000);
                Tools.ForcedExit("尝试回老版本");
            }
            else if (user.版本号 == DataProcess.Version && Convert.ToInt32(user.小版本号) < Convert.ToInt32(DataProcess.Version2))
            {
                user.小版本号 = DataProcess.Version2;
                new DataProcess().SaveUserDataFile(user);
            }
        }

        internal void ReceiveUpgradeGift()
        {
            UserInfo user = new DataProcess().ReadUserInfo();
            PropInfo info = new PropInfo()
            {
                道具类型ID = "2017061401",
                道具位置 = PropLoaction.背包.ToString(),
                道具数量 = 1.ToString()
            };
            new DataProcess().AddPlayerProp(info);
            new DataProcess().SaveUserDataFile(user);
        }

        /*internal void TransferGameData()
        {
            string path = Path.Combine(DataProcess.ProgramPath, DataProcess.PF_Path);
            if (!File.Exists(UpgradeCfg))
            {
                new DataProcess().SaveFile(DataProcess.Version + "|" + path, UpgradeCfg);
            }
            else
            {
                string[] cfgs = new DataProcess().ReadFile(UpgradeCfg).Split('|');
                

                if (DataProcess.Version.Equals(cfgs[0]))
                {
                    new DataProcess().SaveFile(DataProcess.Version + "|" + path, UpgradeCfg);
                }
                else if (Convert.ToInt32(DataProcess.Version) > Convert.ToInt32(cfgs[0]))
                {   
                    if (path.Equals(cfgs[1]))
                    {
                        MessageBox.Show(Res.RM.GetString("覆盖解压"),Res.RM.GetString("提示"));
                        Tools.ForcedExit("覆盖解压");
                    }
                    DialogResult dr = MessageBox.Show(Res.RM.GetString("转移存档"), Res.RM.GetString("提示"),
                        MessageBoxButtons.YesNo);
                    if (dr == DialogResult.Yes)
                    {
                        try
                        {
                            File.Copy(cfgs[1], DataProcess.PF_Path);
                            NativeMethods.AutoClosedMsgBox.Show(Res.RM.GetString("转移成功"), Res.RM.GetString("提示"), 5000);
                            new DataProcess().SaveFile(DataProcess.Version + "|" + path, UpgradeCfg);
                            Tools.ForcedExit("转移成功！");
                        }
                        catch (DirectoryNotFoundException)
                        {
                            Tools.ForcedExit("原存档已被移动!");
                        }
                        catch (FileNotFoundException)
                        {
                            Tools.ForcedExit("原存档已被移动!");
                        }
                        catch (UnauthorizedAccessException)
                        {
                            Tools.ForcedExit("无法访问原存档");
                        }
                        catch (IOException)
                        {
                            Tools.ForcedExit("IO异常");
                        }
                        catch (Exception e)
                        {
                            Tools.ForcedExit(e.Message);
                        }
                    }
                }
            }
        }*/

        
    }
}
