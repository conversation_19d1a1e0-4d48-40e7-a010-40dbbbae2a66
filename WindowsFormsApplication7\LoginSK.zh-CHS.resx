﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAICAAAAEAIACoEAAAFgAAACgAAAAgAAAAQAAAAAEAIAAAAAAAABAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMkAAAD/AAAA/wAAAP8AAAD/AAAA/wAAALkAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAyAAAA/zMXAP+qTQD/dzYA/1UnAP8iDwD/AAAA/wAA
        ADoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD/AAAA//93AP//dwD//3cA/3c4
        AP8AAAD/AAAAeAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAALkAAAD/AAAAzQAAAJEAAAAqAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAP8AAAD//3oA//96
        AP//egD/dzkA/wAAAP8AAAB4AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAB+AAAA/yIRAP8AAAD/AAAA/wAA
        AP8AAADDAAAAfgAAADoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA3wAA
        AP/dbAD//30A//99AP93OgD/AAAA/wAAAH4AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAL0AAAD/u18A/8xn
        AP9mNAD/EQkA/wAAAP8AAAD/AAAA/wAAAN8AAAChAAAAeAAAADoAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAC9AAAA/7tfAP//gQD//4EA/7tfAP8AAAD/AAAAvQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAvQAA
        AP+7YgD//4UA//+FAP//hQD/u2IA/3c+AP8zGwD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAADDAAAAXAAA
        AAAAAAAAAAAAAAAAAL0AAAD/u2IA//+FAP//hQD/u2IA/wAAAP8AAAC9AAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAC9AAAA/7tkAP//iQD//4kA//+JAP//iQD//4kA//+JAP/ddwD/mVIA/3dAAP8zGwD/AAAA/wAA
        AP8AAAD/AAAAXAAAAAAAAAAAAAAAvQAAAP+7ZAD//4kA//+JAP+7ZAD/AAAA/wAAAL0AAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAL0AAAD/u2cA//+NAP//jQD//40A/+6EAP//jQD//40A//+NAP//jQD//40A//+N
        AP//jQD/mVUA/wAAAP8AAACbAAAAAAAAAAAAAAC9AAAA/7tnAP//jQD//40A/7tnAP8AAAD/AAAAvQAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAvQAAAP+7awH//5IB//+SAf//kgH/RCcA/wAAAP8AAAD/RCcA/8x1
        Af//kgH//5IB/8x1Af8iEwD/AAAA/wAAAJEAAADNAAAAkQAAAL0AAAD/u2sB//+SAf//kgH/u2sB/wAA
        AP8AAAC9AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAC9AAAA/7tvAf//lwH//5cB//+XAf9EKAD/AAAA/wAA
        AP8AAAD/u28B//+XAf//lwH/3YMB/wAAAP8AAADfAAAA/wAAAP8AAAD/AAAA/wAAAP+7bwH//5cB//+X
        Af+7bwH/AAAA/wAAAL0AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAL0AAAD/u3IB//+cAf//nAH//5wB/0Qq
        AP8AAAD/AAAAiQAAAP+IUwH//5wB//+cAf//nAH/AAAA/wAAAP9EKgD/zH0B/3dJAP93SQD/RCoA/7ty
        Af//nAH//5wB/7tyAf8AAAD/AAAAvQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAvQAAAP+7dgH//6EB//+h
        Af//oQH/RCsA/wAAAP8AAAB4AAAA/3dLAP//oQH//6EB//+hAf8RCwD/AAAA/3dLAP//oQH//6EB//+h
        Af9mQAD/u3YB//+hAf//oQH/u3YB/wAAAP8AAAC9AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAC9AAAA/7t6
        Af//pgH//6YB//+mAf9ELAD/AAAA/wAAAFYAAAD/VTcA//+mAf//pgH//6YB/0QsAP8AAAD/iFkB//+m
        Af//pgH//6YB/zMhAP+7egH//6YB//+mAf+7egH/AAAA/wAAAL0AAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AL0AAAD/un0B//6rAf/+qwH//qsB/0QuAP8AAAD/AAAARAAAAP9ELgD//qsB//6rAf/+qwH/RC4A/wAA
        AP+6fQH//qsB//6rAf/+qwH/AAAA/7p9Af/+qwH//qsB/7p9Af8AAAD/AAAAvQAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAvQAAAP+6gQH//rAB//6wAf/+sAH/RC8A/wAAAP8AAAD9AAAA/0QvAP/+sAH//rAB//6w
        Af9VOwD/AAAA//6wAf/+sAH//rAB/7qBAf8AAAD/uoEB//6wAf/+sAH/uoEB/wAAAP8AAAC9AAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAC9AAAA/7qFAf/+tQH//rUB//61Af/cnQH/ZkgA/wAAAP8AAAD/AAAA//61
        Af/+tQH//rUB/3dUAP8zJAD//rUB//61Af/+tQH/h2EB/wAAAP+6hQH//rUB//61Af+6hQH/AAAA/wAA
        AL0AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAL0AAAD/uogB//66Af/+ugH//roB//66Af/+ugH/3KEB/4dj
        Af8zJQD//roB//66Af/+ugH/mHAB/0QyAP/+ugH//roB//66Af9EMgD/AAAA/7qIAf/+ugH//roB/7qI
        Af8AAAD/AAAAvQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAvQAAAP+6jAH//r8B//6/Af/+vwH/y5kB/7qM
        Af/+vwH//r8B//6/Af/+vwH//r8B//6/Af/+vwH//r8B//6/Af/+vwH/y5kB/wAAAP8AAAD/uowB//6/
        Af/+vwH/uowB/wAAAP8AAAC9AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAC9AAAA/7qPAf/+wwH//sMB//7D
        Af9ENAD/AAAA/wAAAP8AAAD/RDQA//7DAf/+wwH//sMB//7DAf/+wwH//sMB//7DAf/ttgH/qYIB/3db
        AP/LnAH//sMB//7DAf+6jwH/AAAA/wAAAP8AAAD/AAAAwwAAAJsAAABcAAAAAAAAAL0AAAD/upMB//7I
        Av/+yAL//sgC/0Q1Af8AAAD/AAAA/wAAAP8AAAD//sgC//7IAv/+yAL//sgC//7IAv/+yAL//sgC//7I
        Av/+yAL//sgC//7IAv/+yAL//sgC/8ugAv9ENQH/IhsA/wAAAP8AAAD/AAAA/wAAAP8AAAAAAAAAvQAA
        AP+6lgH//swC//7MAv/+zAL/RDYB/wAAAP8AAABEAAAA/wAAAP/+zAL//swC//7MAv+6lgH/EQ4A/0Q2
        Af93XwH/h20B/7qWAf/LowL//swC//7MAv/+zAL//swC//7MAv/+zAL//swC/7qWAf+YegH/AAAA/wAA
        AAAAAACJAAAA/4dvAf/+0AL//tAC//7QAv9ENwH/AAAA/wAAAKEAAAD/AAAA//7QAv/+0AL//tAC/7qZ
        Af8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP/+0AL//tAC//7QAv/LpgL/VUUB/3dhAf93YQH/d2EB/1VF
        Af8AAAD/AAAAAAAAAHgAAAD/d2MB//7UAv/+1AL//tQC/3djAf8AAAD/AAAA/wAAAP8zKgD//tQC//7U
        Av/+1AL/h3EB/wAAAP8AAACJAAAAeAAAAIkAAAD/AAAA//7UAv/+1AL//tQC/7qbAf8AAAD/AAAA/wAA
        AP8AAAD/AAAA/wAAAOsAAAAAAAAAeAAAAP93ZQH//tgC//7YAv/+2AL//tgC/9y7Av93ZQH/VUgB/8ut
        Av/+2AL//tgC//7YAv9mVgH/AAAA/wAAAGYAAAAAAAAAAAAAAP8AAAD//tgC//7YAv/+2AL/up4B/wAA
        AP8AAAC9AAAAeAAAAHgAAABWAAAAGAAAAAAAAAB4AAAA/3dmAf/+2wL//tsC//7bAv/+2wL//tsC//7b
        Av/+2wL//tsC//7bAv/+2wL/y68C/wAAAP8AAAD9AAAAKgAAAAAAAAAiAAAA/yIdAP/+2wL//tsC//7b
        Av+6oQH/AAAA/wAAAL0AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGYAAAD/ZlkB//7eAv/+3gL/7c8C/1VK
        Af+HdgH/uqMB//7eAv/+3gL/3MAC/3doAf8RDwD/AAAA/wAAAJEAAAAAAAAAAAAAAEQAAAD/RDsB//7e
        Av/+3gL//t4C/7qjAf8AAAD/AAAAvQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAKgAAAP0AAAD/d2kB/3dp
        Af8zLQD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAACnAAAAAAAAAAAAAAAAAAAAMgAA
        AP8zLQD//uEC//7hAv/+4QL/mIcB/wAAAP8AAACbAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOgAA
        AP8AAAD/AAAA/wAAAP8AAADJAAAAiQAAAMMAAAD/AAAA/wAAAN8AAAChAAAAOgAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAA3wAAAP/cxQL//uMC//7jAv9mWwH/AAAA/wAAAGYAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAOgAAAHgAAAB4AAAAOgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAChAAAA/yIeAP93agH/ZlsB/wAAAP8AAAD9AAAAKgAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAoAAAC5AAAA/wAAAP8AAAD/AAAA/QAAACwAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA6AAAAeAAAAGYAAAAqAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAA///wH///8B////Afw//wH8D/8B+AD/APgADwD4AAcA+AADAPgAAAD4AA
        AA+AAAAPgEAAD4BAAA+AQAAPgAAAD4AAAA+AAAAPgAAAD4AAAAGAAAAAgEAAAIAAAADAACAAwABwD8AA
        cA/AAHAPwADwD+AD8B////Af///4P/////8=
</value>
  </data>
</root>