﻿using Shikong.Pokemon2.PCG;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using 测试工具.GameData;

namespace 测试工具
{
    public partial class Main : AntdUI.Window
    {
        public Main()
        {
            InitializeComponent();
        }

        private void Main_Load(object sender, EventArgs e)
        {
            DataProcess.old = false;

            foreach (var k in MapData.MapList)
            {
                Map_select_MapList.Items.Add(k.Key);
            }
        }
        private void button_Log_Clear_Click(object sender, EventArgs e)
        {
            input_Outout.Text = "";
        }
        void Log_Add(string str)
        {
            input_Outout.AppendText(str + "\n");
        }
        private void Map_button_TestDrop_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(Map_select_MapList.Text))
            {
                AntdUI.Message.warn(this,"Need Select Map!",autoClose:1);
                return;
            }
            int num = 0;
            try
            {
                num = Convert.ToInt32(Map_Input_FightNum.Text);
            }
            catch
            {
                AntdUI.Message.warn(this, "Fightnum Erro!", autoClose: 1);
                return;
            }
            if(num < 1)
            {
                AntdUI.Message.warn(this, "Fightnum must be greater than 0!", autoClose: 1);
                return;
            }
            try
            {
                if (!Map_checkbox_TTT.Checked)
                {
                    if (!MapData.MapList.ContainsKey(Map_select_MapList.Text))
                    {
                        AntdUI.Message.warn(this, "MapKey Erro!", autoClose: 1);
                        return;
                    }
                    Log_Add("Test Drop,Need some time.");
                    Fight.DEBUG_MAP = MapData.MapList[Map_select_MapList.Text];
                    Fight.test(num, null);

                }
            }catch(Exception ex)
            {
                AntdUI.Message.warn(this, "Stop Test Fight,Exception:"+ex.Message, autoClose: 2);
                return;
            }
        }

        private void Item_button_TestItem_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(Item_input_PID.Text))
            {
                AntdUI.Message.warn(this, "Need Input ItemID!", autoClose: 1);
                return;
            }
            int num = 0;
            try
            {
                num = Convert.ToInt32(Item_input_UserNum.Text);
            }
            catch
            {
                AntdUI.Message.warn(this, "usenum Erro!", autoClose: 1);
                return;
            }
            if (num < 1)
            {
                AntdUI.Message.warn(this, "Can't do it!", autoClose: 1);
                return;
            }
            try
            {
                if (!Map_checkbox_TTT.Checked)
                {
                    if (!MapData.MapList.ContainsKey(Map_select_MapList.Text))
                    {
                        AntdUI.Message.warn(this, "MapKey Erro!", autoClose: 1);
                        return;
                    }
                    Log_Add("Test Drop,Need some time.");
                    Fight.DEBUG_MAP = MapData.MapList[Map_select_MapList.Text];
                    Fight.test(num, null);

                }
            }
            catch (Exception ex)
            {
                AntdUI.Message.warn(this, "Stop Test Fight,Exception:" + ex.Message, autoClose: 2);
                return;
            }
        }
    }
}
