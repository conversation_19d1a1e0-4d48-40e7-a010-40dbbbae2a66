using Shikong.Pokemon2.PCG.成长突破;

Form1.cs 能量汇聚功能集成代码
================================

请将以下代码添加到Form1.cs中：

方法1：添加using语句（推荐）
在Form1.cs文件顶部的using语句部分添加：
```csharp
using Shikong.Pokemon2.PCG.成长突破;
```

然后在Form1类中添加以下方法：

```C#
/// <summary>
/// 使用聚灵晶石进行能量汇聚
/// </summary>
/// <param name="count">使用数量</param>
/// <returns>操作结果</returns>
public string EnergyGather_UseJulingStone(string count)
{
    try
    {
        int useCount = Convert.ToInt32(count);
        if (useCount <= 0)
        {
            return "使用数量必须大于0！";
        }
        
        // 记录使用前的等级
        var user = new DataProcess().ReadUserInfo();
        int oldLevel = EnergyGather.GetBarrierLevel(user);
        
        // 使用聚灵晶石
        string result = EnergyGather.UseJulingStone(useCount);
        
        // 如果成功，检查是否升级
        if (result.Contains("成功"))
        {
            user = new DataProcess().ReadUserInfo(); // 重新读取用户信息
            int newLevel = EnergyGather.GetBarrierLevel(user);
            
            // 如果升级了，发送特殊消息
            if (newLevel > oldLevel)
            {
                发送游戏公告($"恭喜！神圣结界等级提升至Lv.{newLevel}！");
                
                // 如果达到满级，发送特殊提示
                if (newLevel >= EnergyGather.MAX_LEVEL)
                {
                    发送红色公告("神圣结界已达到满级！现在可以进行成长突破了！");
                }
            }
        }
        
        return result;
    }
    catch (Exception ex)
    {
        return "参数错误：" + ex.Message;
    }
}

/// <summary>
/// 获取能量汇聚信息
/// </summary>
/// <returns>JSON格式的能量汇聚信息</returns>
public string EnergyGather_GetInfo()
{
    try
    {
        var info = EnergyGather.GetEnergyGatherInfo();
        return JsonConvert.SerializeObject(info);
    }
    catch (Exception ex)
    {
        return "{\"error\":\"" + ex.Message + "\"}";
    }
}

/// <summary>
/// 处理recv函数中的能量汇聚相关命令
/// </summary>
/// <param name="command">用户输入的命令</param>
/// <returns>是否处理了命令</returns>
public bool ProcessEnergyGatherCommand(string command)
{
    // 查看神圣结界等级
    if (command.Equals("/神圣结界") || command.Equals("/结界等级"))
    {
        try
        {
            var info = EnergyGather_GetInfo();
            var infoDict = JsonConvert.DeserializeObject<System.Collections.Generic.Dictionary<string, string>>(info);
            
            发送红色公告($"【神圣结界信息】<br>" +
                $"当前等级：Lv.{infoDict["currentLevel"]}<br>" +
                $"当前经验：{infoDict["currentExp"]}/{infoDict["needExp"]}<br>" +
                $"聚灵晶石：{infoDict["julingStoneCount"]}个");
            
            // 如果已满级，显示特殊提示
            if (infoDict["currentLevel"] == "100")
            {
                发送游戏公告("恭喜！您的神圣结界已达到满级，现在可以进行成长突破了！");
            }
        }
        catch
        {
            发送红色公告("获取神圣结界信息失败！");
        }
        return true;
    }
    
    // 使用聚灵晶石
    if (command.Contains("/使用聚灵晶石"))
    {
        string[] parts = command.Split(' ');
        if (parts.Length >= 2)
        {
            try
            {
                string result = EnergyGather_UseJulingStone(parts[1]);
                发送游戏公告(result);
            }
            catch
            {
                发送红色公告("使用聚灵晶石失败！格式：/使用聚灵晶石 数量");
            }
        }
        else
        {
            发送红色公告("使用格式：/使用聚灵晶石 数量");
        }
        return true;
    }
    
    // 查询突破系统帮助
    if (command.Equals("/突破帮助") || command.Equals("/成长突破"))
    {
        发送游戏公告("【成长突破系统】<br>" +
            "1. 能量汇聚：使用聚灵晶石提升神圣结界等级（0-100级）<br>" +
            "2. 成长突破：神圣结界满级后可突破成长上限（需要专属材料）<br>" +
            "3. 种族突破：突破种族值上限，全面提升宠物能力<br>" +
            "4. 境界突破：突破境界等级，解锁更高战斗力<br>" +
            "相关命令：/神圣结界 查看结界等级，/使用聚灵晶石 数量");
        return true;
    }
    
    return false;
} 
```