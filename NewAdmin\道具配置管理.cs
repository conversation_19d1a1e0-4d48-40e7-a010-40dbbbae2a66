﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Reflection.Emit;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using 道具类型 = Shikong.Pokemon2.PCG.PropType;
using 道具具体信息 = Shikong.Pokemon2.PCG.PropConfig;
using 数据处理 = Shikong.Pokemon2.PCG.DataProcess;
using Newtonsoft.Json;
using Shikong.Pokemon2.PCG;
using PetShikongTools;

namespace Admin
{
    public partial class 道具配置管理 : Form
    {
        public 道具配置管理()
        {
            InitializeComponent();
        }
        List<道具类型> 所有道具 = new List<道具类型>();
        string 道具使用类型;
        道具类型 现编辑道具 = new 道具类型();
        道具具体信息 现编辑道具具体信息 = new 道具具体信息();

        private void button1_Click(object sender, EventArgs e)
        {
            所有道具 = new 数据处理().ReadAllPropTypes(true);
            button8.PerformClick();
        }

        private void 道具配置管理_Load(object sender, EventArgs e)
        {
            所有道具 = new 数据处理().ReadAllPropTypes(true);
            button8.PerformClick();
        }

        private void button8_Click(object sender, EventArgs e)
        {
            dataGridView1.Rows.Clear();
            string 分类 = 搜索_道具分类.Text,图标 = 搜索_道具图标.Text;
            List<string> 搜索列表 = (textBox2.Text ?? string.Empty)
        .Split(new[] { "\r\n", "\n" }, StringSplitOptions.RemoveEmptyEntries)
        .Select(s => s.Trim())
        .Where(s => s.Length > 0)
        .Distinct()
        .ToList();
            foreach (道具类型 道具 in 所有道具)
            {
                if(!string.IsNullOrEmpty(分类) || !string.IsNullOrEmpty(图标))
                {
                    // 名称/序号匹配（保持 IndexOf > -1 的行为，避免语义变化）
                    bool flag1 = 搜索列表.Count == 0 ? true : 搜索列表.Any(t =>
                    ((道具.道具名字?.IndexOf(t, StringComparison.CurrentCulture) ?? -1) > -1) ||
                    ((道具.道具序号?.IndexOf(t, StringComparison.CurrentCulture) ?? -1) > -1));

                    // 分类匹配：若未指定分类则不限制；指定了则要求道具分类非空且相等
                    bool flag2 = string.IsNullOrEmpty(分类)
                        ? true
                        : (!string.IsNullOrEmpty(道具.道具分类) && 道具.道具分类.Equals(分类));

                    // 图标匹配：若未指定图标则不限制；指定了则要求相等
                    bool flag3 = string.IsNullOrEmpty(图标)
                        ? true
                        : (道具.道具图标 != null && 道具.道具图标.Equals(图标));
                    if (flag1 && flag2 && flag3)
                    {
                        string[] str = { 道具.道具序号, 道具.道具名字, 道具.道具图标, 道具.道具分类, 道具.数量上限 };
                        dataGridView1.Rows.Add(str);
                        continue;
                    }
                }
                else
                {
                    bool flag1 = false;
                    if (道具ID精确匹配.Checked)
                    {
                        flag1 = 搜索列表.Count == 0 ? true : 搜索列表.Any(t => 道具.道具序号.Equals(t, StringComparison.CurrentCulture));
                    }
                    else
                    {
                        flag1 = 搜索列表.Count == 0 ? true : 搜索列表.Any(t =>
                        ((道具.道具名字?.IndexOf(t, StringComparison.CurrentCulture) ?? -1) > -1) ||
                        (道具ID精确匹配.Checked ? (道具.道具序号.IndexOf(t, StringComparison.CurrentCulture) > -1) : 道具.道具序号.Equals(t, StringComparison.CurrentCulture))
                        );
                    }
                    //在分类和图标不为空的时候以这2个为准
                    if (flag1)
                    {
                        string[] str = { 道具.道具序号, 道具.道具名字, 道具.道具图标, 道具.道具分类, 道具.数量上限 };
                        dataGridView1.Rows.Add(str);
                        continue;
                    }
                    if (checkBox_搜索时包含道具脚本.Checked)
                    {
                        if (道具.道具脚本 == null)
                        {
                            if (ReadPropScript(道具.道具序号).道具脚本.Contains(textBox2.Text))
                            {
                                string[] str = { 道具.道具序号, 道具.道具名字, 道具.道具图标, 道具.道具分类, 道具.数量上限 };
                                dataGridView1.Rows.Add(str);
                                continue;
                            }
                        }
                        else if (道具.道具脚本.Contains(textBox2.Text))
                        {
                            string[] str = { 道具.道具序号, 道具.道具名字, 道具.道具图标, 道具.道具分类, 道具.数量上限 };
                            dataGridView1.Rows.Add(str);
                            continue;
                        }

                    }
                    if (checkBox_搜索时包含道具说明.Checked)
                    {
                        if (道具.道具说明 == null)
                        {
                            if (ReadPropScript(道具.道具序号).道具说明.Contains(textBox2.Text))
                            {
                                string[] str = { 道具.道具序号, 道具.道具名字, 道具.道具图标, 道具.道具分类, 道具.数量上限 };
                                dataGridView1.Rows.Add(str);
                                continue;
                            }

                        }
                        else if (道具.道具说明.Contains(textBox2.Text))//这里是在线道具
                        {
                            string[] str = { 道具.道具序号, 道具.道具名字, 道具.道具图标, 道具.道具分类, 道具.数量上限 };
                            dataGridView1.Rows.Add(str);
                            continue;
                        }

                    }
                }
                

            }
            Text = "道具数：" + dataGridView1.Rows.Count;
        }


        private PropConfig ReadPropScript(string propId)
        {
            String path
                = 数据处理.PSC_Path + SkCryptography.GetHash.GetStringHash(SkRC4.DES.EncryptRC4((Convert.ToInt32(propId) + 14).ToString(),
                                     T.GetKey(2))) + ".data";
            string 存档 = new 数据处理().ReadFile(path);
            if (string.IsNullOrEmpty(存档)) return null;
            存档 = SkRC4.DES.DecryptRC4(存档, T.GetKey(1)); //这里调用了RC4解密,存档我们是用的RC4进行加密的
            var 信息 = JsonConvert.DeserializeObject<PropConfig>(存档);
            return 信息;
        }

        private void button2_Click(object sender, EventArgs e)
        {
            if(dataGridView1.Rows.Count == 0)
            {
                MessageBox.Show("没有数据");
                return;
            }
            foreach(DataGridViewRow row in dataGridView1.Rows)
            {
                string pid = row.Cells[0].Value.ToString();
                道具类型 道具 = 所有道具.FirstOrDefault(i => i.道具序号.Equals(pid));
                if(道具 != null)
                {
                    道具.道具分类 = 道具分类.Text;
                    row.Cells[3].Value = 道具分类.Text;
                }
            }
        }

        private void button3_Click(object sender, EventArgs e)
        {
            if (dataGridView1.Rows.Count == 0)
            {
                MessageBox.Show("没有数据");
                return;
            }
            int maxNum = 0;
            if (!string.IsNullOrEmpty(数量上限.Text))
            {
                try
                {
                    maxNum = Convert.ToInt32(数量上限.Text);
                    if (maxNum < 0)
                    {
                        MessageBox.Show("数量上限不能小于0！");
                        return;
                    }
                }
                catch
                {
                    MessageBox.Show("数量上限不是整数！");
                    return;
                 }
            }
            foreach (DataGridViewRow row in dataGridView1.Rows)
            {
                string pid = row.Cells[0].Value.ToString();
                道具类型 道具 = 所有道具.FirstOrDefault(i => i.道具序号.Equals(pid));
                if (道具 != null)
                {
                    道具.数量上限 = maxNum.ToString();
                    row.Cells[4].Value = maxNum.ToString();
                }
            }

        }

        private void button_更新道具脚本_Click(object sender, EventArgs e)
        {
            if (现编辑道具具体信息.道具序号 != null)
            {
                if (MessageBox.Show("是否更新脚本?", "更新", MessageBoxButtons.OKCancel) == DialogResult.OK)
                {
                    现编辑道具具体信息.道具脚本 = textBox1.Text;
                    new DataProcess().SaveFile(
                    SkRC4.DES.EncryptRC4(new ConvertJson().EntityToJson(现编辑道具具体信息), "qiqiwan.2016.2017.2018.2020.2021.2022"),
                    DataProcess.pf + DataProcess.PSC_Path + @"\" +
                    SkCryptography.GetHash.GetStringHash(SkRC4.DES.EncryptRC4((int.Parse(现编辑道具具体信息.道具序号) + 14).ToString(), "cankusb.1234.5678.9012.3456.7890.abcd")) +
                    ".data");
                    MessageBox.Show("更新成功");
                }


            }
        }

        private void button_更新道具说明_Click(object sender, EventArgs e)
        {
            if (现编辑道具具体信息.道具序号 != null)
            {
                现编辑道具具体信息.道具说明 = textBox3.Text;
                new DataProcess().SaveFile(
                SkRC4.DES.EncryptRC4(new ConvertJson().EntityToJson(现编辑道具具体信息), "qiqiwan.2016.2017.2018.2020.2021.2022"),
                DataProcess.pf + DataProcess.PSC_Path + @"\" +
                SkCryptography.GetHash.GetStringHash(SkRC4.DES.EncryptRC4((int.Parse(现编辑道具具体信息.道具序号) + 14).ToString(), "cankusb.1234.5678.9012.3456.7890.abcd")) +
                ".data");
                MessageBox.Show("更新成功");
            }
        }

        private void dataGridView1_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {
            string pid = dataGridView1.Rows[dataGridView1.CurrentRow.Index].Cells["num"].Value.ToString();
            现编辑道具 = 所有道具.FirstOrDefault(i => i.道具序号.Equals(pid));
            现编辑道具具体信息 = ReadPropScript(现编辑道具.道具序号);
            textBox1.Text = 现编辑道具具体信息.道具脚本;
            textBox3.Text = 现编辑道具具体信息.道具说明;
            label4.Text = "现编辑道具：" + 现编辑道具.道具名字 + "(" + 现编辑道具.道具序号 + ")";
            道具名称新.Text = 现编辑道具.道具名字;
            道具图标新.Text = 现编辑道具.道具图标;
            道具价格.Text = 现编辑道具.道具价格;
        }

        private void button4_Click(object sender, EventArgs e)
        {
            foreach(var i in 所有道具)
            {
                i.道具脚本 = null;
                i.道具说明 = null;
            }
            new DataProcess().SaveFile(
                SkRC4.DES.EncryptRC4(new ConvertJson().ListToJson(所有道具), "qiqiwan.2016.2017.2018.2020.2021.2022"),数据处理.pf + 数据处理.PPDC_Path);
            MessageBox.Show("保存成功");
        }

        private void label1_Click(object sender, EventArgs e)
        {
            textBox2.Text = "";
        }

        private void dataGridView1_Click(object sender, EventArgs e)
        {
            try
            {
                string pid = dataGridView1.Rows[dataGridView1.CurrentRow.Index].Cells["num"].Value.ToString();
                现编辑道具 = 所有道具.FirstOrDefault(i => i.道具序号.Equals(pid));
                现编辑道具具体信息 = ReadPropScript(现编辑道具.道具序号);
                textBox1.Text = 现编辑道具具体信息.道具脚本;
                textBox3.Text = 现编辑道具具体信息.道具说明;
                label4.Text = "现编辑道具：" + 现编辑道具.道具名字 + "(" + 现编辑道具.道具序号 + ")";
                道具名称新.Text = 现编辑道具.道具名字;
                道具图标新.Text = 现编辑道具.道具图标;
                道具价格.Text = 现编辑道具.道具价格;
            }
            catch { }
        }

        private void button5_Click(object sender, EventArgs e)
        {
            if (现编辑道具具体信息.道具序号 != null)
            {
                现编辑道具.道具名字 = 道具名称新.Text;
                try
                {
                    dataGridView1.Rows[dataGridView1.CurrentRow.Index].Cells["name"].Value = 道具名称新.Text;
                }
                catch { }
            }
        }

        private void button6_Click(object sender, EventArgs e)
        {
            if (现编辑道具具体信息.道具序号 != null)
            {
                现编辑道具.道具图标 = 道具图标新.Text;
                try
                {
                    dataGridView1.Rows[dataGridView1.CurrentRow.Index].Cells["道具图标"].Value = 道具名称新.Text;
                }
                catch { }
            }
        }

        private void button7_Click(object sender, EventArgs e)
        {
            if (现编辑道具具体信息.道具序号 != null)
            {
                现编辑道具.道具价格 = 道具价格.Text;
            }
        }
    }
}
