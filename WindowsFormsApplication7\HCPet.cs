﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Newtonsoft.Json;

namespace Shikong.Pokemon2.PCG
{
    public partial class HCPet : Form
    {
        public HCPet()
        {
            InitializeComponent();
        }
        public string value = null;//要指定的神宠,不选择默认随机
        List<PetConfig> petList = new List<PetConfig>();
        private void HCPet_Load(object sender, EventArgs e)
        {
            this.AcceptButton = 确定;
            petList = new DataProcess().GetSPList().Concat(new DataProcess().GetASPList(DataProcess.GetCharacter("A32C6A19"))).ToList();
            foreach (var p in petList)
            {
                dataGridView1.Rows.Add(p.宠物名字,p.系别);
            }
        }

        private void textBox1_TextChanged(object sender, EventArgs e)
        {
            List<PetConfig> petList2 = new List<PetConfig>();
            foreach(var p in petList)
            {
                if (p.宠物名字.IndexOf(textBox1.Text) != -1)
                {
                    petList2.Add(p);
                }
            }
            dataGridView1.Rows.Clear();
            foreach (var p in petList2)
            {
                dataGridView1.Rows.Add(p.宠物名字, p.系别);
            }
        }

        private void 确定_Click(object sender, EventArgs e)
        {
            if(dataGridView1.CurrentRow == null )return;
            int x = dataGridView1.CurrentRow.Index;
            if (x == -1) return;
            PetConfig pet = petList.FirstOrDefault(c => c.宠物名字 == dataGridView1.Rows[x].Cells[0].Value);
            value = SkRC4.DES.EncryptRC4(JsonConvert.SerializeObject(pet), new DataProcess().GetKey(1));//提前加密赋值
            if (pet == null) return;
            if (MessageBox.Show($"确定选择[{pet.宠物名字}]吗","宠物",MessageBoxButtons.OKCancel) == DialogResult.OK)
            {
                if(SkRC4.DES.EncryptRC4(pet.系别, new DataProcess().GetKey(1)) != "A32C" && SkRC4.DES.EncryptRC4(pet.系别, new DataProcess().GetKey(1)) != "A32C6A19")
                {
                    //错误代码0x1 修改系别种族
                    MessageBox.Show(SkRC4.DES.DecryptRC4("DE306E4F19645CFA20DC81", new DataProcess().GetKey(1)), SkRC4.DES.DecryptRC4("DE306E4F", new DataProcess().GetKey(1)), MessageBoxButtons.OK, MessageBoxIcon.Error);
                    value = "";
                    Close();
                }
                if (!petList.Exists(c => c.宠物序号 == pet.宠物序号))//是否修改了宠物序号
                {
                    
                    //错误代码0x2 修改序号
                    MessageBox.Show(SkRC4.DES.DecryptRC4("DE306E4F19645CFA20DC82", new DataProcess().GetKey(1)), SkRC4.DES.DecryptRC4("DE306E4F", new DataProcess().GetKey(1)), MessageBoxButtons.OK, MessageBoxIcon.Error);
                    value = "";
                    Close();
                }
                DialogResult = DialogResult.OK;
                Close();
            }
        }
    }
}
