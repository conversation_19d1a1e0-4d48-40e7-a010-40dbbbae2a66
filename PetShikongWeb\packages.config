﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Antlr" version="3.5.0.2" targetFramework="net461" />
  <package id="bootstrap" version="3.3.7" targetFramework="net461" />
  <package id="jQuery" version="3.3.1" targetFramework="net461" />
  <package id="Microsoft.AspNet.Mvc" version="5.2.4" targetFramework="net461" />
  <package id="Microsoft.AspNet.Mvc.zh-Hans" version="5.2.4" targetFramework="net461" />
  <package id="Microsoft.AspNet.Razor" version="3.2.4" targetFramework="net461" />
  <package id="Microsoft.AspNet.Razor.zh-Hans" version="3.2.4" targetFramework="net461" />
  <package id="Microsoft.AspNet.TelemetryCorrelation" version="1.0.0" targetFramework="net461" />
  <package id="Microsoft.AspNet.Web.Optimization" version="1.1.3" targetFramework="net461" />
  <package id="Microsoft.AspNet.Web.Optimization.zh-Hans" version="1.1.3" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebApi" version="5.2.4" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebApi.Client" version="5.2.4" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebApi.Client.zh-Hans" version="5.2.4" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebApi.Core" version="5.2.4" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebApi.Core.zh-Hans" version="5.2.4" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebApi.HelpPage" version="5.2.4" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebApi.WebHost" version="5.2.4" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebApi.WebHost.zh-Hans" version="5.2.4" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebPages" version="3.2.4" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebPages.zh-Hans" version="3.2.4" targetFramework="net461" />
  <package id="Microsoft.CodeDom.Providers.DotNetCompilerPlatform" version="2.0.0" targetFramework="net461" />
  <package id="Microsoft.Web.Infrastructure" version="*******" targetFramework="net461" />
  <package id="Modernizr" version="2.8.3" targetFramework="net461" />
  <package id="Newtonsoft.Json" version="11.0.1" targetFramework="net461" />
  <package id="System.Diagnostics.DiagnosticSource" version="4.4.1" targetFramework="net461" />
  <package id="WebGrease" version="1.6.0" targetFramework="net461" />
</packages>