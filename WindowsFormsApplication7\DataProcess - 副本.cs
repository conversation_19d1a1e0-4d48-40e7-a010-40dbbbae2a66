﻿using Newtonsoft.Json;
using PetShikongTools;
using Shikong.Pokemon2.PCG.占卜屋;
using Shikong.Pokemon2.PCG.时空屋;
using Shikong.Pokemon2.PCG.装备宝石;
using ShikongPlus.Pokemon2.PCG.龙珠;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Management;
using System.Text; 
using System.Windows.Forms;
using 道具信息 = Shikong.Pokemon2.PCG.PropInfo;
using 道具类型 = Shikong.Pokemon2.PCG.PropType;

// ReSharper disable InconsistentNaming
namespace Shikong.Pokemon2.PCG
{
    public class DataProcess
    {
        internal static string ProgramPath = Environment.CurrentDirectory;
        /// <summary>
        /// 一般情况下这个值为false，如果后台设置了为true则进入自动破坏体验的模式，可以理解为另类的封号 
        /// </summary>
        public static bool openBad = false;
        /// <summary>
        /// 闪退的概率
        /// </summary>
        public static int badProbability = 15;
        /// <summary>
        /// 损坏存档的概率
        /// </summary>
        public static int badDatProbability = 5;
        public static string pf = "";
        public static String[] BanList;//禁止进入的地图列表
        public static String[] BanTask;//禁止完成的任务列表
        public static String[] BanPOP;//禁止使用的道具列表
        public static List<mapNetInfo> mapPropList;
        public static string newmonsterstr = "";
        public static String[] banmapID = { "201901"};
        public static string ptList = "";//在线道具
        public const string PDB_Path = @"PageMain\Pasture_test.html"; //宠物仓库缓存路径
        public const string PDC_Path = @"PageMain\Content\resources\styles\images\4ie\ShiKongTeam.cab"; //宠物定义配置路径
        public const string PPDC_Path = @"PageMain\Content\resources\Thumbs.db"; //道具定义配置路径
        public const string PSC_Path = @"PageMain\propTable\"; //道具脚本配置路径
        public const string EDC_Path = @"PageMain\propTable\e\"; //装备定义配置路径
        public const string TDC_Path = @"PageMain\task"; //任务定义配置路径
        public const string MDC_Path = @"PageMain\map\"; //地图定义配置路径
        public const string AMC_Path = @"PageMain\map\all.config"; //地图全局掉落定义
        public const string MTDC_Path = @"PageMain\pet_01.canku"; //怪物类型定义配置路径
        public const string LDC_Path = @"PageMain\shikong.mask"; //等级定义配置路径
        public const string SSDC_Path = @"PageMain\propTable\e\s\"; //套装定义配置路径
        public const string SDC_Path = @"PageMain\propTable\e\s\2.petshikong"; //技能定义配置路径
        public const string MS_SP_Path = @"PageMain\Malls\_canku.mall"; //神秘商店_元宝道具配置路径
        public const string MS_KP_Path = @"PageMain\Malls\_mask.mall"; //神秘商店_水晶道具配置路径
        public const string MS_XG = @"PageMain\Malls\_shikong.mall"; //商店道具限购配置路径
        public const string 转移_Path = @"PageMain\k.config"; //百炼金丹可转移技能配置
        public static bool loginOK;
        public const string GS_GP_Path = @"PageMain\Malls\_qingshan.mall"; //普通商店_金币道具配置路径
        public static List<AutoMapInfo> AutoMap = new List<AutoMapInfo>();
        //public const string MS_IP_Path = @"PageMain\Malls\_hongzai.mall"; //神秘商店_积分道具配置路径
        public const string GS_PP_Path = @"PageMain\Malls\_ywyh.mall"; //普通商店_威望道具配置路径
        public const string EC_Path = @"PageMain\petInfoTable_a.qingshan"; //进化配置
        public static bool TD = false;
        //public const string SC_Path = @"PageMain\petInfoTable_b.ywyh"; //合成配置
        public const string SC_Path = @"PageMain\Synthesis\synthetsis.ini";
        public static string PF_Path = @"PageMain\Main.dat"; //总存档路径
        public const string Mpass = "ZNQMCK";
        internal const string Support = "MDJBOUQ0Q0M5N0IxQjE2Mjc4Q0REQjU1REFBNzJBMkM4Qjc1N0E1QUE4NUQxQkQ4QkQzNjFFNTVBMEM3MjdERDYwMTQ5NA==";
        /// <summary>
        /// BOSS保底不会加强的地图
        /// </summary>
        public static string[] notMap = { "202", "203", "204", "205", "206", "207", "208", "209" };
        //internal const string Support1 = "RjcxODgyNzY5NTQ4NkI0Q0Q0REQ0Q0U5MjcyNTk1M0VBNTQxQTREMjlDMjI5RURENkM=";

        internal static string ApplicationDataPath =
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);

        public static string TenMinBackup = Path.Combine(ApplicationDataPath, @"PetShikong\Backup\TenMin");

        //internal const string MD = @"PageMain\Content\resources\styles\images\4ie\lld\PetShikong.7z";
        public const double DFV = 6.0; //存档版本号
        private const string VKey = "HYFMWZS";
        private const string Key11 = "XGVUSZF";
        private const string Key12 = "LjIwMTYuMjAxNy4yMDE4LjIwMjAuMjAyMS4yMDIy";
        private const string Key21 = "JYSWQRT";
        private const string Key22 = "LjEyMzQuNTY3OC45MDEyLjM0NTYuNzg5MC5hYmNk";
        internal const string Version = "9342"; //游戏版本号
        internal const string Version2 = "3"; //游戏版本号 - 主程序版本号，防止打补丁之后替换回去
        private const bool test =false; //是否测试版本

        //***********************************************************************************//
        internal static Form1 GameForm;
        private ConvertJson jsonTo = new ConvertJson();
        internal static Random RandomGenerator = new Random();
        internal static Dictionary<long, double> LFCD = new Dictionary<long, double>(); //对数函数对应表
        private static Stopwatch _propWatch;
        private List<string> HCPetList = new List<string>();
        private const string 祝福 = @"您得到了自然女神的祝福,获得了:";
        private const string Error = @"[Error]该道具无法显示";

        private const string 获得道具 = "获得道具 ";

        private static string JF_SHOP = "";//积分商店列表
        private static string JJ_SHOP = "";//结晶商店列表

        private static string JC_SHOP = "";//奖池列表（目前只有远古神龙使用）
        /*private const string 十万 = "100000";
private const string 一千二 = "1200";
private const string 一亿 = "100000000";
private const string 一千万 = "10000000";
private const string 五百万 = "5000000";
private const string 零 = "0";
private const string 一 = "1";
private const string 二 = "2";
private const string 三 = "3";
private const string 五 = "5";*/

        //*********************************************//
        internal static string UserKey;
        internal static bool Noboss = false;
        internal static List<string> CHZT = new List<string> { null, null }; //人物称号状态

        internal static Dictionary<string, List<MonsterInfo>>
            AMML = new Dictionary<string, List<MonsterInfo>>(); //指定地图怪物列表

        internal static Dictionary<string, MapInfo> MapInfo = new Dictionary<string, MapInfo>();
        internal static List<long> LevelExpList = new List<long>();
        private static List<GoodsInfo> MS_SP_List = new List<GoodsInfo>(); //元宝道具
        private static List<GoodsInfo> MS_KP_List = new List<GoodsInfo>(); //水晶道具
        private static List<GoodsInfo> GS_GP_List = new List<GoodsInfo>(); //金币道具
        private static List<GoodsInfo> MS_IP_List; //积分道具
        private static List<GoodsInfo> GS_PP_List = new List<GoodsInfo>(); //威望道具
        public static List<GoodsInfo> MS_CP_List; // 结晶道具

        //*********************************************//
        internal static List<PropInfo> PP_List = new List<PropInfo>(); //玩家道具
        internal static List<PetInfo> PetList = new List<PetInfo>();
        internal static List<PetConfig> PetType_List;
        internal static List<MonsterType> MT_List; //怪物类型
        internal static Dictionary<int, string> PetStates = new Dictionary<int, string>();//宠物境界
        internal static List<EquipmentInfo> EquipmentList = new List<EquipmentInfo>();
        //public static List<TaskInfo> D_taskList = new List<TaskInfo>();

        //**************************************
        internal static List<TaskInfo> Task_TaskHelper = new List<TaskInfo>();
        private readonly Dictionary<string, string> _地图序号表 = new Dictionary<string, string>//地图战斗次数用的
        {
            {"新手基地", "1"},
            {"妖精森林", "2"},
            {"潮汐海崖", "3"},
            {"巨石山脉", "4"},
            {"黄金陵", "5"},
            {"炽热沙滩", "6"},
            {"尤玛火山", "7"},
            {"死亡沙漠", "8"},
            {"海市蜃楼", "9"},
            {"冰滩", "10"},
            {"海底世界", "11"},
            {"圣诞小屋", "12"},
            {"石阵", "13"},
            {"平原", "14"},
            {"绿荫林", "15"},
            {"五指石印", "16"},
            {"鬼屋", "17"},
            {"天空之城", "18"},
            {"天之路", "19"},
            {"危之路", "20"},
            {"幽冥之镜", "21"},
            {"异界深渊", "22"},
            {"时空冰岛", "23"},
            {"圣兽云殿", "102"},
            {"埋骨之地","201" },
            {"孢子林", "202"},
            {"伊苏王的神墓", "9001"},
            {"火龙王的宫殿", "9002"},
            {"史芬克斯密穴", "9003"},
            {"玲珑城", "9004"},
            {"BOSS集中营", "9010"},
            {"楼兰古城的遗迹", "9011"},
            {"赫拉神殿", "201901"},
            {"阿尔提密林", "9012"},
            {"地狱之门", null},
            {"通天塔", null}
        };
        //**************************************
        /// <summary>
        /// 0 = 内存模式 1 = 老模式
        /// </summary>
        public static int AdminMode = 0;
        //public static String 玩家KEY = null;

        //public static bool 初始化 = true;

        //internal static List<宠物类型> TmpPetType_List = null;
        //internal static List<道具类型> TmpPropType_List = null;
        internal static int EnvironmentMode = 0; //1是云服务器

        //**************************************
        public static bool GOLDMAX = false;//金币上限提示
        //**************************************
        //*****彩蛋*****
        public static string[] holiday = { "0401" };//节日名称
        public static bool yrj_ = false;//愚人节开关
        //额外的魔法卡BUFF
        private static userCardBuff ub = null;
        public bool getPower()
        {
            String id = new DataProcess().ReadUserInfo().论坛ID;
            //return (id == "残酷" || id == "北冥有鱼" || id == "青衫" || id == "admin");
            return (id == "青衫" || id=="heima");
        }
        public static double RandomNext(double minimum, double maximum, int Len)   //Len小数点保留位数
        {
            Random random = new Random(Guid.NewGuid().GetHashCode());
            return Math.Round(random.NextDouble() * (maximum - minimum) + minimum, Len);
        }
        public void goucb()//获取用户的卡片，判断特权
        {
            ub = new userCardBuff();
            var uc = CardInfo.getUserCardList();
            if (uc == null) return;
            foreach(var c in uc)
            {
                //人物信息
                if (c.upType.Equals("金币上限"))
                {
                    ub.金币上限 =( Convert.ToInt64(ub.金币上限) + Convert.ToInt64(c.upNum)).ToString();
                }
                if (c.upType.Equals("元宝上限"))
                {
                    ub.元宝上限 = (Convert.ToInt64(ub.元宝上限) + Convert.ToInt64(c.upNum)).ToString();
                }
                if (c.upType.Equals("水晶上限"))
                {
                    ub.水晶上限 = (Convert.ToInt64(ub.水晶上限) + Convert.ToInt64(c.upNum)).ToString();
                }
                //挂机
                if (c.upType.Equals("赫拉神殿小怪额外伤害"))
                {
                    ub.赫拉神殿额外伤害1 = (Convert.ToInt32(ub.赫拉神殿额外伤害1) + Convert.ToInt32(c.upNum)).ToString();
                }
                if (c.upType.Equals("赫拉神殿BOSS额外伤害"))
                {
                    ub.赫拉神殿额外伤害2 = (Convert.ToInt32(ub.赫拉神殿额外伤害2) + Convert.ToInt32(c.upNum)).ToString();
                }
                if (c.upType.Equals("必遇BOSS次数减少"))
                {
                    ub.必遇BOSS次数减少 = (Convert.ToInt32(ub.必遇BOSS次数减少) + Convert.ToInt32(c.upNum)).ToString();
                }
                if (c.upType.Equals("转换战斗胜利经验"))
                {
                    ub.转换战斗胜利经验=( Convert.ToDouble(ub.转换战斗胜利经验) +Convert.ToDouble(c.upNum)).ToString();
                }
                //其他
                if (c.upType.Equals("批量开包"))
                {
                    ub.批量开包 = true;
                }
                if (c.upType.Equals("追龙任务"))
                {
                    ub.追龙任务 = true;
                }
                if (c.upType.Equals("自动涅槃间隔减短"))
                {
                    ub.自动涅槃间隔减短 = (Convert.ToDouble(ub.自动涅槃间隔减短) + Convert.ToDouble(c.upNum)).ToString();
                }
                if (c.upType.Equals("幸运值"))
                {
                    ub.幸运值 = (Convert.ToDouble(ub.幸运值) + Convert.ToDouble(c.upNum)).ToString();
                }
            }
        }
        public string getLuck()
        {
            return "1";
            if (Convert.ToDouble(ub.幸运值) > 100 && !getPower())
            {
                return "0";
            }
            //string rs = SkRC4.DES.DecryptRC4(ub.幸运值, GetKey(1));
            return ub.幸运值;
        }
        public int ggj(int i)//获取赫拉神殿特权
        {
            if (ub == null)
                goucb();
            if (i == 7) return Convert.ToInt32(ub.赫拉神殿额外伤害1);
            else if (i == 2) return Convert.ToInt32(ub.赫拉神殿额外伤害2)<=2 ? Convert.ToInt32(ub.赫拉神殿额外伤害2): 2;//最高不可能超过2
            else return 0;
        }
        public double gjy()//挂机经验
        {
            return 0;
            if (ub == null)
                goucb();
            return Convert.ToDouble(ub.转换战斗胜利经验)<=1.0? Convert.ToDouble(ub.转换战斗胜利经验): 1.0;//最高不可能超过100%
        }
        public double gnt()//自动涅槃间隔减短 上限2秒
        {
            if (ub == null)
                goucb();
            return Convert.ToDouble(ub.自动涅槃间隔减短) <= 3.0 ? Convert.ToDouble(ub.自动涅槃间隔减短) : 3.0;//最高不可能超过3s
        }
        public int gbs()//必遇BOSS减少次数
        {
            if (ub == null)
                goucb();
            return Convert.ToInt32(ub.必遇BOSS次数减少)<=1000? Convert.ToInt32(ub.必遇BOSS次数减少) : 1000;//暂时设置最高-1000
        }
        public long ghb(int i)//5=金币，7=元宝，8=水晶
        {

            if (ub == null) 
                goucb();
            return i == 5 ? Convert.ToInt64(ub.金币上限) : i == 7 ? Convert.ToInt64(ub.元宝上限) : i==8? Convert.ToInt64(ub.水晶上限):0;
        }
        public bool gzl()//追龙特权
        {
            if (ub == null) 
                goucb();
            return ub.追龙任务;
        }
        public bool gkb()//开包特权
        {
            if (ub == null)
                goucb();
            return ub.批量开包;
        }
        //**************************************
        internal void SaveGameData(bool exit = false)
        {
            if (exit)
            {
                if (Res.ResourceContiainer != null && AdminMode != 1)
                {
                    new DataProcess().SaveFile(Res.ResourceContiainer, pf + PF_Path);
                }
            }
            else
            {
                if (Res.ResourceContiainer != null && AdminMode != 1)
                {
                    string tmp = Res.ResourceContiainer;
                    if (Res.LastHash == 0 || tmp.GetHashCode() != Res.LastHash)
                    {
                        new DataProcess().SaveFile(tmp, pf + PF_Path);
                        Res.LastHash = tmp.GetHashCode();
                    }
                    LogSystem.JoinLog(LogSystem.EventKind.自动存档);
                }
            }
        }

        public static List<Formula> FormulaList = new List<Formula>();
        public static string FormulaJson = "";//这里不反序列化,每次合成的时候反序列化解决有时候公式不生效的问题
        static List<Formula> 合成公式 = new List<Formula>();

        public String seekFormula(PetInfo pet1, PetInfo pet2)
        {
            FormulaList = JsonConvert.DeserializeObject<List<Formula>>(FormulaJson);
            foreach (Formula f in FormulaList)
            {
                if (f.pet1 == pet1.形象 && (f.pet2 == "-1" || f.pet2 == pet2.形象))
                {
                    if (Convert.ToDouble(pet1.成长) >= f.pet1_cc && Convert.ToDouble(pet2.成长) >= f.pet2_cc)
                    {
                        return f.Result;
                    }
                }
            }

            return null;
        }

        internal string GetKey(int keyIndex, bool dat = false)
        {
            abc++;
            //Console.WriteLine(abc);
            if (keyIndex == 1)
            {
                if (dat && !old)
                {
                    return SkCryptography.Vigenere.de(Key11, VKey).ToLower() + SkCryptography.Base64.DecodeBase64(Key12) + "ZNQMCK";

                }
                return SkCryptography.Vigenere.de(Key11, VKey).ToLower() + SkCryptography.Base64.DecodeBase64(Key12);
            }

            if (keyIndex == 2)
            {
                return SkCryptography.Vigenere.de(Key21, VKey).ToLower() + SkCryptography.Base64.DecodeBase64(Key22);
            }

            return null;
        }



        /// <summary>
        /// 
        /// </summary>
        /// <param name="min"></param>
        /// <param name="max"></param>
        /// <param name="type">1金币，2元宝，3掉落量</param>
        /// <returns></returns>
        internal static int GetProbability(int min, int max, int type)
        {
            //1 金币
            //2 元宝
            //3 道具掉落量
            switch (type)
            {
                case 1:
                    return RandomGenerator.Next(min, max + 1); //金币随便
                case 2:
                    int t = RandomGenerator.Next(1, 101);
                    if (max == min + 1)
                    {
                        return t <= 10 ? max : min;
                    }

                    if (t <= NumEncrypt.十())
                    {
                        return RandomGenerator.Next((int)Math.Floor((min + max) / 2.0), max + 1); //  10%在 平均值 最大值之间取
                    }

                    return RandomGenerator.Next(min, (int)Math.Floor((min + max) / 2.0)); //90%在最小值和平均值间取
                case 3:
                    if (min == 0) //这种情况下 40%不掉，60%掉 
                    {
                        int tt = RandomGenerator.Next(1, 101);
                        if (tt <= NumEncrypt.四十())
                        {
                            //testgg(min, max, 0);
                            return 0;
                        }
                        else
                        {
                            if (max == 1)
                            {
                                // testgg(min, max, 1);
                                return 1;
                            }
                            else
                            {
                                return PropProbability(1, max);
                            }
                        }
                    }
                    else
                    {
                        return PropProbability(min, max);
                    }
            }

            //testgg(min, max, 8888);
            return 0;
        }

        private static int PropProbability(int min, int max)
        {
            int num = max - min + 1;
            int luckynum = RandomGenerator.Next(1, 101);
            switch (num)
            {
                case 1:
                    //testgg(min, max, min);
                    return min;
                case 2:
                    if (luckynum >= 1 && luckynum <= 90)
                    {
                        //testgg(min, max, min);
                        return min;
                    }
                    else
                    {
                        //testgg(min, max, max);
                        return max;
                    }
                case 3:
                    if (luckynum >= 1 && luckynum <= 59)
                    {
                        //testgg(min, max, min);
                        return min;
                    }
                    else if (luckynum >= 60 && luckynum <= 92)
                    {
                        //testgg(min, max, min+1);
                        return min + 1;
                    }
                    else
                    {
                        //testgg(min, max, max);
                        return max;
                    }
                case 4:
                    if (luckynum >= 1 && luckynum <= 46)
                    {
                        //testgg(min, max, min);
                        return min;
                    }
                    else if (luckynum >= 47 && luckynum <= 80)
                    {
                        //testgg(min, max, min+1);
                        return min + 1;
                    }
                    else if (luckynum >= 81 && luckynum <= 96)
                    {
                        //testgg(min, max, min+2);
                        return min + 2;
                    }
                    else
                    {
                        //testgg(min, max, max);
                        return max;
                    }
                case 5:
                    if (luckynum >= 1 && luckynum <= 38)
                    {
                        //testgg(min, max, min);
                        return min;
                    }
                    else if (luckynum >= 39 && luckynum <= 67)
                    {
                        //testgg(min, max, min + 1);
                        return min + 1;
                    }
                    else if (luckynum >= 68 && luckynum <= 87)
                    {
                        //testgg(min, max, min + 2);
                        return min + 2;
                    }
                    else if (luckynum >= 88 && luckynum <= 98)
                    {
                        //testgg(min, max, min + 3);
                        return min + 3;
                    }
                    else
                    {
                        //testgg(min, max, max);
                        return max;
                    }
            }

            //testgg(min, max, 99999);
            return Convert.ToInt32(Math.Floor((max + min) / 2.0));
        }

        /*private static void testgg(int min, int max, int result)
        {
            GameForm.发送红色公告(min.ToString() + "," + max.ToString() + "," + result.ToString());
        }*/

        //private static String 存档Hash;
        public string ABC()//返回游戏版本号
        {
            return Version;
        }
        public string ABCD()//返回游戏小版本号
        {
            return Version2;
        }

        public bool ASD()//返回测试版本
        {
            return test;
        }
        public void SaveFile(string text, string name)
        {

            /*if (name.IndexOf("Main.dat") != -1)
            {
                if (!File.Exists(PF_Path))
                {
                    File.WriteAllText(name, text, Encoding.UTF8);
                }
                File.Copy(name, TmpSave, true);         
                File.WriteAllText(name+"_tmp", text, Encoding.UTF8);
                File.Delete(name);
                File.Move(name + "_tmp", name);
                //存档Hash = GetFileHash(name);
            }
            else
            {
                File.WriteAllText(name, text, Encoding.UTF8);
            }*/
            if (MaindatData != null)
            {
                MaindatData = text;
            }
            if (name == pf + PF_Path && MaindatData != null)
            {
                return;
            }
            if (name.IndexOf("Main.dat", StringComparison.Ordinal) != -1)
            {
                try
                {

                    if (File.Exists(pf + PF_Path))
                    {
                        File.Delete(pf + PF_Path);
                    }

                    FileStream saveFile = new FileStream(name, FileMode.OpenOrCreate, FileAccess.Write,
                        FileShare.ReadWrite);
                    var charData = text.ToCharArray();
                    //初始化字节数组
                    var byData = new byte[charData.Length];
                    //将字符数组转换为正确的字节格式
                    Encoder enca = Encoding.UTF8.GetEncoder();
                    enca.GetBytes(charData, 0, charData.Length, byData, 0, true);
                    saveFile.Seek(0, SeekOrigin.Begin);
                    saveFile.Write(byData, 0, byData.Length);
                    saveFile.Close();

                    ///////////////////////////////////////////////////////

                    ulong now = NativeMethods.AntiGear.GetTickCount64();

                    if (Res.LastSaveGameDataTime == 0 ||
                       now - Res.LastSaveGameDataTime >= 600000)
                    {
                        FileStream saveBackup = new FileStream(
                            Path.Combine(TenMinBackup, new Tools.GetTime().GetSystemTime() + ".dat"),
                            FileMode.OpenOrCreate, FileAccess.Write,
                            FileShare.ReadWrite);
                        charData = text.ToCharArray();
                        //初始化字节数组
                        byData = new byte[charData.Length];
                        //将字符数组转换为正确的字节格式
                        enca = Encoding.UTF8.GetEncoder();
                        enca.GetBytes(charData, 0, charData.Length, byData, 0, true);
                        saveBackup.Seek(0, SeekOrigin.Begin);
                        saveBackup.Write(byData, 0, byData.Length);
                        saveBackup.Close();
                        Res.LastSaveGameDataTime = now;
                        LogSystem.JoinLog(LogSystem.EventKind.自动备份);
                    }


                    /*FileStream backup = new FileStream(name + "_tmp", FileMode.Create, FileAccess.Write,
                        FileShare.ReadWrite);
                    //获得字符数组
                    charData = text.ToCharArray();
                    //初始化字节数组
                    byData = new byte[charData.Length];
                    //将字符数组转换为正确的字节格式
                    Encoder enc = Encoding.UTF8.GetEncoder();
                    enc.GetBytes(charData, 0, charData.Length, byData, 0, true);
                    backup.Seek(0, SeekOrigin.Begin);
                    backup.Write(byData, 0, byData.Length);
                    backup.Close();
                    File.Copy(name + "_tmp", TmpSave, true);
                    File.Delete(name);
                    File.Move(name + "_tmp", name);*/
                }
                catch (Exception ex)
                {
                    // ReSharper disable once PossibleIntendedRethrow
                    LogSystem.JoinLog(LogSystem.EventKind.加入日志, "函数 数据处理.写出文件 出现错误：【" + ex.StackTrace + "】\r\n【" + ex.Message + "】");
                  
                }
            }
            else
            {
                File.WriteAllText(name, text, Encoding.UTF8);
            }
        }

        internal static double GetFloat(string text)
        {
            return Convert.ToDouble(SkRC4.DES.DecryptRC4(text, new DataProcess().GetKey(1)));
        }

        internal static int GetInt(string text)
        {
            return Convert.ToInt32(SkRC4.DES.DecryptRC4(text, new DataProcess().GetKey(1)));
        }

        internal static string GetCharacter(string text)
        {
            return SkRC4.DES.DecryptRC4(text, new DataProcess().GetKey(1));
        }

        /*public static long GetLong(string config)
        {
            return Convert.ToInt64(RC4.DecryptRC4wq(config, new 数据处理().GetKey(1)));
        }*/


        public List<MonsterType> Get_MTList() //取怪物类型列表
        {
            if (MT_List != null)
            {
                return MT_List;
            }

            MT_List = new List<MonsterType>();
            string cfg = ReadFile(pf + MTDC_Path);
            cfg = SkRC4.DES.DecryptRC4(cfg, new DataProcess().GetKey(1));
            string[] 配置组 = cfg.Split(new[] { "\r\n" }, StringSplitOptions.None);
            List<MonsterType> 类型列表 = 配置组.Select(s => s.Split('，'))
                .Select(ss => new MonsterType() { 怪物序号 = ss[0], 对应的宠物序号 = ss[1], 怪物名字 = ss[2], 怪物五行 = ss[3] })
                .ToList();
            MT_List = 类型列表;
            return 类型列表;
        }

        internal MonsterType Get_SMT(string 怪物序号) //取指定怪物类型
        {
            List<MonsterType> list = Get_MTList();
            return list.FirstOrDefault(怪物 => 怪物.怪物序号.Equals(怪物序号));
        }

        internal void PetDied(string petXh)
        {
            List<PetInfo> petList = ReadPlayerPetList();
            foreach (PetInfo t in petList)
            {
                if (!t.宠物序号.Equals(petXh)) continue;
                t.生命 = "0";
                break;
            }
        }

        /*public void 宠物加血(String 宠物序号, String hp)
        {
            List<宠物信息> 宠物列表 = new List<宠物信息>();
            宠物列表 = 读取宠物列表_();
            for (int i = 0; i < 宠物列表.Count; i++)
            {
                if (宠物列表[i].宠物序号.Equals(宠物序号))
                {
                    宠物列表[i].生命 = (Convert.ToInt32(宠物列表[i].生命) + Convert.ToInt32(hp)).ToString();
                    if (Convert.ToInt32(宠物列表[i].生命) > Convert.ToInt32(宠物列表[i].最大生命))
                    {
                        宠物列表[i].生命 = 宠物列表[i].最大生命;
                    }
                    break;
                }
            }
        }*/
        internal void PetRefresh(string petXh)
        {
            List<PetInfo> petList = ReadPlayerPetList();
            foreach (PetInfo t in petList)
            {
                if (!t.宠物序号.Equals(petXh)) continue;
                t.生命 = t.最大生命;
                break;
            }
        }

        public List<MonsterInfo> GetAMML(string mapId, bool admin = false) //取指定地图怪物列表
        {
            if (mapId == "test") {
                return JsonConvert.DeserializeObject<List<MonsterInfo>>(File.ReadAllText("testMonster.txt"));
            }
            if (admin)
            {
                return ProcessAMML(mapId);
            }
            else
            {
                if (!AMML.ContainsKey(mapId))
                {
                    if (AMML.Count >= 10)
                    {
                        AMML.Clear();
                    }

                    List<MonsterInfo> tmp = ProcessAMML(mapId);
                    AMML.Add(mapId, tmp);
                    //Console.WriteLine("tmp:"+tmp.Count);
                    return tmp;
                }
                else
                {
                    return AMML[mapId];
                }
            }

        }

        private List<MonsterInfo> ProcessAMML(string mapId)
        {
            string cfg = ReadFile(pf + MDC_Path + "_" + mapId + "pet_.data");
            if (cfg == null)
            {
                return null;
            }

            cfg = SkRC4.DES.DecryptRC4(cfg, new DataProcess().GetKey(1));
            List<MonsterInfo> monsterList = JsonConvert.DeserializeObject<List<MonsterInfo>>(cfg);
            var nlist = GetAllNMonster().Where(C => C.所属地图 == mapId).ToList();
            monsterList.AddRange(nlist);
            return monsterList;
        }
        /// <summary>
        /// 获取联网怪物配置
        /// </summary>
        /// <returns></returns>
        public List<MonsterInfo> GetAllNMonster()
        {

            if (newmonsterstr == "" || newmonsterstr == null) return new List<MonsterInfo>();
            var cfg = SkRC4.DES.DecryptRC4(newmonsterstr, new DataProcess().GetKey(1));
            List<MonsterInfo> monsterList = JsonConvert.DeserializeObject<List<MonsterInfo>>(cfg);

            return monsterList;
        }
        public MapInfo ReadMapInfo(string mapId, bool admin = false)
        {
            if (mapId == "test") { 
                return ProcessMapInfo("1");
            }
            if (admin)
            {
                return ProcessMapInfo(mapId);
            }
            else
            {
                if (!MapInfo.ContainsKey(mapId))
                {
                    if (MapInfo.Count >= 10)
                    {
                        MapInfo.Clear();
                    }

                    MapInfo tmp = ProcessMapInfo(mapId);
                    MapInfo.Add(mapId, tmp);
                    return tmp;
                }
                else
                {
                    return JsonConvert.DeserializeObject<MapInfo>(JsonConvert.SerializeObject(MapInfo[mapId]));
                }
            }
        }

        private MapInfo ProcessMapInfo(string mapId)
        {
            string cfg = ReadFile(pf + MDC_Path + "_" + mapId + "map_.data");
            if (cfg == null)
            {
                return null;
            }

            cfg = SkRC4.DES.DecryptRC4(cfg, new DataProcess().GetKey(1));
            MapInfo map = JsonConvert.DeserializeObject<MapInfo>(cfg);
            if (map.地图ID != mapId)
            {
                AntiCheat.TamperingProcess();
                return null;
            }

            return map;
        }

        internal PetInfo GetTtMonster()
        {
            List<MonsterType> monster = Get_MTList();
            UserInfo user = new DataProcess().ReadUserInfo();
            int index = RandomGenerator.Next(0, monster.Count);
            MonsterInfo info = new MonsterInfo

            {
                掉落道具 =//通天塔怪物掉落
                    "",
                序号 = monster[index].怪物序号,
                怪物五行 = "仙"
            };
            long floor = 1;
            if (SkTools.JudgeObjectType.NumOrNot(user.TTT))
            {
                floor = Convert.ToInt32(user.TTT);
            }
            if (Fight.Test_TT)
            {
                floor = Fight.Test_TTfloor;
            }
            MapInfo map = new MapInfo();
            {
                Int64 通天塔怪物成长 = 100 + 100 * (1400 + 200 * floor);

                if (floor >= 1)
                {
                    map.最大元宝 = "5";
                    map.最小元宝 = "0";
                    map.最大金币 = "150000";
                    map.最小金币 = "18000";
                    map.掉落道具 = "|2016111002,2|2016111003,2|2016111004,2|2016111005,2|2016111006,2|2016092502,3|2016100403,12|2016092501,3|2017050301,3|2018092302,3|2018092316,3|2018092317,3|2016110513,3|2016110546,3|2017052501,2|2017052502,2|2016100401,3|2016101701,8|2017082101,8|2018042901,10|2016112101,12|2016092304,15";//通天塔地图掉落
                    map.最小掉落 = "1";
                    map.最大掉落 = "3";
                    info.经验值 = "10000000";
                    //通天塔掉落，怪物掉落
                    if (floor == 70 || floor == 140 || floor == 210 || floor == 270 || floor == 310 || floor == 370 || floor == 410 || floor == 470 || floor == 477)
                    {
                        info.最大掉落 = "3";
                        info.怪物成长 = (floor * 324 + 100).ToString();
                        info.掉落道具 += "|2018031001|943011|2019030101|2020032602|2020032601|20";
                        //修炼丹 精练星辰 琉璃符  魂之源  魂宠之灵 涅槃兽之卵  
                    }
                    if (floor == 100 || floor == 160 || floor == 200 || floor == 260 || floor == 300 || floor == 360 || floor == 400 || floor == 460 || floor == 500)
                    {
                        info.最大掉落 = "2";
                        info.怪物成长 = (floor * 324 + 100).ToString();
                        info.掉落道具 += "|2019051903|2019051905|2019051906|2018082101|2020032602|2016110403|2018040102|2018040103";
                        //荣耀印记  光辉之力 勇者之光 自动合涅卷轴·子 魂之源 灰色龙鳞 银色龙鳞 金色龙鳞
                    }
                    if (floor % 10 == 0)//每10层加强属性
                    {
                        info.最大掉落 = "3";
                        info.怪物成长 = (floor * 324 + 100).ToString();
                    }
                    if (floor == 185 || floor == 230 || floor == 285 || floor == 330 || floor == 385 || floor == 430 || floor == 485 || floor == 500)//通天装备图纸
                    {
                        info.最大掉落 = "3";
                        info.怪物成长 = (floor * 216 + 100).ToString();
                        info.掉落道具 += "|2020091708|2019051903|2019051905|2019051906|2018082101|2018031001|943011|2019030101|2020032602|2020032601|20|2019051911|2019051912|2019051913|2019051914|2019051915|2019051916|2019051917|2019051918|2019051919|2019051920|2019051921|2019051922";
                    }
                    if (floor % 4 == 0)
                    {
                        info.最大掉落 = "1";
                        info.怪物成长 = (floor * 216 + 100).ToString();
                        if (floor % 10 != 0) info.怪物成长 = (floor * 324 + 100).ToString();
                        info.掉落道具 += "|2019052002";//每4层有概率掉落北冥鸽碎片
                    }
                    else
                    {
                        info.最大掉落 = "2";
                        info.怪物成长 = (floor * 108 + 100).ToString();
                    }
                }
                else if (floor >= 1 && floor <= 100)
                {
                    if (floor % 10 == 0)
                    {
                        info.最大掉落 = "3";
                        info.怪物成长 = 通天塔怪物成长.ToString();
                        info.掉落道具 = "";
                    }
                    else if (floor % 5 == 0)
                    {
                        info.最大掉落 = "2";
                        info.怪物成长 = 通天塔怪物成长.ToString();
                    }
                    else
                    {
                        info.最大掉落 = "2";
                        info.怪物成长 = 通天塔怪物成长.ToString();
                    }

                    map.最大元宝 = "1";
                    map.最小元宝 = "0";
                    map.最大金币 = "50000";
                    map.最小金币 = "6000";
                    //map.掉落道具 = "2016110513,7|2016110546,7|2016100401,12|2016101701,12|2017080712|2017082101,8|2018042901,10|2016112101,12|2016092304,12"; //通天塔掉落
                    map.最小掉落 = "0";
                    map.最大掉落 = "2";
                    info.经验值 = "2500000";
                }
                else if (floor >= 101 && floor <= 200)
                {
                    info.最大掉落 = "2";
                    if (floor % 10 == 0)
                    {
                        info.最大掉落 = "3";
                        info.怪物成长 = 通天塔怪物成长.ToString();
                    }
                    else if (floor % 5 == 0)
                    {
                        info.最大掉落 = "2";
                        info.怪物成长 = 通天塔怪物成长.ToString();
                    }
                    else
                    {
                        info.最大掉落 = "2";
                        info.怪物成长 = 通天塔怪物成长.ToString();
                    }

                    map.最大元宝 = "2";
                    map.最小元宝 = "0";
                    map.最大金币 = "50000";
                    map.最小金币 = "6000";
                    //map.掉落道具 = "2016110513,7|2016110546,7|2016100401,12|2016101701,12|2017080712|2017082101,8|2018042901,10|2016112101,12|2016092304,12"; //通天塔掉落
                    map.最小掉落 = "0";
                    map.最大掉落 = "2";
                    info.经验值 = "2500000";
                }
                else if (floor >= 201 && floor <= 300)
                {
                    if (floor % 10 == 0)
                    {
                        info.最大掉落 = "3";
                        info.怪物成长 = 通天塔怪物成长.ToString();
                    }
                    else if (floor % 5 == 0)
                    {
                        info.最大掉落 = "3";
                        info.怪物成长 = 通天塔怪物成长.ToString();
                    }
                    else
                    {
                        info.最大掉落 = "2";
                        info.怪物成长 = 通天塔怪物成长.ToString();
                    }

                    map.最大元宝 = "3";
                    map.最小元宝 = "0";
                    map.最大金币 = "100000";
                    map.最小金币 = "12000";
                    //map.掉落道具 ="2016110513,7|2016110546,7|2016100401,12|2016101701,12|2017080712|2017082101,8|2018042901,10|2016112101,12|2016092304,12";//通天塔掉落
                    map.最小掉落 = "0";
                    map.最大掉落 = "3";
                    info.经验值 = "5000000";
                }
                else if (floor >= 301 && floor <= 400)
                {
                    if (floor % 10 == 0)
                    {
                        info.最大掉落 = "3";
                        info.怪物成长 = 通天塔怪物成长.ToString();
                    }
                    else if (floor % 5 == 0)
                    {
                        info.最大掉落 = "3";
                        info.怪物成长 = 通天塔怪物成长.ToString();
                    }
                    else
                    {
                        info.最大掉落 = "2";
                        info.怪物成长 = (floor * 27 + 100).ToString();
                    }

                    map.最大元宝 = "4";
                    map.最小元宝 = "0";
                    map.最大金币 = "100000";
                    map.最小金币 = "12000";
                    //map.掉落道具 ="2016110513,7|2016110546,7|2016100401,12|2016101701,12|2017080712|2017082101,8|2018042901,10|2016112101,12|2016092304,12"; //通天塔掉落
                    map.最小掉落 = "0";
                    map.最大掉落 = "3";
                    info.经验值 = "5000000";
                }
                else if (floor >= 401 && floor <= 500)
                {
                    if (floor % 10 == 0)
                    {
                        info.最大掉落 = "4";
                        info.怪物成长 = 通天塔怪物成长.ToString();
                    }
                    else if (floor % 5 == 0)
                    {
                        info.最大掉落 = "3";
                        info.怪物成长 = 通天塔怪物成长.ToString();
                    }
                    else
                    {
                        info.最大掉落 = "2";
                        info.怪物成长 = 通天塔怪物成长.ToString();
                    }
                    map.最大元宝 = "5";
                    map.最小元宝 = "0";
                    map.最大金币 = "150000";
                    map.最小金币 = "18000";
                    //map.掉落道具 = "2016110513,8|2016110546,8|2016100401,12|2016101701,10|2017080712|2017082101,8|2018042901,12|2016112101,10|2016092304,12";
                    map.最小掉落 = "0";
                    map.最大掉落 = "3";
                    info.经验值 = "7500000";
                }

            }
            //这里是替换重复层数掉落叠加导致掉落配置不正确的'|'
            if (info.掉落道具.IndexOf("|") == 0)
            {
                info.掉落道具 = info.掉落道具.Substring(1);
            }
            info.掉落道具.Replace("||", "");
            PetInfo pet = new PetInfo();
            Fight.怪物信息 = info;
            Fight.地狱通天 = map;
            pet.TTT = true;
            pet.宠物名字 = info.怪物名字;
            pet.形象 = info.序号;
            pet.五行 = info.怪物五行;
            pet = SetDefaultAttribute(pet);
            pet.成长 = info.怪物成长;
            //pet.当前经验 = floor + 350.ToString();
            //long 等级 = 100 + 100 * (1400 + 200 * floor);
            pet.当前经验 = 2000.ToString();//通天塔怪物等级
            if (Convert.ToInt64(pet.当前经验) > 2000)
            {
                pet.当前经验 = 2000.ToString();
            }
            //通天塔怪物属性
            pet = PetCalc.CalcPetAttribute(pet, false, true);
            pet.生命 = Convert.ToInt64(Convert.ToInt64(pet.生命) * 750).ToString();
            pet.最大生命 = Convert.ToInt64(Convert.ToInt64(pet.最大生命) * 750).ToString();
            pet.闪避 = Convert.ToInt64(Convert.ToDouble(pet.闪避) * 50).ToString(CultureInfo.InvariantCulture);
            pet.命中 = Convert.ToInt64(Convert.ToInt64(pet.命中) * 5500).ToString();
            pet.速度 = Convert.ToInt64(Convert.ToInt64(pet.速度) * 20).ToString();
            pet.防御 = Convert.ToInt64(Convert.ToInt64(pet.防御) * 10000).ToString();
            pet.攻击 = Convert.ToInt64(Convert.ToInt64(pet.攻击) * 160).ToString();
            return pet;
        }

        internal PetInfo GetHellMonster()
        {
            List<MonsterType> monster = Get_MTList();
            UserInfo user = new DataProcess().ReadUserInfo();
            int index = RandomGenerator.Next(0, monster.Count);
            MonsterInfo info = new MonsterInfo()
            {
                掉落道具 =
                    "2016092501,20|10000,2|2016100403,4|2016092903,3|2017072203,2|2016101701,2|2017060302,4|2017050301",//地狱之门掉落
                序号 = monster[index].怪物序号,
                怪物五行 = "魔"
            };
            long floor = 1;
            if (SkTools.JudgeObjectType.NumOrNot(user.地狱层数))
            {
                floor = Convert.ToInt64(user.地狱层数);
            }

            MapInfo map = new MapInfo();
            if (floor <= 100 && floor >= 1)
            {
                if (floor % 100 == 0)
                {
                    info.最大掉落 = "4";
                    info.怪物成长 = (floor * 40 + 100).ToString();
                    map.最大元宝 = "120";
                    map.最小元宝 = "30";
                    map.最大金币 = "100000";
                    map.最小金币 = "1200";
                    map.掉落道具 = "10000,3|2016092501|2016101701|2017082402|2017072203|2016100803";
                    map.最小掉落 = "0";
                    map.最大掉落 = "2";
                    info.经验值 = "100000000";
                }
                else if (floor % 50 == 0)
                {
                    info.最大掉落 = "4";
                    info.怪物成长 = (floor * 28 + 100).ToString();
                    map.最大元宝 = "60";
                    map.最小元宝 = "20";
                    map.最大金币 = "100000";
                    map.最小金币 = "1200";
                    map.掉落道具 = "10000|2016092501|2016101701|2017082402|2017072203";
                    map.最小掉落 = "0";
                    map.最大掉落 = "2";
                    info.经验值 = "100000000";
                }
                else if (floor % 10 == 0)
                {
                    info.最大掉落 = "4";
                    info.怪物成长 = (floor * 20 + 100).ToString();
                    map.最大元宝 = "21";
                    map.最小元宝 = "15";
                    map.最大金币 = "100000";
                    map.最小金币 = "1200";
                    map.掉落道具 = "10000|2016092501|2016101701|2017082402|2017072203";
                    map.最小掉落 = "0";
                    map.最大掉落 = "2";
                    info.经验值 = "100000000";
                }
                else if (floor % 5 == 0)
                {
                    info.最大掉落 = "4";
                    info.怪物成长 = (floor * 13 + 100).ToString();
                    map.最大元宝 = "12";
                    map.最小元宝 = "6";
                    map.最大金币 = "100000";
                    map.最小金币 = "1200";
                    map.掉落道具 = "10000|2016092501";
                    map.最小掉落 = "0";
                    map.最大掉落 = "2";
                    info.经验值 = "10000000";
                }
                else
                {
                    info.最大掉落 = "2";
                    info.怪物成长 = (floor * 8 + 100).ToString();
                    map.最大元宝 = "9";
                    map.最小元宝 = "4";
                    map.最大金币 = "100000";
                    map.最小金币 = "1200";
                    map.掉落道具 = "无";
                    info.经验值 = "5000000";
                }
            }
            else if (floor <= 1000 && floor > 100)
            {
                if (floor % 100 == 0)
                {
                    info.最大掉落 = "4";
                    info.怪物成长 = (floor * 53 + 100).ToString();
                    map.最大元宝 = "144";
                    map.最小元宝 = "36";
                    map.最大金币 = "100000";
                    map.最小金币 = "1200";
                    map.掉落道具 = "10000,3|2016092501|2016101701|2017082402,2|2017072203|2016100803";
                    map.最小掉落 = "1";
                    map.最大掉落 = "3";
                    info.经验值 = "100000000";
                }
                else if (floor % 50 == 0)
                {
                    info.最大掉落 = "4";
                    info.怪物成长 = (floor * 31 + 100).ToString();
                    map.最大元宝 = "72";
                    map.最小元宝 = "27";
                    map.最大金币 = "100000";
                    map.最小金币 = "1200";
                    map.掉落道具 = "10000|2016092501|2016101701|2017082402|2017072203|2016100803";
                    map.最小掉落 = "0";
                    map.最大掉落 = "3";
                    info.经验值 = "100000000";
                }
                else if (floor % 10 == 0)
                {
                    info.最大掉落 = "4";
                    info.怪物成长 = (floor * 22 + 100).ToString();
                    map.最大元宝 = "25";
                    map.最小元宝 = "18";
                    map.最大金币 = "100000";
                    map.最小金币 = "1200";
                    map.掉落道具 = "10000|2016092501|2016101701|2017082402|2017072203";
                    map.最小掉落 = "0";
                    map.最大掉落 = "2";
                    info.经验值 = "100000000";
                }
                else if (floor % 5 == 0)
                {
                    info.最大掉落 = "4";
                    info.怪物成长 = (floor * 15 + 100).ToString();
                    map.最大元宝 = "14";
                    map.最小元宝 = "7";
                    map.最大金币 = "100000";
                    map.最小金币 = "1200";
                    map.掉落道具 = "10000|2016092501";
                    map.最小掉落 = "0";
                    map.最大掉落 = "2";
                    info.经验值 = "10000000";
                }
                else
                {
                    double 函数值 = LFCD[floor];
                    info.最大掉落 = "2";
                    info.怪物成长 = (floor * 9 + 100).ToString();
                    map.最大元宝 = Convert.ToInt32(Math.Round(12 * 函数值)).ToString();
                    map.最小元宝 = Convert.ToInt32(Math.Round(8 * 函数值)).ToString();
                    map.最大金币 = Convert.ToInt32(Math.Round(100000 * 函数值)).ToString();
                    map.最小金币 = Convert.ToInt32(Math.Round(1200 * 函数值)).ToString();
                    map.掉落道具 = "无";
                    info.经验值 = "5000000";
                }
            }
            else if (floor > 1000)
            {
                if (floor % 100 == 0)
                {
                    info.最大掉落 = "5";
                    info.怪物成长 = (floor * 100 + 100).ToString();
                    map.最大元宝 = "160";
                    map.最小元宝 = "40";
                    map.最大金币 = "100000";
                    map.最小金币 = "1200";
                    map.掉落道具 = "2017050301|2017082101|2017082402,2|2017072203|2016100803";
                    map.最小掉落 = "1";
                    map.最大掉落 = "3";
                    info.经验值 = "100000000";
                }
                else if (floor % 50 == 0)
                {
                    info.最大掉落 = "5";
                    info.怪物成长 = (floor * 60 + 100).ToString();
                    map.最大元宝 = "80";
                    map.最小元宝 = "30";
                    map.最大金币 = "100000";
                    map.最小金币 = "1200";
                    map.掉落道具 = "2017050301|10000|2017082101|2017082402|2017072203|2016100803";
                    map.最小掉落 = "1";
                    map.最大掉落 = "3";
                    info.经验值 = "100000000";
                }
                else if (floor % 10 == 0)
                {
                    info.最大掉落 = "5";
                    info.怪物成长 = (floor * 44 + 100).ToString();
                    map.最大元宝 = "28";
                    map.最小元宝 = "20";
                    map.最大金币 = "100000";
                    map.最小金币 = "1200";
                    map.掉落道具 = "2017050301|10000|2016101701|2017082402|2017072203|2016100803";
                    map.最小掉落 = "1";
                    map.最大掉落 = "3";
                    info.经验值 = "100000000";
                }
                else if (floor % 5 == 0)
                {
                    info.最大掉落 = "5";
                    info.怪物成长 = (floor * 30 + 100).ToString();
                    map.最大元宝 = "16";
                    map.最小元宝 = "8";
                    map.最大金币 = "100000";
                    map.最小金币 = "1200";
                    map.掉落道具 = "10000|2016092501|2016101701|2017050301|2017082402|2017072203";
                    map.最小掉落 = "1";
                    map.最大掉落 = "2";
                    info.经验值 = "10000000";
                }
                else
                {
                    info.最大掉落 = "2";
                    info.怪物成长 = (floor * 20 + 100).ToString();
                    map.最大金币 = "1";
                    map.最小金币 = "0";
                    map.最大元宝 = "1";
                    map.最小元宝 = "0";
                    map.掉落道具 = "无";
                    info.经验值 = "5000000";
                }
            }

            info.怪物成长 = Convert.ToDouble(info.怪物成长).ToString(CultureInfo.InvariantCulture);
            if (Convert.ToInt64(info.怪物成长) < -1000)
            {
                info.怪物成长 = long.MaxValue.ToString();
            }

            PetInfo pet = new PetInfo();
            Fight.怪物信息 = info;
            Fight.地狱通天 = map;
            pet.地狱之门 = true;
            pet.宠物名字 = info.怪物名字;
            pet.形象 = info.序号;
            pet.五行 = info.怪物五行;
            pet = SetDefaultAttribute(pet);
            pet.成长 = info.怪物成长;
            pet.当前经验 = floor + 350.ToString();
            if (Convert.ToInt64(pet.当前经验) > 600)
            {
                pet.当前经验 = 600.ToString();
            }

            pet = PetCalc.CalcPetAttribute(pet, false, true);
            pet.生命 = (Convert.ToInt64(pet.生命) * 3).ToString();
            pet.最大生命 = (Convert.ToInt64(pet.最大生命) * 3).ToString();
            pet.攻击 = Convert.ToInt64(Convert.ToInt64(pet.攻击) * 1.32).ToString();
            return pet;
        }

        internal void Calc_LFCD()
        {
            for (int i = 100; i <= 1000; i++)
            {
                if (i % 5 != 0)
                {
                    LFCD.Add(i, Math.Round(Math.Log10(-i + 1100) - 2, 6));
                }
            }
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="mapId"></param>
        /// <param name="怪物序号"></param>
        /// <param name="fbID">fubenjindu</param>
        /// <returns></returns>
        internal MonsterInfo Get_SMMList1(string mapId, string 怪物序号, int fbID) //取指定地图怪物列表
        {
            if (mapId=="test") return new MonsterInfo();
            List<MonsterInfo> monsterList = ProcessAMML(mapId);
            //return monsterList.FirstOrDefault(info => info.序号.Equals(怪物序号));
            MonsterInfo m = null;
            if (fbID != -1)
            {
                m = monsterList[fbID];//gen ju jin du qu guaiwu
            }
            else
            {
                m = monsterList.FirstOrDefault(info => info.序号.Equals(怪物序号));
            }

            return m;
        }

        internal bool fight(PetInfo 怪物, PetInfo pet, string map)
        {
            Fight.地图 = map;
            Fight.StartTime = new Tools.GetTime().GetSystemTS();
            Fight.宠物 = pet;
            Fight.怪物 = 怪物;
            Fight.Round = 0;
            Fight.sb();
            double 宠物战斗速度 = RandomGenerator.Next(97, 103) * Convert.ToDouble(Fight.宠物.速度) / 100.0;
            double 怪物战斗速度 = RandomGenerator.Next(97, 103) * Convert.ToDouble(Fight.怪物.速度) / 100.0;

            if (宠物战斗速度 <= 怪物战斗速度)
            {
                Fight.advance = false;
            }
            else
            {
                Fight.advance = true;
            }


            return true;
        }
        /// <summary>
        /// 副本获取怪物函数
        /// </summary>
        /// <param name="monster"></param>
        /// <param name="index"></param>
        /// <returns></returns>
        internal PetInfo ChooseAM(List<MonsterInfo> monster, int index) //取指定怪物
        {
            if (index == -1) index = 0;
            var info = monster[index];
            PetInfo pet = new PetInfo() { 自定义宠物名字 = info.怪物名字, 形象 = info.序号, 指定五行 = info.怪物五行 };
            pet = SetDefaultAttribute(pet);
            pet.成长 = info.怪物成长;
            if (Convert.ToInt32(info.最小等级) > Convert.ToInt32(info.最大等级))
            {
                string 临时变量 = info.最小等级;
                info.最小等级 = info.最大等级;
                info.最大等级 = 临时变量;
            }

            string lv = RandomGenerator.Next(Convert.ToInt32(info.最小等级), Convert.ToInt32(info.最大等级)).ToString();
            pet.当前经验 = lv; //当宠物序号==null时,默认以经验的值作为等级.
            if (info.固定属性 != null)
            {
                pet.攻击 = info.固定属性.攻击;
                pet.最大生命 = info.固定属性.最大生命;
                pet.最大魔法 = info.固定属性.最大魔法;
                pet.生命 = info.固定属性.生命;
                pet.速度 = info.固定属性.速度;
                pet.闪避 = info.固定属性.闪避;
                pet.防御 = info.固定属性.防御;
                pet.魔法 = info.固定属性.魔法;

                pet.状态 = "固定属性";//因调用层存在额外的加成代码，会干扰固定属性，因此做一个标记在外层判定避开对固定属性加成
            }
            else
            {
                pet = PetCalc.CalcPetAttribute(pet, false, true);
            }
            return pet;
        }
        /// <summary>
        /// 普通地图获取怪物函数
        /// </summary>
        /// <param name="monster"></param>
        /// <param name="boss"></param>
        /// <returns></returns>

        internal PetInfo ChooseRM(List<MonsterInfo> monster, int boss)
        {
            List<MonsterInfo> mList = new List<MonsterInfo>();
            if (boss == 0 && monster.Count > 1)
            {
                mList.AddRange(monster.Where(em => !em.怪物名字.Contains('§')));
            }
            else if (boss == 1 && monster.Count > 1)
            {
                mList.AddRange(monster.Where(em => em.怪物名字.Contains('§')));
                if (mList.Count == 0)
                {
                    return ChooseRM(monster, 0);
                }
            }
            else
            {
                mList.AddRange(monster);
            }

            var info = mList[RandomGenerator.Next(0, mList.Count)];
            PetInfo pet = new PetInfo() { 自定义宠物名字 = info.怪物名字, 形象 = info.序号, 指定五行 = info.怪物五行 };
            pet = SetDefaultAttribute(pet);
            pet.成长 = info.怪物成长;
            if (Convert.ToInt32(info.最小等级) > Convert.ToInt32(info.最大等级))
            {
                string tmp = info.最小等级;
                info.最小等级 = info.最大等级;
                info.最大等级 = tmp;
            }

            string lv = RandomGenerator.Next(Convert.ToInt32(info.最小等级), Convert.ToInt32(info.最大等级)).ToString();
            pet.当前经验 = lv; //当宠物序号==null时,默认以经验的值作为等级.
            if (info.固定属性 != null)
            {
                pet.攻击 = info.固定属性.攻击;
                pet.最大生命 = info.固定属性.最大生命;
                pet.最大魔法 = info.固定属性.最大魔法;
                pet.生命 = info.固定属性.生命;
                pet.速度 = info.固定属性.速度;
                pet.闪避 = info.固定属性.闪避;
                pet.防御 = info.固定属性.防御;
                pet.魔法 = info.固定属性.魔法;
                //pet.状态 = "固定属性";//因调用层存在额外的加成代码，会干扰固定属性，因此做一个标记在外层判定避开对固定属性加成
                //此处影响未知，因此暂不实现
                
                
            }
            else
            {
                pet = PetCalc.CalcPetAttribute(pet, false, true);
            }
            return pet;
        }
        internal string GetSumExp(int j)
        {
            if (j > LevelExpList.Count)
            {
                j = LevelExpList.Count;
            }

            if (j <= 1)
            {
                return j.ToString();
            }

            return LevelExpList[j - 1].ToString();
        }

        internal string GetUpgradeExp(int j)
        {
            if (j >= LevelExpList.Count)
            {
                j = LevelExpList.Count;
            }

            if (j <= 1)
            {
                return j.ToString();
            }

            return (LevelExpList[j - 1] - LevelExpList[j - 2]).ToString();
        }

        public string GetMonsterName(string mid)
        {
            Get_MTList();
            foreach (MonsterType type in MT_List)
            {
                if (type.怪物序号.Equals(mid))
                {
                    return type.怪物名字;
                }
            }

            return "Null";
        }

        internal string GetMonsterWX(string mid)
        {
            Get_MTList();
            foreach (MonsterType type in MT_List)
            {
                if (type.怪物序号.Equals(mid))
                {
                    return type.怪物五行;
                }
            }

            return "Null";
        }

        private static DateTime _getjjTime;
        private static DateTime _getjfTime;

        /// <summary>
        /// 0为元宝 1为水晶
        /// </summary>
        /// <param name="type"></param>
        /// <param name="qz"></param>
        /// <returns></returns>
        internal List<GoodsInfo> ReadShopList(int type, int qz = 1)
        {
            string cfg;
            switch (type)
            {
                case 0:
                    cfg = ReadFile(pf + MS_SP_Path);
                    cfg = SkRC4.DES.DecryptRC4(cfg, new DataProcess().GetKey(1));
                    MS_SP_List = JsonConvert.DeserializeObject<List<GoodsInfo>>(cfg);
                    MS_SP_List = SetShopName(MS_SP_List);
                    return MS_SP_List;
                case 1:
                    cfg = ReadFile(pf + MS_KP_Path);
                    cfg = SkRC4.DES.DecryptRC4(cfg, new DataProcess().GetKey(1));
                    MS_KP_List = JsonConvert.DeserializeObject<List<GoodsInfo>>(cfg);
                    MS_KP_List = SetShopName(MS_KP_List);
                    return MS_KP_List;
                case 2:
                    cfg = ReadFile(pf + GS_GP_Path);
                    cfg = SkRC4.DES.DecryptRC4(cfg, new DataProcess().GetKey(1));
                    GS_GP_List = JsonConvert.DeserializeObject<List<GoodsInfo>>(cfg);
                    GS_GP_List = SetShopName(GS_GP_List);
                    return GS_GP_List;
                case 3:
                    if (!DZ.LoginState) return MS_IP_List;
                    if (_getjfTime.Ticks == 0)
                    {
                        _getjfTime = DateTime.Now;
                    }
                    else
                    {
                        try
                        {
                            if (qz == 1 && MS_IP_List != null || MS_IP_List.Count > 0)
                            {
                                TimeSpan ts1 = new TimeSpan(DateTime.Now.Ticks); //获取当前时间的刻度数  
                                                                                 //你的代码或者其他操作  
                                TimeSpan ts2 = new TimeSpan(_getjfTime.Ticks);
                                TimeSpan ts = ts2.Subtract(ts1).Duration(); //时间差的绝对值  
                                                                            //Console.WriteLine(ts + "分钟");
                                if (ts.Minutes < 60) //多少分钟才刷新一次积分商城
                                {
                                    return MS_IP_List;
                                }
                            }
                        }
                        catch { }
                    }

                    cfg = getOnlineShopCFG(0);
                    if (cfg == "" || cfg == null)
                    {
                        MessageBox.Show("正在获取积分商店信息,请等待等一会在访问!\r\n如果在云端,可能会更久\r\n请勿频繁点击", Res.RM.GetString("提示"));
                        return new List<GoodsInfo>();
                    }
                    if (cfg.Contains("出现错误"))
                    {
                        MessageBox.Show(cfg, Res.RM.GetString("提示"));
                    }


                    MS_IP_List = JsonConvert.DeserializeObject<List<GoodsInfo>>(cfg);
                    MS_IP_List = SetShopName(MS_IP_List);
                    return MS_IP_List;
                case 4:
                    cfg = ReadFile(GS_PP_Path);
                    cfg = SkRC4.DES.DecryptRC4(cfg, new DataProcess().GetKey(1));
                    GS_PP_List = JsonConvert.DeserializeObject<List<GoodsInfo>>(cfg);
                    GS_PP_List = SetShopName(GS_PP_List);
                    return GS_PP_List;
                case 5:
                    if (!DZ.LoginState) return MS_CP_List;
                    if (_getjjTime.Ticks == 0)
                    {
                        _getjjTime = DateTime.Now;
                    }
                    else
                    {
                        try
                        {
                            if (qz == 1 && MS_CP_List != null && MS_CP_List.Count > 0)
                            {
                                TimeSpan ts1 = new TimeSpan(DateTime.Now.Ticks); //获取当前时间的刻度数  
                                                                                 //你的代码或者其他操作  
                                TimeSpan ts2 = new TimeSpan(_getjjTime.Ticks);
                                TimeSpan ts = ts2.Subtract(ts1).Duration(); //时间差的绝对值  
                                                                            //Console.WriteLine(ts + "分钟");
                                if (ts.Minutes < 60) //多少分钟才刷新一次结晶商城
                                {
                                    return MS_CP_List;
                                }
                            }
                        }
                        catch { }

                    }

                    cfg = getOnlineShopCFG(1);
                    if (cfg == "" || cfg == null)
                    {
                        MessageBox.Show("正在获取结晶商店信息,请等待等一会在访问!\r\n如果在云端,可能会更久\r\n请勿频繁点击", Res.RM.GetString("提示"));
                        return new List<GoodsInfo>();
                    }
                    if (cfg.Contains("出现错误"))
                    {
                        MessageBox.Show(cfg);
                    }

                    if(qz!=-1)DZ.GetJj();
                    MS_CP_List = JsonConvert.DeserializeObject<List<GoodsInfo>>(cfg);
                    MS_CP_List = SetShopName(MS_CP_List);
                    return MS_CP_List;
            }

            return null;
        }

        private List<GoodsInfo> SetShopName(List<GoodsInfo> goodsList)
        {
            foreach (GoodsInfo t in goodsList)
            {
                if (t.道具名字 != null) continue;
                t.道具名字 = GetPropName(t.商品序号);
                t.道具图标 = GetPropICO(t.商品序号);
            }

            return goodsList;
        }


        internal static string GetSalt()
        {
            return SkRC4.DES.DecryptRC4("1BAAC5CED9E7EB787FD4D149D0A6632D8F78791AE80E5583FC6C", new DataProcess().GetKey(1));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="viplv"></param>
        /// <param name="kind">0=金币 1=元宝水晶</param>
        private static void MallGg(string viplv, int kind)//VIP特权  商店购买
        {
            UserInfo user = new DataProcess().ReadUserInfo();
            int discount;
            if (kind == 0)
            {
                discount = 2 * Convert.ToInt16(viplv);
                if (user.至尊VIP || user.星辰VIP)
                {
                    discount = 25;
                }
            }
            else
            {
                discount = Convert.ToInt16(viplv);
            }
            if (user.星辰VIP)
            {
                GameForm.发送红色公告("由于您是尊贵的星辰VIP玩家，您此次购物享受了" + discount + "%的优惠。");
            }
            else if (user.至尊VIP)
            {
                GameForm.发送红色公告("由于您是尊贵的至尊VIP玩家，您此次购物享受了" + discount + "%的优惠。");
            }
            else if (Convert.ToInt16(viplv) > 0)
            {
                GameForm.发送红色公告("由于您是尊贵的VIP" + viplv + "玩家，您此次购物享受了" + discount + "%的优惠。");
            }

        }

        private static void MallGg1(string name, double zk)
        {
            GameForm.发送红色公告("由于您佩戴了称号" + "“" + name + "”，您本次购物享受了" + 100 * zk + "%的优惠。");
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="type">1为元宝 2为水晶 7为奖池商店</param>
        /// <param name="道具序号"></param>
        /// <param name="quantity"></param>
        /// <returns></returns>
        internal bool Shopping(int type, string 道具序号, int quantity)//在商店购买道具
        {
            if (quantity <= 0 || quantity > 1000)
            {
                return false;//购买失败
            }
            if (type == 6 && (quantity <= 0 || quantity > 100))
            {
                return false;//购买失败
            }
            GetPAP();
            if (PP_List.Count > Convert.ToInt32(new DataProcess().ReadUserInfo().道具容量))
            {
                return false;
            }
            ReadShopList(type - 1, -1);
            List<GoodsInfo> shop = new List<GoodsInfo>();
            //List<MallsRestrict> shop_xg = new List<MallsRestrict>();//限购配置
            switch (type)
            {
                case 1:
                    shop = MS_SP_List;
                    break;
                case 2:
                    shop = MS_KP_List;
                    break;
                case 3:
                    shop = GS_GP_List;
                    break;
                case 4:
                    shop = MS_IP_List;
                    break;
                case 5:
                    shop = GS_PP_List;
                    break;
                case 6:
                    shop = MS_CP_List;
                    break;
                case 8:
                    var cfg = SkRC4.DES.DecryptRC4(JC_SHOP, new DataProcess().GetKey(1));
                    shop = JsonConvert.DeserializeObject<List<GoodsInfo>>(cfg); 
                    break;
            }

            GoodsInfo 目标道具 = shop.FirstOrDefault(道具 => 道具.商品序号.Equals(道具序号));
            if (目标道具 == null)
            {
                return false;
            }

            AntiCheat.AntiCheat_B();
            AntiCheat.AntiCheat_C();
            {//VIP特权-返利，优惠
                //进入这里大概是开始判断能不能买道具吧?
                UserInfo user = ReadUserInfo();
                //判断限购开始-限制购买-限购
                /*
                string cfg;
                cfg = ReadFile(pf + MS_XG);
                cfg = SkRC4.DES.DecryptRC4(cfg, new DataProcess().GetKey(1));
                shop_xg = JsonConvert.DeserializeObject<List<MallsRestrict>>(cfg);
                if (user.商店限购 != null)
                {
                    if (user.商店限购.Count > 0)
                    {
                        for(int i =0;i< user.商店限购.Count; i++)//先循环一遍已购买的道具
                        {
                            for(int j = 0; j < shop_xg.Count; j++)
                            {
                                string[] c = user.商店限购[i].Split(',');//道具ID,已购买数量,货币类型
                                if (c.Length >= 2)
                                {
                                    //理一下思路，这里需要判断购买的道具没有达到上限，且，c[2]的货币类型要和目标道具.货币类型一样
                                    if (shop_xg[j].道具ID.Equals(c[0]) && shop_xg[j].限购货币类型.Equals(c[2]) && c[2].Equals(目标道具.货币类型) && c[0].Equals(目标道具.商品序号) )//找到道具,且货币类型一样,和目标道具的序号一样
                                    {
                                        if (shop_xg[j].购买上限 < (Convert.ToInt32(c[1]) + Convert.ToInt32(目标道具.商品数量) * quantity))//当配置上限小等于已购买数量+准备购买数量时，返回 
                                        {
                                            if (shop_xg[j].道具ID.IndexOf("9102") != -1)
                                            {
                                                GameForm.发送游戏公告($"该结晶礼盒目前还能买{shop_xg[j].购买上限 - Convert.ToInt32(c[1])}个！");
                                                return false;
                                            }
                                            else
                                            {
                                                GameForm.发送游戏公告($"道具[{GetPropName(shop_xg[j].道具ID)}]目前还能买{shop_xg[j].购买上限 - Convert.ToInt32(c[1])}个！");
                                                return false;
                                            }
                                            
                                        }
                                    }
                                    //这里需要对不在商店限购里的道具进行判断
                                    if (shop_xg[j].道具ID.Equals(目标道具.商品序号) && shop_xg[j].限购货币类型.Equals(目标道具.货币类型) && shop_xg[j].购买上限< (Convert.ToInt32(目标道具.商品数量) * quantity) )
                                    {
                                        if (shop_xg[j].道具ID.IndexOf("9102")!=-1)
                                        {
                                            GameForm.发送游戏公告($"该结晶礼盒超过购买上限!购买上限是：{shop_xg[j].购买上限}个！");
                                            return false;
                                        }
                                        else
                                        {
                                            GameForm.发送游戏公告($"道具[{GetPropName(shop_xg[j].道具ID)}]超过购买上限!购买上限是：{shop_xg[j].购买上限}个！");
                                            return false;
                                        }
                                        
                                    }
                                }
                            }
                        }
                    }
                }
                //不进入循环时判断
                else
                {
                    foreach (var s in shop_xg)
                    {
                        if (s.道具ID.Equals(目标道具.商品序号) && s.限购货币类型.Equals(目标道具.货币类型))
                        {
                            if(s.购买上限 < (Convert.ToInt32(目标道具.商品数量) * quantity))
                            {
                                if (s.道具ID.IndexOf("9102")!=-1)
                                {
                                    GameForm.发送游戏公告($"该结晶礼盒超过购买上限!购买上限是：{s.购买上限}个！");
                                    return false;
                                }
                                else
                                {
                                    GameForm.发送游戏公告($"道具[{GetPropName(s.道具ID)}]超过购买上限!购买上限是：{s.购买上限}个！");
                                    return false;
                                }
                                
                            }
                        }
                    }
                }
                */
                //判断限购结束
                short vipLv = Convert.ToInt16(user.vip);
                //new AntiCheat().反作弊();
                if (目标道具.货币类型.Equals("元宝"))
                {
                    long sum = Convert.ToInt32(目标道具.商品价格) * quantity;
                    if (Convert.ToInt32(目标道具.商品价格) <= 0 || sum < 0)
                    {
                        MessageBox.Show(Res.RM.GetString("Naive"));
                        return false;
                    }

                    if (Convert.ToInt32(user.元宝) - sum >= 0)
                    {
                        double discount = 1 - 0.01 * vipLv;
                        if (!string.IsNullOrEmpty(CHZT[0]))
                        {
                            string script = SkRC4.DES.DecryptRC4(CHZT[0], new DataProcess().GetKey(2));
                            if (script == "水晶返利" || script == "返利")
                            {
                                double zk = GetFloat(CHZT[1]);
                                discount -= zk;
                                MallGg1(user.称号, zk);
                            }
                        }

                        user.元宝 = (Convert.ToInt32(user.元宝) - Convert.ToInt32(Math.Round(sum * discount))).ToString();
                        if (vipLv > 0)
                        {
                            MallGg(user.vip, 1);
                        }
                    }
                    else
                    {
                        return false; //元宝不足
                    }
                }
                else if (目标道具.货币类型.Equals("水晶"))
                {
                    long sum = Convert.ToInt32(目标道具.商品价格) * quantity;
                    if (Convert.ToInt32(目标道具.商品价格) <= 0 || sum < 0)
                    {
                        MessageBox.Show(Res.RM.GetString("Naive"));
                        return false;
                    }

                    if (Convert.ToInt32(user.水晶) - sum >= 0)
                    {
                        double discount = 1 - 0.01 * vipLv;
                        if (!string.IsNullOrEmpty(CHZT[0]))
                        {
                            string script = SkRC4.DES.DecryptRC4(CHZT[0], new DataProcess().GetKey(2));
                            if (script == "水晶返利" || script == "返利")
                            {
                                double zk = GetFloat(CHZT[1]);
                                discount -= zk;
                                MallGg1(user.称号, zk);
                            }
                        }

                        user.水晶 = (Convert.ToInt32(user.水晶) - Convert.ToInt32(Math.Round(sum * discount))).ToString();
                        if (vipLv > 0)
                        {
                            MallGg(user.vip, 1);
                        }
                    }
                    else
                    {
                        return false; //水晶不足
                    }
                }
                else if (目标道具.货币类型.Equals("金币"))
                {
                    long sum = Convert.ToInt64(目标道具.商品价格) * quantity;
                    if (Convert.ToInt64(目标道具.商品价格) <= 0 || sum < 0)
                    {
                        MessageBox.Show(Res.RM.GetString("Naive"));
                        return false;
                    }

                    if (Convert.ToInt64(user.金币) - sum >= 0)
                    {
                        double discount = 1 - 0.02 * vipLv;
                        if (user.至尊VIP || user.星辰VIP)
                        {
                            discount = 1 - 0.025 * vipLv;
                        }
                        if (!string.IsNullOrEmpty(CHZT[0]))
                        {
                            string script = SkRC4.DES.DecryptRC4(CHZT[0], new DataProcess().GetKey(2));
                            if (script == "金币返利" || script == "返利")
                            {
                                double zk = GetFloat(CHZT[1]);
                                discount -= zk;
                                MallGg1(user.称号, zk);
                            }
                        }

                        user.金币 = (Convert.ToInt64(user.金币) - Convert.ToInt64(Math.Round(sum * discount))).ToString();
                        if (vipLv > 0)
                        {
                            MallGg(user.vip, 0);
                        }
                    }
                    else
                    {
                        return false; //金币不足
                    }
                }
                else if (目标道具.货币类型.Equals("积分"))
                {
                    if (DZ.LoginState)
                    {
                        long sum = Convert.ToInt32(目标道具.商品价格) * quantity;
                        if (Convert.ToInt32(目标道具.商品价格) <= 0 || sum < 0)
                        {
                            MessageBox.Show(Res.RM.GetString("Naive"));
                            return false;
                        }

                        if (Convert.ToInt32(user.VIP积分) - sum >= 0)
                        {
                            user.VIP积分 = (Convert.ToInt32(user.VIP积分) - sum).ToString();
                        }
                        else
                        {
                            return false; //积分不足
                        }
                    }
                    else
                    {
                        return false;
                    }
                }
                else if (目标道具.货币类型.Equals("威望"))
                {
                    long sum = Convert.ToInt32(目标道具.商品价格) * quantity;
                    if (Convert.ToInt32(目标道具.商品价格) <= 0 || sum < 0)
                    {
                        MessageBox.Show(Res.RM.GetString("Naive"));
                        return false;
                    }

                    if (Convert.ToInt32(user.威望) - sum >= 0)
                    {
                        user.威望 = (Convert.ToInt32(user.威望) - sum).ToString();
                    }
                    else
                    {
                        return false; //威望不足
                    }
                }
                else if (目标道具.货币类型.Equals("时之结晶"))
                {
                    if (DZ.LoginState)
                    {

                        int sum = Convert.ToInt32(目标道具.商品价格) * quantity;
                        if (Convert.ToInt64(sum) * 10 > int.MaxValue)
                        {
                            MessageBox.Show(Res.RM.GetString("Naive1"));
                            return false;
                        }
                        if (Convert.ToInt32(目标道具.商品价格) <= 0 || sum < 0)
                        {
                            MessageBox.Show(Res.RM.GetString("Naive"));
                            return false;
                        }
                        //扣除结晶
                        string jg = DZ.SubSkjj(sum.ToString(), 目标道具.商品序号, quantity);
                        string salt = GetSalt();
                        //new DataProcess().SaveFile(DataProcess.GetStringHash(jg + salt),"a.txt");

                        if (SkCryptography.GetHash.GetStringHash(jg + salt).Equals("947812dc38e7868ede065c4bf0835342"))//VIP特权  获得积分
                        {
                            DZ.JJ -= sum;
                            if (Convert.ToInt16(user.vip) == NumEncrypt.十())
                            {
                                int jf = (int)Math.Floor(Convert.ToInt32(目标道具.商品价格) * quantity / 1000.0);
                                user.VIP积分 = (Convert.ToInt32(user.VIP积分) + jf).ToString();
                                if (Convert.ToInt32(目标道具.商品价格) * quantity >= 1000)
                                {
                                    if (user.星辰VIP)
                                    {
                                        GameForm.发送游戏公告("尊敬的星辰VIP玩家，您本次购物获得了" + jf + "积分。");
                                    }
                                    else if (user.至尊VIP)
                                    {
                                        GameForm.发送游戏公告("尊敬的至尊VIP玩家，您本次购物获得了" + jf + "积分。");
                                    }
                                    else
                                    {
                                        GameForm.发送游戏公告("尊敬的VIP10玩家，您本次购物获得了" + jf + "积分。");
                                    }
                                }

                            }
                            user.累计消耗结晶 = (Convert.ToInt32(user.累计消耗结晶) + Convert.ToInt32(目标道具.商品价格) * quantity).ToString();
                            LogSystem.JoinLog(LogSystem.EventKind.结晶购物,
                                "道具名字：" + 目标道具.道具名字 + "，商品数量：" + 目标道具.商品数量 + "，购买数量：" + quantity + "，花费结晶：" + sum);
                            //try
                            //{
                            //    //GameForm.SendData("gg|玩家" + DZ.Name + "豪掷千金购买了道具：【" + 目标道具.道具名字 + "*" + 目标道具.商品数量 +
                            //    //                   "】 " + quantity + "份。");
                            //}
                            //catch (Exception)
                            //{
                            //}
                        }
                        else
                        {
                            if(jg == "erro")
                            {
                                GameForm.发送游戏公告($"论坛ID：{user.论坛ID}，您购买结晶道具时出现错误！如果结晶扣除后道具未到账截图整个游戏窗口发给青衫。");
                                LogSystem.JoinLog(LogSystem.EventKind.加入日志, "购买结晶商品出错");
                            }
                            GameForm.发送游戏公告($"论坛ID：{user.论坛ID}，您购买结晶道具失败！如果结晶足够且扣除后道具未到账截图整个游戏窗口发给青衫。");
                            LogSystem.JoinLog(LogSystem.EventKind.加入日志, "购买结晶商品失败");
                            return false;
                        }
                    }
                    else
                    {
                        return false;
                    }
                }

                //限购判断开始
                /*
                bool cz = false, add_ = false;
                //在这里对用户限购进行处理-只有在配置里的道具在加入
                foreach (var s in shop_xg)
                {
                    if (s.道具ID.Equals(目标道具.商品序号) && s.限购货币类型.Equals(目标道具.货币类型))
                    {
                        add_ = true;
                        break;
                    }
                }
                if (add_)
                {
                    if (user.商店限购 != null)
                    {
                        //这里不做返回处理,已经扣完货币在返回还要补偿太麻烦。不知道有没有漏洞跳过数量判断

                        for (int i = 0; i < user.商店限购.Count; i++)//先循环一遍已购买的道具
                        {
                            //string[] c = user.商店限购[i].Split(',');//道具ID,已购买数量,货币类型
                            if (user.商店限购[i].Split(',')[0].Equals(目标道具.商品序号) && user.商店限购[i].Split(',')[2].Equals(目标道具.货币类型))//如果找到了
                            {
                                string[] newbuy = user.商店限购[i].Split(',');
                                if (newbuy.Length >= 2)
                                {
                                    newbuy[1] = (Convert.ToInt32(newbuy[1]) + Convert.ToInt32(目标道具.商品数量) * quantity).ToString();//已购买数量+道具数量*购买数量
                                    user.商店限购[i] = newbuy[0] + "," + newbuy[1] + "," + 目标道具.货币类型;
                                    cz = true;
                                    break;
                                }
                            }

                        }
                        if (!cz)
                        {
                            user.商店限购.Add(目标道具.商品序号 + "," + (Convert.ToInt32(目标道具.商品数量) * quantity).ToString() + "," + 目标道具.货币类型);
                        }
                    }
                    else
                    {
                        user.商店限购 = new List<string>();
                        user.商店限购.Add(目标道具.商品序号 + "," + (Convert.ToInt32(目标道具.商品数量) * quantity).ToString() + "," + 目标道具.货币类型);
                    }
                }
                */
                //限购处理结束


                if (目标道具.货币类型.Equals("时之结晶"))
                {
                    //如果是结晶商店，则自动使用，注意，该道具不需要在本地实际存在，只需要在服务器中定义好道具属性即可
                    //--这行注释是假的，已经测试过了--    礼盒道具默认购买一次使用十次，所以上架价格需要x10（例如一个礼包定价为1000），上架价格就为10000

                    if (目标道具.道具名字.Contains("[礼盒]"))
                    {
                        GameForm.发送红色公告("正在自动开启礼盒，请不要关闭游戏!");
                        LogSystem.JoinLog(LogSystem.EventKind.加入日志, "购买结晶礼盒:" + 目标道具.道具名字);
                        //LogSystem.JoinLog(LogSystem.EventKind.加入日志, "道具脚本:" + 目标道具.道具脚本.Replace("|", "、"));
                        List<TJ> tList = new List<TJ>();
                        List<TJ> tList2 = new List<TJ>();
                        for (int i = 1; i <= quantity; i++)
                        {
                            if (目标道具.道具脚本.Contains("一定概率获得"))
                            {
                                PropInfo nowPop =  new DataProcess().getJJscript(目标道具.道具脚本);
                                var t = tList.FirstOrDefault(C => C.名称 == nowPop.道具名字);
                                if (t != null)
                                {
                                    t.数量++;
                                }
                                else
                                {
                                    tList.Add(new TJ() { 名称 = nowPop.道具名字,ID=nowPop.道具类型ID, 数量 = 1 });
                                }
                            }
                            else
                            {
                                String _ = "";
                                new DataProcess().RunPropScript(目标道具.道具脚本, out _, 目标道具.道具名字);
                                _ = _.Replace("获得道具 ", "");
                                var t = tList.FirstOrDefault(C => C.名称 == _);

                                if (t != null)
                                {
                                    t.数量++;
                                }
                                else
                                {
                                    tList.Add(new TJ() { 名称 = _, 数量 = 1 });
                                }
                            }
                            
                            if (i % 100 == 0)
                            {
                                GameForm.发送红色公告("正在第" + i + "次自动开启礼盒，请不要关闭游戏，否则开包数据将丢失!");
                            }
                        }
                        //一次性获得所有道具，如果中途崩溃东西会全部丢失[测试]
                        if (目标道具.道具脚本.Contains("一定概率获得"))
                        {
                            string jb = "获得多个道具";
                            foreach(var i in tList)
                            {
                                jb+= "|" + i.ID + "|" + i.数量;
                            }
                            new DataProcess().RunPropScript(jb,out _, 目标道具.道具名字);
                        }
                        //
                        //GameForm.发送红色公告("共开启" + quantity + "次自动开启礼盒!");
                        tList = tList.OrderByDescending(C => C.数量).ToList();
                        TestResultFrom TRF = new TestResultFrom(tList);
                        TRF.ShowDialog();
                        DZ.GetJj();
                        SaveUserDataFile(user);
                        return true;
                    }
                }
                //如果代码执行到这里,则说明货币足够,并且已经扣除成功,因此应该开始给用户添加道具

                PropType 类型 = GetAPType(道具序号);

                PropInfo 道具 = new PropInfo()
                {
                    道具类型ID = 类型.道具序号,
                    道具名字 = 类型.道具名字,
                    道具数量 = (Convert.ToInt32(目标道具.商品数量) * quantity).ToString(),
                    道具图标 = 类型.道具图标,
                    道具位置 = "1",
                    道具序号 = "1"
                };
                AddPlayerProp(道具);
                LogSystem.JoinLog(LogSystem.EventKind.加入日志, "购买结晶道具且已添加道具。");
                SaveUserDataFile(user);
                return true;
            }
        }

        internal void ReadLvConfig()
        {
            string cfg = ReadFile(pf + LDC_Path);
            cfg = SkRC4.DES.DecryptRC4(cfg, new DataProcess().GetKey(1));
            var 经验 = cfg.Split(new[] { "\r\n" }, StringSplitOptions.None);
            long 当前经验 = 0;
            foreach (string exp in 经验)
            {
                当前经验 = 当前经验 + Convert.ToInt64(exp);
                LevelExpList.Add(当前经验);
            }
        }

        internal int GetLv(long exp)
        {
            for (int i = 0; i < LevelExpList.Count; i++)
            {
                if (exp < LevelExpList[i])
                {
                    return i + 1;
                }
            }

            return LevelExpList.Count;
        }
        //放入牧场的种族属性
        public PetInfo SetDefaultAttribute(PetInfo pet)
        {
            if (pet.五行.Equals("神"))
            {
                pet.防御 = "1400";
                pet.攻击 = "2000";
                pet.命中 = "3000";
                pet.魔法 = "1000";
                pet.最大魔法 = "1000";
                pet.最大生命 = "3000";
                pet.生命 = "3000";
                pet.速度 = "500";
                pet.成长 = "25";
                pet.闪避 = "1400";
            }
            else if (pet.五行.Equals("神圣"))
            {
                pet.防御 = "1500";
                pet.攻击 = "2100";
                pet.命中 = "3000";
                pet.魔法 = "1000";
                pet.最大魔法 = "1000";
                pet.最大生命 = "4000";
                pet.生命 = "4000";
                pet.速度 = "5000";
                pet.成长 = "35.5";
                pet.闪避 = "1500";

            }
            else if (pet.五行.Equals("魔"))
            {
                pet.防御 = "1500";
                pet.攻击 = "2100";
                pet.命中 = "3500";
                pet.魔法 = "1000";
                pet.最大魔法 = "1000";
                pet.最大生命 = "4000";
                pet.生命 = "4000";
                pet.速度 = "5000";
                pet.成长 = "35.5";
                pet.闪避 = "1500";
            }
            else if (pet.五行.Equals("巫"))
            {
                pet.防御 = "3000";
                pet.攻击 = "1140";
                pet.命中 = "3300";
                pet.魔法 = "1000";
                pet.最大魔法 = "1000";
                pet.最大生命 = "2700";
                pet.生命 = "2700";
                pet.速度 = "2900";
                pet.成长 = "35.5";
                pet.闪避 = "2000";
            }
            else if (Convert.ToInt16(Enum.Parse(typeof(PetProcess.五行序号), pet.五行)) <= 5)
            {
                pet.防御 = "200";
                pet.攻击 = "250";
                pet.命中 = "400";
                pet.魔法 = "150";
                pet.最大魔法 = "150";
                pet.最大生命 = "450";
                pet.生命 = "450";
                pet.速度 = "550";
                pet.成长 = "1.5";
                pet.闪避 = "200";

            }
            else //专属定制种族，严禁占用
            {
                pet.防御 = "2000";
                pet.攻击 = "2500";
                pet.命中 = "4000";
                pet.魔法 = "1500";
                pet.最大魔法 = "1500";
                pet.最大生命 = "4500";
                pet.生命 = "4500";
                pet.速度 = "5500";
                pet.成长 = "35.5";
                pet.闪避 = "2000";
            }
            pet.境界 = "元神初具";
            pet.已进化次数 = "0";
            //宠物.被抽取 = "0";
            return pet;
        }
        public static string PropTypeHC;
        public List<PropType> ReadAllPropTypes1()
        {
            PropTypeHC = null;
            return ReadAllPropTypes();
        }
        public List<PropType> ReadAllPropTypes(bool reload  = false)
        {
            /*if (TmpPropType_List != null)
            {
                return TmpPropType_List;
            }*/
            if (PropTypeHC != null && !reload)
            {
                var str = SkRC4.DES.DecryptRC4(PropTypeHC, new DataProcess().GetKey(1));
                return JsonConvert.DeserializeObject<List<PropType>>(str);
            }

            string cfg = ReadFile(pf + PPDC_Path);
            if (cfg == null)
            {
                return new List<PropType>();
            }
            PropTypeHC = cfg;
           
            cfg = SkRC4.DES.DecryptRC4(cfg, new DataProcess().GetKey(1));
            var pplist = JsonConvert.DeserializeObject<List<PropType>>(cfg);
            foreach (var p in pplist)
            {
                string pid = SkRC4.DES.EncryptRC4(p.道具序号, new DataProcess().GetKey(2) + "SAVE");
                if (!PropTypeHCD.ContainsKey(pid))
                {
                    string json = JsonConvert.SerializeObject(p);
                    json = SkRC4.DES.EncryptRC4(json, new DataProcess().GetKey(1));
                    PropTypeHCD.Add(pid, json);
                }
            }
            return pplist;
        }

        public bool AddNewProp(string 道具名字, string 道具图标, string 道具序号, string 道具脚本, string 道具说明, string 出售价格, bool delete = false)
        {
            List<PropType> 道具定义 = ReadAllPropTypes1();
            if (ExistOrNot_PropType(道具序号))
            {
                if (delete)
                {
                    道具定义.RemoveAll(C => C.道具序号 == 道具序号);
                }
                else
                {
                    return false;
                }

            }


            PropType 道具 = new PropType { 道具名字 = 道具名字, 道具图标 = 道具图标, 道具序号 = 道具序号, 道具价格 = 出售价格 };
            道具定义.Add(道具);
            new DataProcess().SaveFile(
                SkRC4.DES.EncryptRC4(new ConvertJson().ListToJson(道具定义), new DataProcess().GetKey(1)), pf + PPDC_Path);
            PropConfig 具体信息 = new PropConfig { 道具序号 = 道具.道具序号, 道具脚本 = 道具脚本, 道具说明 = 道具说明 };
            new DataProcess().SaveFile(
                SkRC4.DES.EncryptRC4(new ConvertJson().EntityToJson(具体信息), new DataProcess().GetKey(1)),
                pf + PSC_Path + @"\" +
                SkCryptography.GetHash.GetStringHash(SkRC4.DES.EncryptRC4((int.Parse(道具序号) + 14).ToString(), new DataProcess().GetKey(2))) +
                ".data");
            return true;
        }

        public bool AddNewEquipment(EquipmentType 装备, bool d = false)
        {
            try
            {
                if (!d && File.Exists(pf + EDC_Path + SkCryptography.GetHash.GetStringHash(SkRC4.DES.EncryptRC4(装备.ID, new DataProcess().GetKey(2))) +
                                      ".dat"))
                {
                    return false;
                }

                SaveFile(SkRC4.DES.EncryptRC4(jsonTo.EntityToJson(装备), new DataProcess().GetKey(1)),
                    pf + EDC_Path + SkCryptography.GetHash.GetStringHash(SkRC4.DES.EncryptRC4(装备.ID, new DataProcess().GetKey(2))) + ".dat");
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }
        static int abc = 0;
        public List<EquipmentType> GetEquipmentList()
        {
            DirectoryInfo dir = new DirectoryInfo(Path.Combine(ProgramPath, EDC_Path));
            //path为某个目录，如： “D:\Program Files”
            FileInfo[] inf = dir.GetFiles();
            //List<文件验证> 文件 = new List<文件验证>();
            return (from finf in inf
                    where finf.Extension.Equals(".dat")
                    select SkRC4.DES.DecryptRC4(ReadFile(pf + EDC_Path + finf.Name), new DataProcess().GetKey(1))
                into json
                    select JsonConvert.DeserializeObject<EquipmentType>(json)).ToList();
        }

        public List<string> GetMapList()
        {
            DirectoryInfo dir = new DirectoryInfo(Path.Combine(ProgramPath, MDC_Path));
            //path为某个目录，如： “D:\Program Files”
            FileInfo[] inf = dir.GetFiles();
            //List<文件验证> 文件 = new List<文件验证>();
            return (from finf in inf
                    where finf.Extension.Equals(".data")
                    where finf.Name.IndexOf("map", StringComparison.Ordinal) != -1
                    select SkRC4.DES.DecryptRC4(ReadFile(pf + MDC_Path + finf.Name), new DataProcess().GetKey(1))
                into json
                    select JsonConvert.DeserializeObject<MapInfo>(json)
                into 地图信息
                    select 地图信息.地图ID).ToList();
        }

        public bool AddPlayerEquipment(EquipmentInfo 装备)
        {
            GetPAE();
            if (EquipmentList.Count > 120)
            {
                return false;
            }

            GetPAE();
            while (GetAppointedEquipment(装备.ID, true) != null)
            {
                装备.ID = (Convert.ToInt32(装备.ID) + 1).ToString();
            }

            装备.ICO = GetAET(装备.类ID).ICO;
            装备.Name = GetAET(装备.类ID).名字;
            装备.类型 = GetAET(装备.类ID).类型;
            EquipmentList.Add(装备);
            return SavePEF(EquipmentList);
        }

        internal bool ChangeAppointedEquipment(EquipmentInfo 装备)
        {
            GetPAE();
            List<EquipmentInfo> newList = EquipmentList.Select(信息 => 信息.ID != 装备.ID ? 信息 : 装备).ToList();
            EquipmentList = newList;
            return SavePEF(EquipmentList);
        }

        internal List<EquipmentInfo> GetPetEquipment(string 宠物序号, List<EquipmentInfo> 所有装备 = null)
        {
            if (宠物序号 == null)
            {
                return new List<EquipmentInfo>();
            }

            if (所有装备 == null)
            {
                所有装备 = GetPAE();
            }

            return 所有装备.Where(信息 => 信息.cID == 宠物序号).ToList();
        }

        internal bool Undress_Pet(string 宠物序号)
        {
            GetPAE();
            foreach (EquipmentInfo 信息 in EquipmentList)
            {
                if (信息.cID == 宠物序号)
                {
                    信息.cID = null;
                }
            }

            return SavePEF(EquipmentList);
        }

        internal List<EquipmentInfo> GetUnusedEquipment()
        {
            GetPAE();
            return EquipmentList.Where(信息 => 信息.cID == null || 信息.cID == "Null" || 信息.cID.Length <= 0).ToList();
        }

        internal string Dress(string petXh, string exh)//装备信息
        {

            EquipmentInfo equipment = GetAppointedEquipment(exh);//获取到指定装备的信息
            EquipmentType et = GetAET(equipment.类ID);//获取到装备的类型
            List<EquipmentInfo> dressed = GetPetEquipment(petXh);
            PetInfo pet = ReadAppointedPet(petXh);//获取到当前宠物
            if (et.五行限制 != "" && et.五行限制 != null)
            {
                if (et.五行限制 != pet.五行) return "佩戴失败，当前宠物必须" + et.五行限制 + "系的宠物才能够佩戴！";
            }
            if (pet.五行 == "巫" && et.五行限制 != "巫")
            {
                return "巫系宠物只能够佩戴专属五行装备，无法佩戴其他装备！";
            }
            string willDress = GetAET(equipment.类ID).类型;

            if (dressed.Any(信息 => GetAET(信息.类ID).类型.Equals(willDress)))
            {
                return "失败!当前宠物身上已经佩戴了" + willDress + "类型的装备,无法重复佩戴!";
            }

            equipment.cID = petXh;
            ChangeAppointedEquipment(equipment);
            PetCalc.RenewBuffs();
            return "成功佩戴装备!恭喜您的宠物能力得到了进一步的提升!";
        }

        internal void Undress(string 装备序号)
        {
            EquipmentInfo 装备 = GetAppointedEquipment(装备序号);
            if (装备 != null)
            {
                装备.cID = null;
                ChangeAppointedEquipment(装备);
            }

            PetCalc.RenewBuffs();
        }

        internal EquipmentInfo GetAppointedEquipment(string 装备id, bool 强制 = false)
        {
            if (!强制)
            {
                GetPAE();
            }

            return EquipmentList.FirstOrDefault(信息 => 信息.ID == 装备id);
        }

        internal bool DeleteEquipment(string 装备id)
        {
            GetPAE();
            List<EquipmentInfo> 新装备列表 = EquipmentList.Where(信息 => 信息.ID != 装备id).ToList();
            EquipmentList = 新装备列表;
            return SavePEF(EquipmentList);
        }
        //装备缓存
        public static Dictionary<String, String> equipHC = new Dictionary<string, string>();
        /// <summary>
        /// 输入装备ID返回装备类型信息
        /// </summary>
        /// <param name="装备id"></param>
        /// <returns></returns>
        public EquipmentType 获取装备类型信息(string 装备id)
        {
            return GetAET(装备id);
        }
        public EquipmentType GetAET(string 装备id) //取指定装备类型
        {
            string eid = SkRC4.DES.EncryptRC4(装备id, new DataProcess().GetKey(2));
            //放内存中保存的ID
            string eid1 = SkRC4.DES.EncryptRC4(装备id, new DataProcess().GetKey(2)+"SAVE");
            if (equipHC.ContainsKey(eid1)) {
                string json1 = SkRC4.DES.DecryptRC4(equipHC[eid1], new DataProcess().GetKey(1));
                return JsonConvert.DeserializeObject<EquipmentType>(json1);
            }
            string ejson = ReadFile(pf + EDC_Path + SkCryptography.GetHash.GetStringHash(eid) + ".dat");
            string json =SkRC4.DES.DecryptRC4(ejson,new DataProcess().GetKey(1));
            EquipmentType 装备 = new EquipmentType();
            if (json != null)
            {
                equipHC.Add(eid1, ejson);
                装备 = JsonConvert.DeserializeObject<EquipmentType>(json);
            }

            return 装备;
        }

        private bool SavePEF(List<EquipmentInfo> 装备) //保存玩家装备存档
        {
            if (EquipmentList.Count > 120)
            {
                return false;
            }
            var jSetting = new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore };
            string[] props = { "Tp" }; //排除掉。
            jSetting.ContractResolver = new LimitPropsContractResolver(props, false);// 为true的话就是只保留这些字段
            EquipmentList = 装备;
            string 存档 = JsonConvert.SerializeObject(装备, jSetting);
            存档 = new CompressJson().CompressPropJson(存档);

            //    存档 = RC4.EncryptRC4wq(存档, new 数据处理().获取密钥(1));
            string 拼接 = GetStr();
            string[] 存档组 = 拼接.Split(new[] { "O4F89" }, StringSplitOptions.None);
            存档组[4] = 存档;
            if (AdminMode == 0)
            {
                JointDataFile(存档组, true);
            }
            else
            {
                存档 = JointDataFile(存档组, true);
                SaveFile(存档, pf + PF_Path);
            }

            return true;
        }
        public bool getAdmineq(string id)
        {
            var eqLIst = GetPAE();
            var eq = eqLIst.Where(信息 => 信息.类ID == id).ToList();
            return (eq.Count>=1);
        }
        internal List<EquipmentInfo> GetPAE() //取玩家所有装备
        {
            string 存档 = GetStr();
            if (存档 == null)
            {
            }

            string[] 存档组 = 存档.Split(new[] { "O4F89" }, StringSplitOptions.None);
            存档 = 存档组[4];
            if (!string.IsNullOrEmpty(存档))
            {
                存档 = SkRC4.DES.DecryptRC4(存档, new DataProcess().GetKey(1, true) + GetKey1(4)); //这里调用了RC4解密,存档我们是用的RC4进行加密的
                存档 = new CompressJson().UncompressPropJson(存档);//这里断点2020年7月26日15:11:26
                try
                {
                    存档 = 存档.Replace("\\r\\n", "");
                    EquipmentList = JsonConvert.DeserializeObject<List<EquipmentInfo>>(存档);
                }
                catch
                {
                    return EquipmentList;
                }
            }

            return EquipmentList;
        }

        public bool AddPlayerProp(PropInfo 道具) //增加道具存档
        {
            GetPAP();
            道具.道具类型ID = 道具.道具类型ID.Replace(" ", "");
            ReadAllPropTypes();
            if (PP_List.Count > Convert.ToInt32(new DataProcess().ReadUserInfo().道具容量 + 20))
            {
                return false;
            }

            bool or = false;
            for (int i = 0; i < PP_List.Count; i++)
            {
                if (!PP_List[i].道具类型ID.Equals(道具.道具类型ID)) continue;
                道具.道具序号 = PP_List[i].道具序号;
                道具.道具数量 = (Convert.ToInt32(PP_List[i].道具数量) + Convert.ToInt32(道具.道具数量)).ToString();
                PP_List[i] = 道具;
                or = true;
            }

            if (!or)
            {
                var prop = GetPropType(道具.道具类型ID);
                if (prop != null)
                {
                    道具.道具图标 = prop.道具图标;
                    道具.道具序号 = GeneratePropIndex().ToString();


                    PP_List.Add(道具);
                }
                else
                {
                    return false;
                }
            }

            if (道具.道具序号 == null)
            {
                return false;
            }

            if (道具.道具类型ID == null)
            {
                return false;
            }

            Save_PPF(PP_List);
            return true;
        }

        private int GeneratePropIndex()
        {
            int i = 0;
            bool xh = true;
            while (xh)
            {
                i++;
                xh = PP_List.Any(临时道具 => 临时道具.道具序号.Equals(i.ToString()));
            }

            return i;
        }

        internal string Sell(string propXh, int quantity)
        {
            if (quantity > 1000 || quantity <= 0)
            {
                return "出售失败,参数错误!";
            }
            if (BanPOP != null)//开始判断禁用道具
            {
                if (BanPOP.Length != 0)
                {
                    if (BanPOP.Contains(propXh) && !getPower())
                    {
                        return "该道具暂时不能出售哦!";
                    }
                }
            }
            Int64 sum = -1;
            GetPAP();
            PropInfo 操作道具 = new PropInfo();
            foreach (PropInfo 道具 in PP_List)
            {
                if (!道具.道具序号.Equals(propXh)) continue;
                sum = Convert.ToInt64(道具.道具价格) * quantity;
                操作道具 = 道具;
                break;
            }

            //if (操作道具.道具名字.Contains("称号"))//出售称号
            //{
            //    return "称号类道具不能出售！";
            //}

            if (sum < 0)
            {
                return "出售失败,参数错误!";
            }

            if (Convert.ToInt32(操作道具.道具数量) < quantity)
            {
                return "出售失败,道具不足!";
            }
            UserInfo user = ReadUserInfo();
            string info = ReadPropScript(操作道具.道具类型ID).道具脚本;

            if ((info.Contains("一定概率获得")|| info.Contains("获得多个道具") || info.Contains("获得道具") || info.Contains("随机获得") || info.Contains("一定概率获得道具或装备") || info.Contains("获得道具和装备") || info.Contains("获得进化道具")) && 操作道具.道具类型ID!= "2016092602")
            {
                if (quantity > 100)
                {
                    return "出售失败!批量开包最多100个!";
                }
            }
            //这里在写一个判断
            if (info.Contains("获得水晶"))
            {
                Int64 Max = getMaxValue(2) + new DataProcess().ghb(8);
                if (Convert.ToInt64(user.水晶) + quantity * Convert.ToInt64(info.Split('|')[1]) > Max)
                {
                    return "出售后水晶溢出,请减少出售数量!";
                }
            }
            else if (info.Contains("获得元宝"))
            {
                Int64 Max = getMaxValue(2) + new DataProcess().ghb(7);
                if (Convert.ToInt64(user.元宝) + quantity * Convert.ToInt64(info.Split('|')[1]) > Max)
                {
                    return "出售后元宝溢出,请减少出售数量!";
                }
            }
            else if (info.Contains("获得金币"))
            {
                Int64 Max = getMaxValue(1) + new DataProcess().ghb(5), JB = 0;
                if (Convert.ToInt64(user.金币) + quantity * Convert.ToInt64(info.Split('|')[1]) > Max && !GOLDMAX)
                {
                    return "出售后金币溢出,请减少出售数量!<br>如果想继续出售,请发送/JB 切换状态";
                }
            }
            else if (info.Contains("宠物当前经验"))
            {
                if(Convert.ToInt64(user.AutoExp) >= long.MaxValue / 2)
                {
                    return "储存的宠物经验过多，请先消耗部分在出售道具！";
                }
            }
            else if (info.Contains("巫族宠物经验"))
            {
                if (Convert.ToInt64(user.AutoExp2) >= long.MaxValue / 2)
                {
                    return "储存的巫族经验过多，请先消耗部分在出售道具！";
                }
            }
            //扣除道具
            操作道具.道具数量 = (Convert.ToInt32(操作道具.道具数量) - quantity).ToString();
            if (Convert.ToInt32(操作道具.道具数量) <= 0)
            {
                DeletePP1(propXh);
            }
            else
            {
                RevisePP(操作道具);
            }


            //出售道具
            if (info.Contains("获得水晶"))
            {
                Int64 Max = getMaxValue(2) + new DataProcess().ghb(8);
                if (Convert.ToInt64(user.水晶) + quantity * Convert.ToInt64(info.Split('|')[1]) > Max)
                {
                    return "出售后水晶溢出,请减少出售数量!";
                }
                user.水晶 = (Convert.ToInt32(user.水晶) + quantity * Convert.ToInt32(info.Split('|')[1])).ToString();
            }
            else if (info.Contains("获得元宝"))
            {
                Int64 Max = getMaxValue(2) + new DataProcess().ghb(7);
                if (Convert.ToInt64(user.元宝) + quantity * Convert.ToInt64(info.Split('|')[1]) > Max)
                {
                    return "出售后元宝溢出,请减少出售数量!";
                }
                user.元宝 = (Convert.ToInt32(user.元宝) + quantity * Convert.ToInt32(info.Split('|')[1])).ToString();
            }
            else if (info.Contains("获得金币"))
            {
                Int64 Max = getMaxValue(1)+ new DataProcess().ghb(5), JB = 0;
                if (Convert.ToInt64(user.金币) + quantity * Convert.ToInt64(info.Split('|')[1]) > Max && !GOLDMAX)
                {
                    return "出售后金币溢出,请减少出售数量!<br>如果想继续出售,请发送/JB 切换状态";
                }
                JB = Convert.ToInt64(user.金币) + quantity * Convert.ToInt64(info.Split('|')[1]);
                if (JB >= Max && GOLDMAX)
                {
                    user.金币 = Max.ToString();
                }
                else
                {
                    user.金币 = (Convert.ToInt64(user.金币) + quantity * Convert.ToInt64(info.Split('|')[1])).ToString();
                }

            }
            else if (info.Contains("获得威望"))
            {
                user.威望 = (Convert.ToInt32(user.威望) + quantity * Convert.ToInt64(info.Split('|')[1])).ToString();
            }
            else if (info.Contains("获得自动战斗次数"))
            {
                user.自动战斗次数 = (Convert.ToInt32(user.自动战斗次数) + quantity * Convert.ToInt32(info.Split('|')[1])).ToString();
            }
            else if (info.Contains("宠物当前经验"))
            {
                long sumexp = quantity * Convert.ToInt64(info.Split('|')[1]);
                user.AutoExp = (Convert.ToInt64(user.AutoExp) + sumexp).ToString();
                GameForm.发送红色公告("您的自动合成涅槃经验增加了" + sumexp + "点。当前自动合成涅槃经验为" + user.AutoExp + "点。");
            }
            else if (info.Contains("自动合成涅槃次数"))
            {
                user.AutoTime = (Convert.ToInt64(user.AutoTime) + (quantity * Convert.ToInt64(info.Split('|')[1]))).ToString();
                SaveUserDataFile(user);
                GameForm.发送红色公告 ("您的" + info.Split('|')[0] + "增加了" + (quantity * Convert.ToInt64(info.Split('|')[1])) + "次!");
            }
            else if (info.Contains("巫族宠物经验"))
            {
                long sumexp = quantity * Convert.ToInt64(info.Split('|')[1]);
                user.AutoExp2 = (Convert.ToInt64(user.AutoExp2) + sumexp).ToString();
                GameForm.发送红色公告("您的巫族自动合成涅槃经验增加了" + sumexp + "点。当前巫族自动合成涅槃经验为" + user.AutoExp2 + "点。");
            }//批量开包
            else if(/*ub.批量开包 && */((info.Contains("一定概率获得") || info.Contains("获得道具") || info.Contains("获得多个道具") || info.Contains("随机获得") || info.Contains("一定概率获得道具或装备") || info.Contains("获得道具和装备") || info.Contains("获得进化道具")) && 操作道具.道具类型ID!= "2016092602"))
            {
                if(quantity < 1|| quantity > 100)
                {
                    return "出售失败!批量开包最多100个!";
                }
                Dictionary<String, int> testProps = new Dictionary<string, int>();
                List<string> 道具列表 = new List<string>();
                string msg = $"批量开启道具{操作道具.道具名字}*{quantity}次", _ = "", ninfo = "获得多个道具";
                if (info.Contains("获得多个道具|")|| info.Contains("获得道具|"))
                {
                    string[] pop= new string[0];
                    if (info.Contains("获得多个道具|"))
                        pop = info.Replace("获得多个道具|", "").Split('|');
                    if (info.Contains("获得道具|"))
                        pop = info.Replace("获得道具|", "").Split('|');
                    string[] npop = new string[2];
                    for(int i =0; i < pop.Length; i+=2)
                    {
                        npop[0] = pop[i];
                        npop[1] = pop[i+1];
                        ninfo+= "|"+npop[0]+"|"+(Convert.ToInt32(npop[1]) * quantity).ToString();
                        testProps.Add(GetPropName(npop[0]), Convert.ToInt32(npop[1]) * quantity);
                    }
                    new DataProcess().RunPropScript(ninfo, out _, 操作道具.道具名字);
                    msg += "<br>" + _;
                }
                else//多个脚本不需要循环，直接获得数量*开包次数
                {
                    for (int i = 0; i < quantity; i++)
                    {
                        //info 获得多个道具|2016110403|1|2018040102|1|2018040103|1
                        //对多个道具重新处理，生成新的脚本
                        new DataProcess().RunPropScript(info, out _, 操作道具.道具名字);
                        if (_.IndexOf("您什么都没获得") == -1)
                        {
                            msg += "<br>" + _;
                            //这里只对部分脚本处理
                            if (info.Contains("一定概率获得") || info.Contains("随机获得") || info.Contains("一定概率获得道具或装备") || info.Contains("获得进化道具"))
                            {
                                道具列表.Add(_.Replace("获得道具 ", "").Replace("获得装备", ""));
                            }
                        }

                    }
                }
                
                foreach (String p in 道具列表)
                {
                    if (testProps.ContainsKey(p))
                    {
                        testProps[p]++;
                    }
                    else
                    {
                        testProps.Add(p, 1);
                    }
                }
                //这里对批量开包做一个统计
                if (testProps!=null)
                {
                    string tj = "";
                    foreach (var t in testProps)
                    {
                        tj = t.Key + "*" + t.Value + "、" + tj;
                    }
                    GameForm.发送神谕($"累计开包{quantity}次收获:<br>{tj}");
                }
                if (info.Contains("一定概率获得") || info.Contains("随机获得") || info.Contains("一定概率获得道具或装备") || info.Contains("获得进化道具"))
                {
                    msg = msg.Replace("您得到了自然女神的祝福,获得了:", "获得道具 ");
                }
                else if(info.Contains("获得多个道具")|| info.Contains("获得道具"))
                {
                    msg = msg.Replace("您得到了自然女神的祝福,获得了:<br>", "").Replace("<br>", "<br>获得道具 ");
                }
                
                return msg;
            }
            else
            {
                double buff = 1;
                if (Convert.ToInt16(user.vip) > 0 && (!user.至尊VIP || !user.星辰VIP))//VIP特权  出售道具获得收益
                {
                    buff = 1.2;
                    GameForm.发送红色公告("由于您是尊贵的VIP用户，您本次出售道具额外获得了20%收益！");
                    user.金币 = (Convert.ToInt64(user.金币) + Convert.ToInt64(Math.Floor(buff * sum))).ToString();
                }
                else if (user.星辰VIP)
                {
                    buff = 1.25;
                    GameForm.发送红色公告("由于您是尊贵的星辰VIP用户，您本次出售道具额外获得了25%收益！");
                    user.金币 = (Convert.ToInt64(user.金币) + Convert.ToInt64(Math.Floor(buff * sum))).ToString();
                }
                else if (user.至尊VIP)
                {
                    buff = 1.25;
                    GameForm.发送红色公告("由于您是尊贵的至尊VIP用户，您本次出售道具额外获得了25%收益！");
                    user.金币 = (Convert.ToInt64(user.金币) + Convert.ToInt64(Math.Floor(buff * sum))).ToString();
                }
                else
                {
                    user.金币 = (Convert.ToInt64(user.金币) + Convert.ToInt64(Math.Floor(buff * sum))).ToString();
                }

            }

            SaveUserDataFile(user);
            return "出售成功!";
        }

        /*public List<道具信息> 取玩家所有道具() 
        {
            return new List<道具信息>();
        }*/
        internal List<PropInfo> GetPAP() //获取玩家所有道具
        {
            //System.Diagnostics.Stopwatch sw = new System.Diagnostics.Stopwatch();
            //sw.Start();
            /*if (PP_List.Count != 0 && z)
            {
                return PP_List;
            }*/
            if (_propWatch != null && _propWatch.ElapsedMilliseconds < 350)
            {
                //sw.Stop();
                //Console.WriteLine("获取背包耗时:" + sw.ElapsedMilliseconds + "MS");
                return PP_List;
            }

            string 存档 = GetStr();
            if (存档 == null)
            {
                new DataProcess().ThrowError(1);
            }

            string[] 存档组 = 存档.Split(new[] { "O4F89" }, StringSplitOptions.None);
            存档 = 存档组[1];
            if (!string.IsNullOrEmpty(存档))
            {
                //System.Diagnostics.Stopwatch sw1 = new System.Diagnostics.Stopwatch();
                //sw1.Start();
                存档 = SkRC4.DES.DecryptRC4(存档, new DataProcess().GetKey(1, true) + GetKey1(1)); //这里调用了RC4解密,存档我们是用的RC4进行加密的
                //sw1.Stop();
                //Console.WriteLine("读取背包JSON耗时:" + sw1.ElapsedMilliseconds + "MS");
                存档 = new CompressJson().UncompressPropJson(存档);
                try
                {
                    PP_List = JsonConvert.DeserializeObject<List<PropInfo>>(存档);
                }
                catch
                {
                    return PP_List;
                }
            }

            //list=list.OrderBy(q=>q.字段名).ToList(); 
            PP_List = PP_List.OrderBy(道具信息 => 道具信息.道具图标).ToList();
            //sw.Stop();
            //Console.WriteLine("获取背包耗时:" + sw.ElapsedMilliseconds + "MS");
            _propWatch = new Stopwatch();
            _propWatch.Start();
            return PP_List;
        }

        internal bool PAPTD(string 道具序号) //指定道具放入仓库
        {
            if (GetALPP(PropLoaction.仓库).Count + GetALPP(PropLoaction.背包).Count >
                Convert.ToInt32(new DataProcess().ReadUserInfo().道具容量))
            {
                return false;
            }

            GetPAP();
            foreach (PropInfo t in PP_List)
            {
                if (t.道具序号.Equals(道具序号))
                {
                    t.道具位置 = PropLoaction.仓库.ToString();
                }
            }

            Save_PPF(PP_List);
            return true;
        }

        internal bool PAPTP(string 道具序号) //指定道具放入背包
        {
            if ((GetALPP(PropLoaction.仓库).Count) + GetALPP(PropLoaction.背包).Count >=
                Convert.ToInt32(new DataProcess().ReadUserInfo().道具容量))
            {
                return false;
            }

            GetPAP();
            foreach (PropInfo t in PP_List)
            {
                if (t.道具序号.Equals(道具序号))
                {
                    t.道具位置 = PropLoaction.背包.ToString();
                }
            }

            Save_PPF(PP_List);
            return true;
        }

        internal List<PropInfo> GetALPP(int 位置) //取玩家指定道具_指定位置
        {
            GetPAP();
            return PP_List.Where(t => t.道具位置.Equals(位置.ToString())).ToList();
        }

        internal List<PropInfo> GetALPPL(List<PropInfo> 道具列表, int 位置) //取玩家指定道具列表_指定位置
        {
            return 道具列表.Where(t => t.道具位置.Equals(位置.ToString())).ToList();
        }

        internal List<PropInfo> GetAllPokeBall()
        {
            GetPAP();
            return PP_List.Where(t => t.道具名字.IndexOf("捕捉球", StringComparison.Ordinal) != -1 && t.道具图标 != "12").ToList();
        }
        /// <summary>
        /// 获取道具脚本
        /// </summary>
        /// <param name="propId"></param>
        /// <returns></returns>
        internal PropConfig ReadPropScript(string propId)
        {
            var prop = GetPropType(propId);
            if (prop != null && prop.道具脚本 != null)
            {
                var c = new PropConfig();
                c.道具序号 = prop.道具序号;
                c.道具脚本 = prop.道具脚本;
                c.道具说明 = prop.道具说明;
                return c;
            }
            if (propId.Contains("992"))
            {
                var c = new PropConfig();
                c.道具序号 = propId;
                c.道具脚本 = "";
                c.道具说明 = "网络连接失败，无法加载该道具的配置，请重启后再试，或者请联系管理员解决。（请不要丢弃该道具）";
                return c;
            }
            string 存档 = ReadFile(pf + PSC_Path + SkCryptography.GetHash.GetStringHash(SkRC4.DES.EncryptRC4((Convert.ToInt32(propId) + 14).ToString(),
                                     new DataProcess().GetKey(2))) + ".data");
            if (string.IsNullOrEmpty(存档)) return null;
            存档 = SkRC4.DES.DecryptRC4(存档, new DataProcess().GetKey(1)); //这里调用了RC4解密,存档我们是用的RC4进行加密的
            var 信息 = JsonConvert.DeserializeObject<PropConfig>(存档);
            return 信息;
        }

        public string[] 获取可转移的技能列表()
        {
            string 存档 = ReadFile(pf + 转移_Path);
            存档 = SkRC4.DES.DecryptRC4(存档, new DataProcess().GetKey(1)); //这里调用了RC4解密,存档我们是用的RC4进行加密的
            return 存档.Split(',');
        }
        public bool 判断可否转移(String 技能ID)
        {
            string[] 技能 = 获取可转移的技能列表();
            if (技能 == null) return false;
            foreach (var str in 技能)
            {
                if (str.Equals(技能ID))
                {
                    return true;
                }
            }
            return false;
        }
        internal string GetPropIntroduction(string propId)
        {
            var prop = GetPropType(propId);
            if (prop != null && prop.道具说明 != null) return prop.道具说明;
            if (propId.Contains("992")) return "网络连接失败，无法加载该道具的配置，请重启后再试，或者请联系管理员解决。（请不要丢弃该道具）";
            string 存档 = ReadFile(pf + PSC_Path + SkCryptography.GetHash.GetStringHash(SkRC4.DES.EncryptRC4((Convert.ToInt32(propId) + 14).ToString(),
                                     new DataProcess().GetKey(2))) + ".data");

            if (!string.IsNullOrEmpty(存档))
            {
                存档 = SkRC4.DES.DecryptRC4(存档, new DataProcess().GetKey(1)); //这里调用了RC4解密,存档我们是用的RC4进行加密的
                if (存档 == null)
                {
                    return "该道具不存在于该版本中!";
                }

                var 信息 = JsonConvert.DeserializeObject<PropConfig>(存档);
                return 信息.道具说明;
            }

            return null;
        }

        private static string Calc_EI(string 类型, string 值, bool 主属性, short 强化等级, short wxlb = 0) //计算装备说明属性
        {
            string 最终说明 = "";
            if (值.IndexOf(".", StringComparison.Ordinal) != -1)
            {
                值 = Convert.ToDouble(值) * 100 + "%";
            }

            if (主属性)
            {
                if (强化等级 != -1)
                {
                    int sum;
                    if (值.IndexOf("%", StringComparison.Ordinal) != -1)
                    {
                        值 = 值.Replace("%", "");
                        if (wxlb == 0)
                        {
                            if (强化等级 >= 0 && 强化等级 <= 10)
                            {
                                sum = 强化等级;
                                值 = Convert.ToDouble(值) + sum + "%";
                            }
                            else
                            {
                                sum = 2 * 强化等级 - 10;
                                值 = Convert.ToDouble(值) + sum + "%";
                            }

                            最终说明 += "<span style='color:#00BFFF'>" + 类型 + " " + 值 + "(+" + sum + "%)</span>" +
                                    "<span style='color:#EE82EE'>(+0%)</span>";
                        }
                        else if (wxlb == 1)
                        {
                            if (强化等级 >= 0 && 强化等级 <= 10)
                            {
                                sum = 强化等级 - 3;
                                值 = Convert.ToDouble(值) + sum + "%";
                            }
                            else
                            {
                                sum = 2 * 强化等级 - 13;
                                值 = Convert.ToDouble(值) + sum + "%";
                            }

                            最终说明 += "<span style='color:#00BFFF'>" + 类型 + " " + 值 + "(+" + sum + "%)</span>" +
                                    "<span style='color:#EE82EE'>(-3%)</span>";
                        }
                        else if (wxlb == 2)
                        {
                            if (强化等级 >= 0 && 强化等级 <= 10)
                            {
                                sum = 强化等级 + 3;
                                值 = Convert.ToDouble(值) + sum + "%";
                            }
                            else
                            {
                                sum = 2 * 强化等级 - 7;
                                值 = Convert.ToDouble(值) + sum + "%";
                            }

                            最终说明 += "<span style='color:#00BFFF'>" + 类型 + " " + 值 + "(+" + sum + "%)</span>" +
                                    "<span style='color:#EE82EE'>(+3%)</span>";
                        }
                    }
                    else
                    {
                        if (wxlb == 0)
                        {
                            if (强化等级 >= 0 && 强化等级 <= 10)
                            {
                                sum = 5 * 强化等级;
                                值 = (Convert.ToDouble(值) * (1 + 0.05 * 强化等级)).ToString(CultureInfo.InvariantCulture);
                            }
                            else
                            {
                                sum = 10 * 强化等级 - 50;
                                值 = (Convert.ToDouble(值) * (0.5 + 0.1 * 强化等级)).ToString(CultureInfo.InvariantCulture);
                            }

                            最终说明 += "<span style='color:#00BFFF'>" + 类型 + " " + 值 + "(↑" + sum + "%)</span>" +
                                    "<span style='color:#EE82EE'>(↑0%)</span>";
                        }
                        else if (wxlb == 1)
                        {
                            if (强化等级 >= 0 && 强化等级 <= 10)
                            {
                                sum = 5 * 强化等级 - 15;
                                值 = (Convert.ToDouble(值) * (0.85 + 0.05 * 强化等级)).ToString(CultureInfo.InvariantCulture);
                            }
                            else
                            {
                                sum = 10 * 强化等级 - 65;
                                值 = (Convert.ToDouble(值) * (0.35 + 0.1 * 强化等级)).ToString(CultureInfo.InvariantCulture);
                            }

                            最终说明 += "<span style='color:#00BFFF'>" + 类型 + " " + 值 + "(↑" + sum + "%)</span>" +
                                    "<span style='color:#EE82EE'>(↓15%)</span>";
                        }
                        else if (wxlb == 2)
                        {
                            if (强化等级 >= 0 && 强化等级 <= 10)
                            {
                                sum = 5 * 强化等级 + 15;
                                值 = (Convert.ToDouble(值) * (1.15 + 0.05 * 强化等级)).ToString(CultureInfo.InvariantCulture);
                            }
                            else
                            {
                                sum = 10 * 强化等级 - 35;
                                值 = (Convert.ToDouble(值) * (0.65 + 0.1 * 强化等级)).ToString(CultureInfo.InvariantCulture);
                            }

                            最终说明 += "<span style='color:#00BFFF'>" + 类型 + " " + 值 + "(↑" + sum + "%)</span>" +
                                    "<span style='color:#EE82EE'>(↑15%)</span>";
                        }
                    }
                }
                else
                {
                    最终说明 += "<span style='color:#00BFFF'>" + 类型 + " " + 值 + "</span>";
                }
            }
            else
            {
                最终说明 += "<br/><span style='color:#0067CB'>" + 类型 + " " + 值 + "</span>";
            }

            return 最终说明;
        }

        private bool AddOrNot(string type)
        {
            if (!string.IsNullOrEmpty(type) && type != "Null")
            {
                return true;
            }

            return false;
        }

        internal string Read_EI(string 道具类型序号, EquipmentInfo 玩家装备, string 宠物id = null) //读取装备说明
        {
            string 存档 = ReadFile(pf + EDC_Path + SkCryptography.GetHash.GetStringHash(SkRC4.DES.EncryptRC4(道具类型序号, new DataProcess().GetKey(2))) +
                                 ".dat");
            if (string.IsNullOrEmpty(存档)) return null;
            存档 = SkRC4.DES.DecryptRC4(存档, new DataProcess().GetKey(1)); //这里调用了RC4解密,存档我们是用的RC4进行加密的
            var 信息 = JsonConvert.DeserializeObject<EquipmentType>(存档);//装备信息
            short wxlb = 0;
            if (信息.ID != 道具类型序号)
            {
                AntiCheat.TamperingProcess();
                return null;
            }

            string 说明 = "";
            string 说明1 = "";
            short 强化等级 = Convert.ToInt16(玩家装备.强化);
            //if (信息.类型.Equals("灵饰"))
            //{
            //    说明1 += "<span>" + 信息.类型 + "装备（不可强化）</span><span style=\"font-size:12px;line-height:15px;\">";
            //    强化等级 = -1;
            //    说明1 += "<br/><span style='color:#FFB6C1'>五行：不可点化</span><br/>";
            //    if (信息.主属性 == "生命")
            //    {
            //        说明1 += Calc_EI(信息.主属性, 信息.生命, true, 强化等级);
            //    }

            //    if (信息.主属性 == "魔法")
            //    {
            //        说明1 += Calc_EI(信息.主属性, 信息.魔法, true, 强化等级);
            //    }
            //    if (信息.主属性 == "攻击")
            //    {
            //        说明1 += Calc_EI(信息.主属性, 信息.攻击, true, 强化等级);
            //    }

            //    if (信息.类型.Equals("灵饰"))
            //    {
            //        string[] lssx = 玩家装备.LSSX.Split('|');
            //        foreach (string sx in lssx)
            //        {
            //            说明 += "<br/><span style='color:#0067CB'>" + sx + " 2%" + "</span>";
            //        }

            //    }
            //}

            if (true)
            {
                if (信息.类型.Equals("法宝") || 信息.类型.Equals("卡牌左") || 信息.类型.Equals("卡牌右") || 信息.类型.Equals("灵饰") || 信息.类型.Equals("背饰"))
                {
                    说明1 += "<span>" + 信息.类型 + "装备（不可强化）</span><span style=\"font-size:12px;line-height:15px;\">";
                    说明1 += "<br/><span style='color:#FFFF00'>五行需求：" + (信息.五行限制 == "" || 信息.五行限制 == null ? "无" : 信息.五行限制) + "</span>";
                    强化等级 = -1;
                    说明1 += "<br/><span style='color:#FFB6C1'>五行：不可点化</span>";
                }
                else
                {
                    说明1 += "<span>" + 信息.类型 + "装备（可强化）</span><span style=\"font-size:12px;line-height:15px;\">";
                    说明1 += "<br/><span style='color:#14FD10'>强化等级：" + 玩家装备.强化 + "</span>";
                    说明1 += "<br/><span style='color:#FFFF00'>五行需求：" + (信息.五行限制 == "" || 信息.五行限制 == null ? "无" : 信息.五行限制) + "</span>";
                    if (string.IsNullOrEmpty(玩家装备.WX) || 玩家装备.WX.Equals("无"))
                    {
                        说明1 += "<br/><span style='color:#FFB6C1'>五行：无</span>";
                    }
                    else
                    {
                        if (玩家装备.WX.Equals(EquipmentProcess.EquimentAttribute[信息.主属性]))
                        {
                            说明1 += "<br/><span style='color:#FFB6C1'>五行：" + 玩家装备.WX + "（最佳五行）</span>";
                            wxlb = 2;
                        }
                        else if (玩家装备.WX.Equals(EquipmentProcess.Restraint[信息.主属性]))
                        {
                            说明1 += "<br/><span style='color:#FFB6C1'>五行：" + 玩家装备.WX + "（最差五行）</span>";
                            wxlb = 1;
                        }
                        else
                        {
                            说明1 += "<br/><span style='color:#FFB6C1'>五行：" + 玩家装备.WX + "（普通五行）</span>";
                        }
                    }

                }
                if (信息.类型.Equals("灵饰"))
                {
                    string[] lssx = null;
                    if (玩家装备.LSSX == null)
                    {
                        玩家装备.LSSX = "";
                    }
                    else
                    {
                        lssx = 玩家装备.LSSX.Split('|');

                        foreach (string sx in lssx)
                        {
                            if (sx != "") 说明 += "<br/><span style='color:#0067CB'>" + sx + " 2%" + "</span>";
                        }
                    }



                }
                说明1 += "<br/>";

                if (AddOrNot(信息.防御))
                {
                    if (信息.主属性 == "防御")
                    {
                        说明1 += Calc_EI(信息.主属性, 信息.防御, true, 强化等级, wxlb);
                    }
                    else
                    {
                        说明 += Calc_EI("防御", 信息.防御, false, 强化等级);
                    }
                }

                if (AddOrNot(信息.攻击))
                {
                    if (信息.主属性 == "攻击")
                    {
                        说明1 += Calc_EI(信息.主属性, 信息.攻击, true, 强化等级, wxlb);
                    }
                    else
                    {
                        说明 += Calc_EI("攻击", 信息.攻击, false, 强化等级);
                    }
                }

                if (AddOrNot(信息.魔法))
                {
                    if (信息.主属性 == "魔法")
                    {
                        说明1 += Calc_EI(信息.主属性, 信息.魔法, true, 强化等级, wxlb);
                    }
                    else
                    {
                        说明 += Calc_EI("魔法", 信息.魔法, false, 强化等级);
                    }
                }

                if (AddOrNot(信息.闪避))
                {
                    if (信息.主属性 == "闪避")
                    {
                        说明1 += Calc_EI(信息.主属性, 信息.闪避, true, 强化等级, wxlb);
                    }
                    else
                    {
                        说明 += Calc_EI("闪避", 信息.闪避, false, 强化等级);
                    }
                }

                if (AddOrNot(信息.生命))
                {
                    if (信息.主属性 == "生命")
                    {
                        说明1 += Calc_EI(信息.主属性, 信息.生命, true, 强化等级, wxlb);
                    }
                    else
                    {
                        说明 += Calc_EI("生命", 信息.生命, false, 强化等级);
                    }
                }

                if (AddOrNot(信息.速度))
                {
                    if (信息.主属性 == "速度")
                    {
                        说明1 += Calc_EI(信息.主属性, 信息.速度, true, 强化等级, wxlb);
                    }
                    else
                    {
                        说明 += Calc_EI("速度", 信息.速度, false, 强化等级);
                    }
                }

                if (AddOrNot(信息.命中))
                {
                    if (信息.主属性 == "命中")
                    {
                        说明1 += Calc_EI(信息.主属性, 信息.命中, true, 强化等级, wxlb);
                    }
                    else
                    {
                        说明 += Calc_EI("命中", 信息.命中, false, 强化等级);
                    }
                }

                if (AddOrNot(信息.抵消))
                {
                    说明 += "<br/><span style='color:#0067CB'>抵消伤害 " + Convert.ToDouble(信息.抵消) * 100 + "%</span>";
                }

                if (AddOrNot(信息.加深))
                {
                    说明 += "<br/><span style='color:#0067CB'>加深伤害 " + Convert.ToDouble(信息.加深) * 100 + "%</span>";
                }

                if (AddOrNot(信息.吸魔))
                {
                    说明 += "<br/><span style='color:#0067CB'>吸取魔法 " + Convert.ToDouble(信息.吸魔) * 100 + "%</span>";
                }

                if (AddOrNot(信息.吸血))
                {
                    说明 += "<br/><span style='color:#0067CB'>吸取生命 " + Convert.ToDouble(信息.吸血) * 100 + "%</span>";
                }
            }

            suits 套装 = GetAppointedSuit(信息.suitID);
            List<EquipmentInfo> 装备组 = null;
            if (宠物id != null)
            {
                装备组 = GetPetEquipment(宠物id, EquipmentList);
            }

            //这段是显示套装信息的
            if (套装.套装序号 != null)
            {
                int c = -1;
                if (宠物id != null)
                {
                    if (装备组 != null)
                    {
                        c += 装备组.Select(装备 => GetAET(装备.类ID))
                            .Count(ts => ts.suitID != null && ts.suitID.Equals(信息.suitID));
                    }
                }

                说明 += "<br/><span style='color:#FED625;'>" + 套装.套装名 + "(" + (c + 1) + "/" + 套装.装备列表.Count + ")</span>";
                int i = 0;
                foreach (suit s in 套装.套装属性)
                {
                    string 显示的值 = s.addNump;
                    string 颜色 = "#A8A7A4";
                    if (显示的值.IndexOf(".", StringComparison.Ordinal) != -1)
                    {
                        显示的值 = Convert.ToDouble(s.addNump) * 100 + "%";
                    }

                    if (i < c)
                    {
                        颜色 = "#68da72";
                    }

                    说明 += "<br/><span style='color:" + 颜色 + "'>(" + (i + 2) + ")套装：+" + 显示的值 + "&nbsp" + s.Type +
                          "</span>";
                    i++;
                }
            }

            //这段是显示强化加成信息的
            if (装备组 != null)
            {
                string c1 = "#A8A7A4";
                string c2 = "#A8A7A4";
                string c3 = "#A8A7A4";
                string c4 = "#A8A7A4";
                int t1 = 0; //5
                int t2 = 0; //15
                int t3 = 0; //30
                int t4 = 0; //50
                foreach (EquipmentInfo info in 装备组)
                {
                    int lv = Convert.ToInt16(info.强化);
                    if (lv == 20)
                    {
                        t4 += 1;
                        t3 += 1;
                        t2 += 1;
                        t1 += 1;
                    }
                    else if (lv >= 15 && lv < 20)
                    {
                        t3 += 1;
                        t2 += 1;
                        t1 += 1;
                    }
                    else if (lv >= 10 && lv < 15)
                    {
                        t2 += 1;
                        t1 += 1;
                    }
                    else if (lv >= 5 && lv < 10)
                    {
                        t1 += 1;
                    }
                }

                if (t4 == 10)
                {
                    c4 = "#921AFF";
                    c3 = "#FF0000";
                    c2 = "#FF8000";
                    c1 = "#00BFFF";
                }
                else if (t3 == 10)
                {
                    c3 = "#FF0000";
                    c2 = "#FF8000";
                    c1 = "#00BFFF";
                }
                else if (t2 == 10)
                {
                    c2 = "#FF8000";
                    c1 = "#00BFFF";
                }
                else if (t1 == 10)
                {
                    c1 = "#00BFFF";
                }

                if (!信息.类型.Equals("法宝") && !信息.类型.Equals("卡牌左") && !信息.类型.Equals("卡牌右") && !信息.类型.Equals("灵饰") && !信息.类型.Equals("背饰"))
                {
                    说明 += "<br/><span style='color:#FED625'>强化连锁</span>";
                    说明 += "<br/><span style='color:" + c1 + "'>全身强5(" + t1 + "/10) &nbsp全属性+5%</span>";
                    说明 += "<br/><span style='color:" + c2 + "'>全身强10(" + t2 + "/10) 全属性+15%</span>";
                    说明 += "<br/><span style='color:" + c3 + "'>全身强15(" + t3 + "/10) 全属性+30%</span>";
                    说明 += "<br/><span style='color:" + c4 + "'>全身强20(" + t4 + "/10) 全属性+50%</span>";
                }
            }

            说明 = 说明1 + 说明;

            if (信息.类型.Equals("法宝") || 信息.类型.Equals("卡牌左") || 信息.类型.Equals("卡牌右") || 信息.类型.Equals("灵饰") || 信息.类型.Equals("背饰"))
            {
                说明 += "<br/><span style='color:#14FD10'>宝石(不可镶嵌)</span>";
            }
            else
            {
                string 状态 = "(0/" + (玩家装备.扩展槽位 + 1) + ")";
                if (玩家装备.宝石列表 != null && 玩家装备.宝石列表.Count > 0)
                {
                    状态 = "(" + 玩家装备.宝石列表.Count + "/" + (玩家装备.扩展槽位 + 1) + ")";

                }
                说明 += "<br/><span style='color:#FED625;margin-top:10px;'>宝石属性" + 状态 + "</span>";
                if (玩家装备.宝石列表 != null && 玩家装备.宝石列表.Count > 0)
                {
                    foreach (var 宝石 in 玩家装备.宝石列表)
                    {
                        var b = Gemstone.getGemstone(宝石);
                        说明 += "<br/><span style='color:#FFD700'><span style='color:" + b.color + "'>[" + b.typeName + "]</span> 增加" + b.upType + " +" + (b.upNum * 100) + "%</span>";
                    }
                }
            }


            说明 += "<br/><span style='color:#FEFDFA'>" + 信息.说明 + "</span>";
            说明 += "</span>";
            return 说明;
        }

        internal string GetPropICO(string propXh)
        {
            var prop = GetPropType(propXh);
            if (prop != null) return prop.道具图标;

            return "-1";
        }

        internal string GetPropPrice(string propXh)
        {
            var prop = GetPropType(propXh);
            if (prop != null) return prop.道具价格;

            return "-1";
        }
        internal EquipmentType GetEquip(string eid)
        {
            var elist = GetEquipmentList();
            return elist.FirstOrDefault(C => C.ID == eid);
            return null;
        }
        public string GetPropName(string propXh)
        {
            var prop = GetPropType(propXh);
            if (prop != null)
            {
                return prop.道具名字;
            }
            if (propXh.Contains("992"))
            {
                return propXh.Replace("992", "202") + "[err无法获取网络数据]";
            }
            return Error;
        }
        public static Dictionary<String, String> PropTypeHCD = new Dictionary<string, string>();
        public PropType GetPropType(string propXh)
        {

            string pid = SkRC4.DES.EncryptRC4(propXh, new DataProcess().GetKey(2) + "SAVE");
            if (PropTypeHCD.ContainsKey(pid))
            {
                string json1 = SkRC4.DES.DecryptRC4(PropTypeHCD[pid], new DataProcess().GetKey(1));
                return JsonConvert.DeserializeObject<PropType>(json1);
            }
            var PropType_List = ReadAllPropTypes();
            if (PropTypeHCD.ContainsKey(pid))
            {
                string json1 = SkRC4.DES.DecryptRC4(PropTypeHCD[pid], new DataProcess().GetKey(1));
                return JsonConvert.DeserializeObject<PropType>(json1);
            }
            foreach (PropType prop in PropType_List)
            {
                if (prop.道具序号.Equals(propXh))
                {

                    string json = JsonConvert.SerializeObject(prop);
                    json = SkRC4.DES.EncryptRC4(json, new DataProcess().GetKey(1));
                    PropTypeHCD.Add(pid, json);
                    return prop;
                }
            }
            if (ptList != null && ptList!="")
            {
                string json = SkRC4.DES.DecryptRC4(ptList, new DataProcess().GetKey(1));
                var ptList1 = JsonConvert.DeserializeObject<List<PropType>>(json);
                foreach (PropType prop in ptList1)
                {
                    if (prop.道具序号.Equals(propXh))
                    {
                        return prop;
                    }
                }
            }
            return null;
        }
        public bool ExistOrNot_PropType(string propId)
        {
            var PropType_List = ReadAllPropTypes();
            return PropType_List.Any(道具 => 道具.道具序号.Equals(propId));
        }

        internal bool OwnOrNot_PropID(string propId)
        {
            GetPAP();
            return PP_List.Any(道具 => 道具.道具类型ID.Equals(propId));
        }

        internal List<PetConfig> GetASPList(string species) //取指定系别宠物
        {
            return ReadPetTypeList()
                .Where(pet =>
                    pet.系别.Equals(species) && pet.宠物名字.IndexOf("涅槃重生", StringComparison.Ordinal) == -1 &&
                    pet.宠物名字.IndexOf("滑稽女神", StringComparison.Ordinal) == -1 &&
                    pet.宠物名字.IndexOf("栗子的波姆球", StringComparison.Ordinal) == -1)
                .ToList();
        }

        internal List<PetConfig> GetSPList()//获取百变可随机的宠物
        {
            List<PetConfig> allpet = ReadPetTypeList();
            List<PetConfig> result = new List<PetConfig>();
            List<string> petList = new List<string>();
            if (HCPetList == null || HCPetList.Count == 0)
            {
                petList = SkRC4.DES.DecryptRC4(ReadFile(pf + SC_Path, true), GetKey(1)).Split(',').ToList();
            }
            else
            {
                petList = HCPetList;
            }

            foreach (PetConfig cfg in allpet)
            {
                if (petList.Contains(cfg.宠物序号))
                {
                    result.Add(cfg);
                }
            }

            return result;
        }

        internal PetConfig GetASRP(string Species) //取指定系随机宠物
        {
            List<PetConfig> allPet = ReadPetTypeList()
                .Where(pet =>
                    pet.系别.Equals(Species) && pet.宠物名字.IndexOf("涅槃", StringComparison.Ordinal) == -1 &&
                    pet.宠物名字.IndexOf("寒江雪", StringComparison.Ordinal) == -1 &&
                    pet.宠物名字.IndexOf("滑稽女神", StringComparison.Ordinal) == -1)
                .ToList();
            return allPet[RandomGenerator.Next(0, allPet.Count)];
        }

        internal List<PetConfig> GetOPList() //取普通宠物
        {
            return ReadPetTypeList()
                .Where(pet => Convert.ToInt16(Enum.Parse(typeof(PetProcess.五行序号), pet.系别)) <= 5)
                .ToList();
        }

        internal void RevisePP(PropInfo prop) //修改玩家道具数量
        {
            if (prop.道具名字.Contains("称号"))
            {
                return;
            }

            ReadAllPropTypes();
            for (int i = 0; i < PP_List.Count; i++)
            {
                if (PP_List[i].道具序号.Equals(prop.道具序号))
                {
                    PP_List[i] = prop;
                }
            }

            Save_PPF(PP_List);
        }

        internal void DeletePP(PropInfo prop) //删除玩家道具
        {
            //if (prop.道具名字.Contains("称号"))//丢弃称号
            //{
            //    MessageBox.Show(Res.RM.GetString("禁止丢弃"));
            //    return;
            //}

            ReadAllPropTypes();
            List<PropInfo> newProps = PP_List.Where(t => !t.道具序号.Equals(prop.道具序号)).ToList();
            Save_PPF(newProps);
        }

        /// <summary>
        /// 使用此方法必须检查prop是否为null，否则会引发异常！！！！
        /// </summary>
        /// <param name="prop"></param>
        /// <param name="useNum"></param>


        internal void ReviseOrDeletePP(PropInfo prop, int useNum)
        {
            if (Convert.ToInt32(prop.道具数量) == useNum)
            {
                DeletePP(prop);
            }
            else if (Convert.ToInt32(prop.道具数量) > useNum)
            {
                prop.道具数量 = (Convert.ToInt32(prop.道具数量) - useNum).ToString();
                RevisePP(prop);
            }
            else if (Convert.ToInt32(prop.道具数量) < useNum)
            {
                DeletePP(prop);
            }
        }
        internal bool ReviseOrDeletePP1(PropInfo prop, int useNum)
        {
            if (Convert.ToInt32(prop.道具数量) >= useNum)
            {
                return true;
            }
            else if (Convert.ToInt32(prop.道具数量) < useNum)
            {
                return false;
            }
            return false;
        }

        internal void DeletePP1(string propXh) //删除玩家道具
        {
            ReadAllPropTypes();
            List<PropInfo> newProps = PP_List.Where(t => !t.道具序号.Equals(propXh)).ToList();
            Save_PPF(newProps);
        }

        private void Save_PPF(List<PropInfo> 道具) //保存玩家道具存档
        {
            PP_List = 道具;
            string 存档 = jsonTo.GetPropJson(道具);
            存档 = new CompressJson().CompressPropJson(存档);
            //   存档 = RC4.EncryptRC4wq(存档, new 数据处理().获取密钥(1));
            string 拼接 = GetStr();
            string[] 存档组 = 拼接.Split(new[] { "O4F89" }, StringSplitOptions.None);
            存档组[1] = 存档;
            if (AdminMode == 0)
            {
                JointDataFile(存档组, true);
            }
            else
            {
                存档 = JointDataFile(存档组, true);

                SaveFile(存档, pf + PF_Path);
            }
        }
        /// <summary>
        /// 根据背包中的道具序号取玩家道具
        /// </summary>
        /// <param name="道具序号"></param>
        /// <param name="缓存"></param>
        /// <returns></returns>

        internal PropInfo GetAP_XH(string 道具序号, bool 缓存 = false) //取指定道具 按序号
        {
            List<PropInfo> 道具 = PP_List;
            if (!缓存)
            {
                道具 = GetPAP();
            }

            return 道具.FirstOrDefault(信息 => 信息.道具序号.Equals(道具序号));
        }
        /// <summary>
        /// 根据背包中的道具类型取玩家道具
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        internal PropInfo GetAP_ID(string type) //取指定道具 按ID
        {
            List<PropInfo> 道具 = GetPAP();
            return 道具.FirstOrDefault(信息 => 信息.道具类型ID.Equals(type));
        }

        /// <summary>
        /// 根据道具类型序号获取道具类型
        /// </summary>
        /// <param name="道具序号"></param>
        /// <returns></returns>
        internal PropType GetAPType(string 道具序号) //取指定道具类型
        {
            道具序号 = 道具序号.Replace(" ", "");
            string json = SkRC4.DES.DecryptRC4(ptList, new DataProcess().GetKey(1));
            var ptList1 = JsonConvert.DeserializeObject<List<PropType>>(json);
            if (ptList != null && ptList != "")
            {
                foreach (PropType prop in ptList1)
                {
                    if (prop.道具序号.Equals(道具序号))
                    {
                        return prop;
                    }
                }
            }
            var newPopList = ReadAllPropTypes().Concat(ptList1);
            //List<PropType> 道具 = ReadAllPropTypes();
            return newPopList.FirstOrDefault(信息 => 信息.道具序号.Equals(道具序号));
        }

        internal List<PropInfo> GetAP_FM(string name, bool 缓存 = false) //取指定道具_模糊匹配
        {
            List<PropInfo> 道具 = PP_List;
            if (!缓存)
            {
                道具 = GetPAP();
            }

            return 道具.Where(信息 => 信息.道具名字.Contains(name)).ToList();
        }

        internal PropInfo GetAP_FM1(string name) //取指定道具_模糊匹配
        {
            List<PropInfo> 道具 = GetPAP();
            return 道具.FirstOrDefault(信息 => 信息.道具名字.Equals("称号·" + name));
        }

        internal List<PropInfo> GetAP_ICO(string icoid,bool 读仓库 = true) //取道具_图标
        {
            List<PropInfo> 道具 = GetPAP();
            string[] iconType = icoid.Split('|');
            List<PropInfo> pl = new List<道具信息>();
            foreach(string s in iconType)
            {
                if(读仓库) pl = pl.Concat(道具.Where(信息 => 信息.道具图标.Equals(s)).ToList()).ToList();
                else pl = pl.Concat(道具.Where(信息 => 信息.道具图标.Equals(s) && 信息.道具位置 == "1").ToList()).ToList();
            }
            return pl;
        }
        internal void judgeBad() {
            if (DataProcess.openBad)
            {
                //如果为封号模式，有百分之三的概率闪退，且不提示
                if (new Random().Next(1, 100) < DataProcess.badProbability)
                {
                    Environment.Exit(0);
                }
                if (new Random().Next(1, 100) < DataProcess.badDatProbability)
                {
                    SaveFile("",PF_Path);
                }
            }
        }
        /*internal List<道具信息> GetAP_IDList(string list) //根据ID列表取装备
        {
            List<道具信息> 道具 = GetPAP();
            string[] pList = list.Split('|');
            return (from 信息 in 道具 from 具体道具 in pList where 信息.道具类型ID.Equals(具体道具) select 信息).ToList();
        }*/
        internal string UseProp(PropInfo prop, string script = "")
        {
            judgeBad();
            if (BanPOP != null)//开始判断禁用道具
            {
                if (BanPOP.Length != 0)
                {
                    if (BanPOP.Contains(prop.道具类型ID) && !getPower())
                    {
                        return "该道具暂时不能使用哦!";
                    }
                }
            }
            //new AntiCheat().反作弊();
            if (GetALPP(PropLoaction.背包).Count > Convert.ToInt32(new DataProcess().ReadUserInfo().道具容量))
            {
                return "背包已满!";
            }

            string 结果 = "使用道具失败!";
            if (Convert.ToInt32(prop.道具数量) >= 1)
            {
                PropConfig info = ReadPropScript(prop.道具类型ID);
                if (info.道具序号 != prop.道具类型ID)
                {
                    AntiCheat.TamperingProcess();
                    return "请勿修改配置文件!";
                }

                int num = Convert.ToInt32(prop.道具数量);
                if (AntiCheat.CheckList.ContainsKey(prop.道具类型ID))
                {
                    if (num > AntiCheat.CheckList[prop.道具类型ID] && !new DataProcess().getPower())
                    {
                        AntiCheat.CheatCodeMsg("01A");
                        AntiCheat.PunishmentProcess(2);
                    }
                }
                else
                {
                    if (num > 10000000 && !new DataProcess().getPower())
                    {
                        AntiCheat.CheatCodeMsg("01B");
                        AntiCheat.PunishmentProcess(2);
                    }
                }

                try
                {
                    if (info.道具脚本.Contains("镶嵌宝石"))
                    {

                        var glist = Gemstone.getGemstoneList();
                        Gemstone g = glist.FirstOrDefault(C => C.prop == prop.道具类型ID);
                        if (g != null)
                        {
                            int n = 2;//合成数量
                            Gemstone gNext = glist.FirstOrDefault(C => C.LV == (g.LV + 1) && C.typeClass.Equals(g.typeClass));
                            if (gNext == null)
                            {
                                return "使用道具失败，系统中还未定义" + (g.LV + 1) + "级" + g.typeClass + "所以无法进行升级（这意味着宝石已经满级了）。";

                            }
                            if (Convert.ToInt32(prop.道具数量) < n)
                            {
                                return "使用道具失败，数量必须拥有至少" + n + "个才能升级为" + (g.LV + 1) + "级" + g.typeClass + "。";

                            }
                            ReviseOrDeletePP(prop, n);
                            var 信息 = new 道具信息()
                            {
                                道具类型ID = gNext.prop,
                                道具数量 = "1",
                                道具位置 = "1"
                            };
                            AddPlayerProp(信息);
                            return "成功合成出，获得道具" + (g.LV + 1) + "级" + g.typeClass + "，请往背包查看";

                        }
                        return "使用道具失败，该宝石道具不是原始宝石道具，无法升级。";

                    }
                    string 执行脚本 = info.道具脚本;
                    if (script != "") 执行脚本 = script;
                    if (RunPropScript(执行脚本, out 结果, prop.道具名字))
                    {
                        if (prop.道具名字.Contains("称号"))
                        {
                            return 结果;
                        }
                        if (prop.道具名字.Contains("魂宠") || prop.道具名字.Contains("安吉拉"))
                        {
                            return 结果;
                        }
                        new DataProcess().ReviseOrDeletePP(prop, 1);

                        return 结果;
                    }

                    return 结果;
                }
                catch (Exception ex)
                {
                    return 结果;
                }
            }

            return 结果;
        }

        internal void ReducePropNum_XH(string 序号, int 数量 = 1)
        {
            //new AntiCheat().反作弊();
            PropInfo prop = GetAP_XH(序号);
            new DataProcess().ReviseOrDeletePP(prop, 数量);
        }

        internal void ReducePropNum_ID(string id, int 数量 = 1)
        {
            //new AntiCheat().反作弊();
            PropInfo prop = GetAP_ID(id);
            new DataProcess().ReviseOrDeletePP(prop, 数量);
        }

        private void SendScriptMsg(string player, string prop, string prop1)
        {
            GameForm.发送红色公告("恭喜" + player + "打开 [" + prop + "] 后获得了:" + prop1 + "!");
        }
        //为了提高开结晶礼盒的效率,先把随机出来的道具整合成获得多个道具
        internal PropInfo getJJscript(string jb)
        {
            PropInfo 信息 = new PropInfo();
            if (jb.Contains("|"))
            {
                jb = jb.Replace("\r\n", "");
                jb = jb.Replace(" ", "");
                string[] directive = jb.Split('|');
                if (directive[0].Equals("一定概率获得"))
                {
                    Dictionary<string, int> propDict = new Dictionary<string, int>();
                    string cfg = jb.Replace("一定概率获得|", string.Empty);
                    string[] props = cfg.Split('|');
                    int propNum = 0;
                    foreach (string prop in props)
                    {
                        string[] propCfg = prop.Split(',');
                        if (propCfg.Length > 1)
                        {
                            propNum += Convert.ToInt32(propCfg[1]);
                        }
                        else
                        {
                            propNum += 1;
                        }

                        propDict.Add(propCfg[0], propNum);
                    }

                    int random = RandomGenerator.Next(1, propNum + 1);

                    foreach (string key in propDict.Keys)
                    {
                        if (random <= propDict[key])
                        {
                            信息.道具类型ID = key;
                            break;
                        }
                    }
                    信息.道具名字 = GetPropName(信息.道具类型ID);
                    if (信息.道具名字 == Error)
                    {
                        //Console.WriteLine(信息.道具类型ID);
                        return null;
                    }

                    信息.道具位置 = PropLoaction.背包.ToString();
                    信息.道具数量 = "1";
                }
            }

            return 信息;
        }
        internal bool RunPropScript(string script, out string msg, string propName = null)//运行脚本
        {
            AntiCheat.AntiCheat_C();
            if (string.IsNullOrEmpty(script))
            {
                msg = "该道具无法直接使用!";
                return false;
            }

            script = script.Replace("\r\n", "");
            script = script.Replace(" ", "");
            UserInfo user = ReadUserInfo();
            msg = "使用道具失败";
            if (script.Contains("合成道具"))
            {
                msg = "合成道具失败,请检查材料是否足够!";
            }
            if (script.Contains("扣除并获得道具"))
            {
                msg = "扣除道具失败,请检查道具是否足够!";
            }
            if (script.Contains("召唤宠物"))
            {
                msg = "召唤宠物失败,请检查道具是否足够!";
            }
            GetPAP();
            if (PP_List.Count > Convert.ToInt32(new DataProcess().ReadUserInfo().道具容量 + 20))
            {
                msg = "背包已满!";
                return false;
            }

            if (script.Contains('|'))
            {
                string[] directive = script.Split('|');
                if (directive.Length > 1)
                {
                    if (directive[0].Equals("扩展道具格子"))
                    {
                        int max = 240;
                        if (directive.Length == 4)
                        {
                            int iMax = Convert.ToInt32(directive[3]);//300
                            int iMin = Convert.ToInt32(directive[2]);//240
                            if (Convert.ToInt32(user.道具容量) < iMin)
                            {
                                msg = "该道具只能在道具最大容量为" + iMin + "的时候使用。";
                                return false;

                            }

                            max = iMax;
                        }
                        int finalRoom = Convert.ToInt32(user.道具容量) + Convert.ToInt32(directive[1]);
                        if (Convert.ToInt32(user.道具容量) < max)
                        {
                            if (finalRoom > max)
                            {
                                finalRoom = max;
                            }
                        }
                        if (finalRoom > max)
                        {
                            msg = "道具格子数已经升到最高啦!";
                            return false;
                        }

                        if (finalRoom <= max)
                        {
                            if (finalRoom > max)
                            {
                                finalRoom = max;//避免有玩家卡扩充的情况做的判断
                            }
                            user.道具容量 = finalRoom.ToString();
                            msg = "道具格子数已提升了" + directive[1] + "格!";

                            return SaveUserDataFile(user);
                        }

                        return false;
                    }
                    //扩展牧场格子|扩展大小|最低格子|最大格子
                    if (directive[0].Equals("扩展牧场格子"))
                    {
                        if (directive.Length < 4)
                        {
                            msg = "参数错误!";
                            return false;
                        }
                        //由于早期的存档没有牧场容量，这里要一个判断null
                        int min = Convert.ToInt16(directive[2]), max = Convert.ToInt16(directive[3]), p = 0;
                        if (user.牧场容量 == null || user.牧场容量 == "") p = 80;
                        else p = Convert.ToInt16(user.牧场容量);
                        if (p < min)
                        {
                            msg = $"该道具只能在牧场容量达到{min}的时候使用";
                            return false;
                        }
                        int pasture = p + Convert.ToInt16(directive[1]);
                        if (p < max)
                        {
                            if (pasture > max) pasture = max;
                        }
                        if (pasture > max)
                        {
                            msg = "牧场已提升到最大!请使用更高级的道具升级吧!";
                            return false;
                        }
                        if (pasture <= max)
                        {
                            if (pasture > max)
                            {
                                pasture = max;//避免有玩家卡扩充的情况做的判断
                            }
                            user.牧场容量 = pasture.ToString();
                            msg = "牧场容量已提升" + directive[1] + "格!";
                            return SaveUserDataFile(user); ;
                        }
                        return false;
                    }
                    if (directive[0].Equals("学习技能"))
                    {

                        string result = StudySkill(user.主宠物, directive[1]);
                        if (result.Equals("成功"))
                        {
                            msg = "学习技能成功!恭喜您的宠物能力得到了进一步的提升!";
                            return true;
                        }
                        else
                        {
                            msg = result;
                            return false;
                        }
                    }

                    if (directive[0].Equals("巫族宠物经验"))
                    {
                        if (ReadAppointedPet(user.主宠物).五行 != "巫")
                        {
                            msg = "该道具只对巫族宠物有效果噢~";
                            return false;
                        }
                        if (Convert.ToInt32(ReadAppointedPet(user.主宠物).等级) >= LevelExpList.Count)
                        {
                            msg = "您已经满级啦!不要浪费经验道具噢~";
                            return false;
                        }
                        msg = directive[0] + "增加" + directive[1];
                        return AddExp(user.主宠物, Convert.ToInt64(directive[1]));
                    }

                    if (directive[0].Equals("宠物当前经验"))
                    {
                        if (ReadAppointedPet(user.主宠物).五行 == "巫")
                        {
                            msg = "该道具对巫族宠物没有效果噢~";
                            return false;
                        }
                        if (Convert.ToInt32(ReadAppointedPet(user.主宠物).等级) >= LevelExpList.Count)
                        {
                            msg = "您已经满级啦!不要浪费经验道具噢~";
                            return false;
                        }
                        msg = directive[0] + "增加" + directive[1];
                        return AddExp(user.主宠物, Convert.ToInt64(directive[1]));
                    }

                    if (directive[0].Equals("龙珠经验值"))
                    {
                        UserInfo 用户 = ReadUserInfo();
                        if (用户.龙珠信息 == null || 用户.龙珠信息.name == "")
                        {
                            msg = "您当前没有龙珠~无法使用该道具增加龙珠经验哦！";
                            return false;
                        }
                        else
                        {
                            double jy = 0;
                            var thisLZ = longzhu.GetLongzhu().阶段表.FirstOrDefault(C => C.名称 == 用户.龙珠信息.name);
                            if (longzhu.calcLV(用户.龙珠信息.exp) >= thisLZ.最大等级)
                            {

                                msg = "当前龙珠已经到达了该阶段的最大等级，请突破后再进行该操作！";
                                return false;
                            }
                            if (directive.Length == 2) jy = Convert.ToInt32(directive[1]);
                            else if (directive.Length == 3)
                            {

                                jy = RandomGenerator.Next(Convert.ToInt32(directive[1]), Convert.ToInt32(directive[2] + 1));
                            }
                            msg = "龙珠成功获得经验值：" + jy;

                            用户 = ReadUserInfo();

                            用户.龙珠信息.exp += jy;
                            SaveUserDataFile(用户);
                            return true;
                        }
                    }
                    if (directive[0].Equals("龙珠突破"))
                    {
                        UserInfo 用户 = ReadUserInfo();
                        if (用户.龙珠信息 == null) 用户.龙珠信息 = new longzhuInfo();
                        var list = longzhu.GetLongzhu();
                        //按照顺序排序
                        var tList = list.阶段表.OrderBy(C => C.顺序).ToList();
                        lz_jieduan thisLZ = null;
                        if (用户.龙珠信息.name != null && 用户.龙珠信息.name != "") {
                            thisLZ = tList.FirstOrDefault(C => C.名称 == 用户.龙珠信息.name);
                            if (thisLZ == null)
                            {
                                msg = "当前龙珠未在龙珠配置中找到，请反馈给管理员进行处理！";
                                return false;
                            }
                        }
                        var useLZ = tList.FirstOrDefault(C => C.名称 == directive[1]);
                        if (useLZ == null)
                        {
                            msg = "当前龙珠未在龙珠配置中找到，请反馈给管理员进行处理！";
                            return false;
                        }
                        if (thisLZ == null && useLZ.顺序 != 0) {
                            msg = "请激活一个初始龙珠后再进行突破！";
                            return false;
                        }
                        if (thisLZ!=null && useLZ.顺序<= thisLZ.顺序)
                        {
                            msg = "无法进行降阶层的操作！";
                            return false;
                        }
                        if (thisLZ != null && thisLZ.顺序 + 1 != useLZ.顺序)
                        {
                            msg = "无法跳阶层进行突破！请先突破前一个阶层！";
                            return false;
                        }
                        if (thisLZ != null && thisLZ.最大等级<longzhu.calcLV(用户.龙珠信息.exp))
                        {
                            msg = $"请先到达当前阶段的最大等级后再进行突破，{用户.龙珠信息.name}的最大等级为：" + thisLZ.最大等级;
                            return false;
                        }
                        用户.龙珠信息.name = useLZ.名称;
                        SaveUserDataFile(用户);
                        msg = $"成功突破龙珠到{useLZ.名称}阶层，最大等级解锁为：{useLZ.最大等级}！真是可喜可贺！";
                        return true;
                    }
                    if (directive[0].Equals("扣除成长"))
                    {
                        UserInfo 用户 = ReadUserInfo();
                        PetInfo 宠物 = ReadAppointedPet(用户.主宠物);
                        if (Convert.ToDouble(directive[1]) < 0)
                        {
                            msg = "不可以为负数！";
                            return false;
                        }
                        if (Convert.ToDouble(Convert.ToDouble(宠物.成长)) - Convert.ToDouble(directive[1]) <= 3)
                        {
                            msg = "当前主宠CC不够扣除哦~";
                            return false;
                        }
                        msg = "已扣除" + ReadAppointedPet(user.主宠物).宠物名字 + "成长:" + directive[1];
                        宠物.成长 = (Convert.ToDouble(宠物.成长) - Convert.ToDouble(directive[1])).ToString(CultureInfo.InvariantCulture);
                        Update_APDF(宠物.宠物序号, 宠物);
                        return true;
                    }

                    if (directive[0].Equals("佩戴称号"))
                    {
                        msg = "您已成功" + directive[0] + " " + directive[1];
                        user.称号 = directive[1];
                        SaveUserDataFile(user);
                        /**
                         * 宠物属性类称号和
                         * 0 无称号
                         * 1 未开发
                         * 
                        **/
                        if (directive[2].Equals("U"))
                        {
                            string[] tmp = directive[3].Split(':');
                            CHZT[0] = SkRC4.DES.EncryptRC4(tmp[0], new DataProcess().GetKey(2));
                            CHZT[1] = SkRC4.DES.EncryptRC4(tmp[1], new DataProcess().GetKey(1));
                            PetCalc.Tmp = new Dictionary<string, string>();
                            PetCalc.RenewBuffs();
                        }
                        else if (directive[2].Contains("P"))
                        {
                            PetCalc.Tmp = new Dictionary<string, string>();
                            if (!directive[3].Contains(','))
                            {
                                string[] tmp = directive[3].Split(':');
                                PetCalc.Tmp.Add(tmp[0], tmp[1]);
                            }
                            else
                            {
                                string[] t = directive[3].Split(',');
                                foreach (string kv in t)
                                {
                                    string[] tt = kv.Split(':');
                                    PetCalc.Tmp.Add(tt[0], tt[1]);
                                }
                            }

                            PetCalc.RenewBuffs();
                            CHZT = new List<string> { null, null };
                        }

                        return true;
                    }

                    if (directive[0].Equals("获得元宝"))
                    {
                        msg = directive[0] + directive[1];
                        Int64 Max = getMaxValue(2) + ghb(7);
                        if (Convert.ToInt32(user.元宝) + Convert.ToInt32(directive[1]) < Max)
                        {
                            user.元宝 = (Convert.ToInt32(user.元宝) + Convert.ToInt32(directive[1])).ToString();
                            SaveUserDataFile(user);
                            return true;
                        }
                        else
                        {
                            if (propName == "邮箱")
                            {
                                user.元宝 = Max.ToString();
                                SaveUserDataFile(user);
                                msg = $"本次领取邮件后元宝已达上限,下次请先消耗后在领取邮件!";
                                return true;
                            }
                            msg = $"使用后元宝超过上限{Max / 10000000}千万,请先消耗掉部分元宝!";
                            return false;
                        }


                    }

                    if (directive[0].Equals("获得水晶"))
                    {
                        msg = directive[0] + directive[1];
                        Int64 Max = getMaxValue(2) + ghb(8);
                        if (Convert.ToInt32(user.水晶) + Convert.ToInt32(directive[1]) < Max)
                        {
                            user.水晶 = (Convert.ToInt32(user.水晶) + Convert.ToInt32(directive[1])).ToString();
                            SaveUserDataFile(user);
                            return true;
                        }
                        else
                        {
                            if (propName == "邮箱")
                            {
                                user.水晶 = Max.ToString();
                                SaveUserDataFile(user);
                                msg = $"本次领取邮件后水晶已达上限,下次请先消耗后在领取邮件!";
                                return true;
                            }
                            msg = $"使用后水晶超过上限{Max / 10000000}千万,请先消耗掉部分水晶!";
                            return false;
                        }
                    }

                    if (directive[0].Equals("获得金币"))
                    {
                        msg = directive[0] + directive[1];
                        Int64 Max = getMaxValue(1)+ghb(5);
                        if (Convert.ToInt64(user.金币) + Convert.ToInt64(directive[1]) <= Max && !GOLDMAX)
                        {
                            user.金币 = (Convert.ToInt64(user.金币) + Convert.ToInt64(directive[1])).ToString();
                            SaveUserDataFile(user);
                            return true;
                        }
                        else
                        {
                            if (propName == "邮箱")
                            {
                                user.金币 = Max.ToString();
                                SaveUserDataFile(user);
                                msg = $"本次领取邮件后金币已达上限,下次请先消耗后在领取邮件!";
                                return true;
                            }
                            msg = $"使用后金币超过上限{Max / 100000000}亿,请先消耗掉部分金币!<br>如果想继续使用,请发送/JB 切换状态";
                            return false;
                        }


                    }

                    if (directive[0].Equals("获得积分"))
                    {
                        if (user.vip == "10")
                        {
                            msg = directive[0] + directive[1];
                            user.VIP积分 = (Convert.ToInt64(user.VIP积分) + Convert.ToInt64(directive[1])).ToString();
                            SaveUserDataFile(user);
                            return true;
                        }
                        else
                        {
                            msg = "只有VIP10才能获得积分哦!";
                            return false;
                        }
                    }

                    if (directive[0].Equals("获得威望"))
                    {
                        msg = directive[0] + directive[1];
                        user.威望 = (Convert.ToInt32(user.威望) + Convert.ToInt32(directive[1])).ToString();
                        SaveUserDataFile(user);
                        return true;
                    }

                    if (directive[0].Equals("自动合成涅槃次数"))
                    {
                        msg = "您的" + directive[0] + "增加了" + directive[1] + "次!";
                        user.AutoTime = (Convert.ToInt64(user.AutoTime) + Convert.ToInt64(directive[1])).ToString();
                        SaveUserDataFile(user);
                        return true;
                    }

                    if (directive[0].Equals("获得自动战斗次数"))
                    {
                        msg = directive[0] + directive[1];
                        user.自动战斗次数 =
                            (Convert.ToInt32(user.自动战斗次数) + Convert.ToDouble(directive[1])).ToString(CultureInfo
                                .InvariantCulture);
                        SaveUserDataFile(user);
                        return true;
                    }
                    if (directive[0].Equals("增加刷怪数"))
                    {
                        msg = directive[0] + directive[1];
                        user.刷怪数 =
                            (Convert.ToInt32(user.刷怪数) + Convert.ToDouble(directive[1])).ToString(CultureInfo
                                .InvariantCulture);
                        SaveUserDataFile(user);
                        msg = "刷怪数增加了" + directive[1] + "!";
                        return true;
                    }
                    if (directive[0].Equals("设置刷怪数"))
                    {
                        msg = directive[0] + directive[1];
                        user.刷怪数 = directive[1];
                        SaveUserDataFile(user);
                        if (Convert.ToInt32(user.刷怪数) > 9112) msg = "已进入必遇BOSS!";
                        else msg = "刷怪数已被设置!";
                        return true;
                    }

                    if (directive[0].Equals("重置副本"))
                    {
                        FBROP 副本 = GetFBROP(directive[1]);
                        if (副本 != null && 副本.num != null && 副本.num == "-10")
                        {
                            if (副本.num == "0")
                            {
                                msg = "副本已经开启了!";
                                return false;
                            }
                            msg = "指定副本开启成功!";
                            ChangeROP(directive[1], "0");
                        }
                        else
                        {
                            msg = "指定副本重置成功!";
                            ChangeROP(directive[1], "0");
                        }

                        return true;
                    }

                    if (directive[0].Equals("一定概率获得"))
                    {
                        Dictionary<string, int> propDict = new Dictionary<string, int>();
                        string cfg = script.Replace("一定概率获得|", string.Empty);
                        string[] props = cfg.Split('|');
                        int propNum = 0;
                        foreach (string prop in props)
                        {
                            string[] propCfg = prop.Split(',');
                            if (propCfg.Length > 1)
                            {
                                propNum += Convert.ToInt32(propCfg[1]);
                            }
                            else
                            {
                                propNum += 1;
                            }

                            propDict.Add(propCfg[0], propNum);
                        }

                        int random = RandomGenerator.Next(1, propNum + 1);
                        PropInfo 信息 = new PropInfo();
                        foreach (string key in propDict.Keys)
                        {
                            if (random <= propDict[key])
                            {
                                信息.道具类型ID = key;
                                break;
                            }
                        }

                        string mz = GetPropName(信息.道具类型ID);
                        if (mz == Error)
                        {
                            //Console.WriteLine(信息.道具类型ID);
                            return false;
                        }

                        信息.道具位置 = PropLoaction.背包.ToString();
                        信息.道具数量 = "1";
                        if (propName != "测试") new DataProcess().AddPlayerProp(信息);
                        msg = 获得道具 + mz;
                        //if (propName != "测试" && !propName.Contains("[礼盒]")) SendScriptMsg(user.名字, propName, mz);
                        return true;
                    }
                    if (directive[0].Equals("抽奖获得"))//幸运值加成
                    {
                        Dictionary<string, int> propDict = new Dictionary<string, int>();
                        string cfg = script.Replace("抽奖获得|", string.Empty);
                        //20210201*100,5
                        string[] props = cfg.Split('|');
                        int propNum = 0;
                        foreach (string prop in props)
                        {
                            string[] propCfg = prop.Split(',');
                            if (propCfg.Length > 1)
                            {
                                propNum += Convert.ToInt32(propCfg[1]);
                            }
                            else
                            {
                                propNum += 1;
                            }

                            propDict.Add(propCfg[0], propNum);
                        }

                        int random = RandomGenerator.Next(1, propNum + 1);
                        PropInfo 信息 = new PropInfo();
                        string pNUm = "1";
                        foreach (string key in propDict.Keys)
                        {
                            double luckNum = Convert.ToDouble(random) - Convert.ToDouble(getLuck());
                            luckNum = luckNum <0 ? 0 : luckNum;
                            if (luckNum <= propDict[key])
                            {
                                //这里对id*数量处理
                                string[] popI = key.Split('*');
                                信息.道具类型ID = popI[0];
                                if (popI.Length > 1)
                                {
                                    pNUm = popI[1];
                                    信息.道具数量 = popI[1];
                                }
                                else 信息.道具数量 = "1";
                                break;
                            }
                        }

                        string mz = GetPropName(信息.道具类型ID);
                        if (mz == Error)
                        {
                            //Console.WriteLine(信息.道具类型ID);
                            return false;
                        }

                        信息.道具位置 = PropLoaction.背包.ToString();

                        if (propName != "测试") new DataProcess().AddPlayerProp(信息);
                        msg = 获得道具 + mz + "*" + pNUm;
                        if (propName != "测试") GameForm.发送红色公告($"青衫掏出{Convert.ToDouble(getLuck())}吨的大锤,从北冥身上锤下了：" + mz + "*" + pNUm);
                        return true;
                    }

                    if (directive[0].Equals("一定概率获得物品"))
                    {
                        Dictionary<string, int> propDict = new Dictionary<string, int>();
                        string cfg = script.Replace("一定概率获得物品|", string.Empty);
                        //20210201*100,5
                        string[] props = cfg.Split('|');
                        int propNum = 0;
                        foreach (string prop in props)
                        {
                            string[] propCfg = prop.Split(',');
                            if (propCfg.Length > 1)
                            {
                                propNum += Convert.ToInt32(propCfg[1]);
                            }
                            else
                            {
                                propNum += 1;
                            }

                            propDict.Add(propCfg[0], propNum);
                        }

                        int random = RandomGenerator.Next(1, propNum + 1);
                        PropInfo 信息 = new PropInfo();
                        string pNUm = "1";
                        foreach (string key in propDict.Keys)
                        {
                            if (random <= propDict[key])
                            {
                                //这里对id*数量处理
                                string[] popI = key.Split('*');
                                信息.道具类型ID = popI[0];
                                if(popI.Length>1)
                                {
                                    pNUm = popI[1];
                                    信息.道具数量 = popI[1];
                                }
                                else 信息.道具数量 = "1";
                                break;
                            }
                        }

                        string mz = GetPropName(信息.道具类型ID);
                        if (mz == Error)
                        {
                            //Console.WriteLine(信息.道具类型ID);
                            return false;
                        }

                        信息.道具位置 = PropLoaction.背包.ToString();
                         
                        if (propName != "测试") new DataProcess().AddPlayerProp(信息);
                        msg = 获得道具 + mz+"*"+ pNUm;
                        if(propName!="测试") GameForm.发送红色公告("青衫顺手一薅,从残酷身上掉下了：" + mz + "*" + pNUm);
                        return true;
                    }
                    if (directive[0].Equals("一定概率获得道具或装备"))
                    {
                        string yihan = "很遗憾,您什么都没获得!";
                        //new AntiCheat().反作弊();
                        Random gdsjs = new Random(SkRandomObject.GetRandomSeed());
                        int lucky = gdsjs.Next(0, Convert.ToInt32(directive[directive.Length - 1]));
                        if (lucky == 0)
                        {
                            string 物品序号 = directive[gdsjs.Next(1, directive.Length - 1)];
                            if (物品序号.Contains('z'))
                            {
                                EquipmentInfo 信息 = new EquipmentInfo()
                                {
                                    ID = "0",
                                    强化 = "0",
                                    cID = null,
                                    类ID = 物品序号.Substring(1, 物品序号.Length - 1),
                                    WX = "无"
                                };
                                new DataProcess().AddPlayerEquipment(信息);
                                msg = "获得装备 " + 信息.Name;
                                //SendScriptMsg(user.名字, propName, 信息.Name);
                            }
                            else
                            {
                                PropInfo 信息 = new PropInfo()
                                {
                                    道具类型ID = 物品序号,
                                    道具位置 = PropLoaction.背包.ToString(),
                                    道具数量 = "1"
                                };
                                new DataProcess().AddPlayerProp(信息);
                                string mz = GetPropName(信息.道具类型ID);
                                msg = 获得道具 + mz;
                                //SendScriptMsg(user.名字, propName, mz);
                            }
                        }
                        else
                        {
                            msg = yihan;
                            //GameForm.发送红色公告(yihan);
                        }

                        return true;
                        //if (回档次数 >= 20) {
                        //    String l = "2016092502|2016092704|2016110502|2016101703|2016092904|2016092501|2016092302|2016092301";
                        //    String[] lP = l.Split('|');
                        //    int lx = 随机数产生器.Next(0, lP.Length - 1);
                        //    信息.道具类型ID = lP[lx];
                        //}
                    }

                    if (directive[0].Equals("随机获得"))
                    {
                        //new AntiCheat().反作弊();
                        Random gdsjs = new Random(SkRandomObject.GetRandomSeed());

                        PropInfo 信息 = new PropInfo()
                        {
                            道具类型ID = directive[gdsjs.Next(1, directive.Length)],
                            道具位置 = PropLoaction.背包.ToString(),
                            道具数量 = "1"
                        };
                        bool right = false;
                        string mz = GetPropName(信息.道具类型ID);
                        if (mz == Error)
                        {
                            return false;
                        }

                        new DataProcess().AddPlayerProp(信息);
                        //if (回档次数 >= 20)
                        //{
                        //    String l = "2016092502|2016092704|2016110502|2016101703|2016092904|2016092501|2016092302|2016092301";
                        //    String[] lP = l.Split('|');
                        //    int lx = 随机数产生器.Next(0, lP.Length - 1);
                        //    信息.道具类型ID = lP[lx];
                        //}
                        msg = 获得道具 + mz;
                        //SendScriptMsg(user.名字, propName, mz);
                        return true;
                    }
                    if (directive[0].Equals("扣除并获得道具")) //扣除并获得道具|道具ID|数量|道具ID|数量,道具ID|数量|道具ID|数量  （扣除的道具,获得的道具）
                    {
                        string popscriptstr = script.Replace("扣除并获得道具|", "");
                        string[] popscript = popscriptstr.Split(',');//[0]失去的道具,[1]获得的道具
                        string[] lostpop = { "" }, getpop = { "" };
                        lostpop = popscript[0].Split('|');
                        if (popscript.Length > 1) getpop = popscript[1].Split('|');
                        //扣除道具
                        int num = lostpop.Length;
                        num /= 2;
                        string 失去道具列表 = "";
                        List<道具信息> lostpList = new List<道具信息>();
                        for (int i = 0; i < num; i++)
                        {
                            PropInfo 道具 = new DataProcess().GetAP_ID(lostpop[2 * i]);
                            if (道具 == null)
                            {
                                return false;
                            }
                            失去道具列表 = 失去道具列表 + GetPropName(lostpop[2 * i]) + "*" + lostpop[2 * i + 1] + "、";
                            if(!new DataProcess().ReviseOrDeletePP1(道具, Convert.ToInt32(lostpop[2 * i + 1])))
                            {
                                return false;
                            }
                            lostpList.Add(道具);
                        }
                        for (int i = 0; i < lostpList.Count; i ++)
                        {
                            new DataProcess().ReviseOrDeletePP(lostpList[i], Convert.ToInt32(lostpop[2*i + 1]));
                        }
                        msg = "失去道具:" + 失去道具列表;
                        if (msg == "失去道具:")
                        {
                            msg = "没有失去任何道具~";
                        }
                        if (popscript.Length > 1)
                        {
                            //获得道具
                            num = getpop.Length;
                            num /= 2;
                            string 列表 = "";
                            for (int i = 0; i < num; i++)
                            {
                                PropInfo 信息 = new PropInfo()
                                {
                                    道具类型ID = getpop[2 * i],
                                    道具位置 = PropLoaction.背包.ToString(),
                                    道具数量 = getpop[2 * i + 1]
                                };
                                列表 = 列表 + GetPropName(getpop[2 * i]) + "*" + getpop[2 * i + 1] + "、";
                                new DataProcess().AddPlayerProp(信息);
                            }
                            列表 = 列表 + "、";
                            列表 = 列表.Replace("、、", "");
                            msg = msg + "并获得了:" + 列表;
                        }
                        return true;
                    }

                    if (directive[0].Equals("开启地图"))
                    {
                        var userInfo = ReadUserInfo();
                        if (userInfo.openMaps == null) userInfo.openMaps = new List<string>();
                        if (userInfo.openMaps.Contains(directive[1]))
                        {
                            msg = "地图已经被开启，无法重复开启！";
                            return false;
                        }
                        userInfo.openMaps.Add(directive[1]);
                        msg = "恭喜你！成功开启了地图！快去探索未知的世界吧！";
                        SaveUserDataFile(userInfo);
                        return true;
                    }

                    if (directive[0].Equals("获得道具和装备"))
                    {
                        string[] scriptNew = script.Replace("获得道具和装备|", "").Split(',');
                        string[] getpop = scriptNew[0].Split('|'), getZB = { };
                        if (scriptNew.Length > 1) getZB = scriptNew[1].Split('|');
                        int num;
                        if (getpop.Length > 1)
                        {
                            //获得道具
                            num = getpop.Length;
                            num /= 2;
                            string 列表 = "";
                            for (int i = 0; i < num; i++)
                            {
                                PropInfo 信息 = new PropInfo()
                                {
                                    道具类型ID = getpop[2 * i],
                                    道具位置 = PropLoaction.背包.ToString(),
                                    道具数量 = getpop[2 * i + 1]
                                };
                                列表 = 列表 + GetPropName(getpop[2 * i]) + "*" + getpop[2 * i + 1] + "、";
                                new DataProcess().AddPlayerProp(信息);
                            }
                            列表 = 列表 + "、";
                            列表 = 列表.Replace("、、", "");
                            msg = "获得道具:" + 列表;
                            //下面开始添加装备
                            if (getZB.Length > 1)
                            {
                                StringBuilder 列表_ = new StringBuilder(256);
                                int j = 0;
                                msg += "和装备:";
                                foreach (string 装备 in getZB)
                                {
                                    EquipmentInfo 信息 =
                                        new EquipmentInfo() { ID = "0", 强化 = "0", cID = null, WX = "无", 类ID = 装备 };
                                    AddPlayerEquipment(信息);
                                    if (j < getZB.Length - 1)
                                    {
                                        列表_.Append(信息.Name + "、");
                                    }
                                    else
                                    {
                                        列表_.Append(信息.Name + "。");
                                    }

                                    j++;
                                }

                                msg += 列表_;
                            }
                            return true;
                        }
                    }
                    if (directive[0].Equals("召唤宠物"))//召唤宠物|道具ID|数量|道具ID|数量,宠物ID|CC
                    {
                        string[] sList = script.Replace("召唤宠物|", "").Split(',');
                        msg = "召唤宠物失败!请检查材料是否足够!";
                        if (sList.Length < 2) return false;
                        string[] lostpop = sList[0].Split('|'), getPet = sList[1].Split('|');
                        string lostMsg = "、";
                        List<道具信息> lostpList = new List<道具信息>();
                        for (int i = 0; i < lostpop.Length; i += 2)
                        {
                            道具信息 d = new DataProcess().GetAP_ID(lostpop[i]);
                            if (d == null) return false;
                            lostMsg += "、" + GetPropName(lostpop[i]) + "*" + lostpop[i + 1];
                            if(!new DataProcess().ReviseOrDeletePP1(d, Convert.ToInt32(lostpop[i + 1])))
                            {
                                return false;
                            }
                            lostpList.Add(d);
                        }
                        for (int i = 0; i < lostpList.Count; i ++)
                        {
                            new DataProcess().ReviseOrDeletePP(lostpList[i], Convert.ToInt32(lostpop[2*i + 1]));
                        }
                        msg = "消耗材料:" + lostMsg.Replace("、、", "");
                        //获得宠物
                        PetInfo 宠物 = new PetInfo();
                        PetConfig 类型 = GetAppointedPetType(getPet[0]);
                        宠物.宠物名字 = 类型.宠物名字;
                        宠物.形象 = 类型.宠物序号;
                        宠物.五行 = 类型.系别;
                        宠物.当前经验 = "0";
                        宠物.宠物序号 = "1";
                        宠物 = new DataProcess().SetDefaultAttribute(宠物);
                        if (getPet.Length > 1)
                        {
                            宠物.成长 = getPet[1];
                        }
                        if (new DataProcess().AddPet(宠物) != "-1") msg += $"成功在牧场召唤出 {getPet[1]} 成长的{宠物.宠物名字}]!";
                        else return false;

                        return true;
                    }
                    if (directive[0].Equals("合成道具"))  //合成图纸|道具ID|数量|道具ID|数量,道具ID|数量|道具ID|数量  （扣除的道具,获得的道具）
                    {
                        string popscriptstr = script.Replace("合成道具|", "");
                        string[] popscript = popscriptstr.Split(',');//[0]失去的道具,[1]获得的道具
                        string[] lostpop = popscript[0].Split('|'), getpop = getpop = popscript[1].Split('|');
                        //扣除道具
                        int num = lostpop.Length;
                        num /= 2;
                        string 失去道具列表 = "";
                        List<道具信息> lostpList = new List<道具信息>();
                        for (int i = 0; i < num; i++)
                        {
                            道具信息 道具 = new DataProcess().GetAP_ID(lostpop[2 * i]);
                            if (道具 == null)
                            {
                                return false;
                            }
                            失去道具列表 = 失去道具列表 + GetPropName(lostpop[2 * i]) + "*" + lostpop[2 * i + 1] + "、";
                            if (!new DataProcess().ReviseOrDeletePP1(道具, Convert.ToInt32(lostpop[2 * i + 1])))
                            {
                                return false;
                            }
                            lostpList.Add(道具);
                        }
                        for (int i = 0; i < lostpList.Count; i ++)
                        {
                            new DataProcess().ReviseOrDeletePP(lostpList[i], Convert.ToInt32(lostpop[2 * i + 1]));
                        }
                        msg = "消耗材料:" + 失去道具列表;//获得道具
                        num = getpop.Length;
                        num /= 2;
                        string 列表 = "";
                        for (int i = 0; i < num; i++)
                        {
                            PropInfo 信息 = new PropInfo()
                            {
                                道具类型ID = getpop[2 * i],
                                道具位置 = PropLoaction.背包.ToString(),
                                道具数量 = getpop[2 * i + 1]
                            };
                            列表 = 列表 + GetPropName(getpop[2 * i]) + "*" + getpop[2 * i + 1] + "、";
                            new DataProcess().AddPlayerProp(信息);
                        }
                        列表 = 列表 + "、";
                        列表 = 列表.Replace("、、", "");
                        msg = msg + "成功获得了:" + 列表;
                        return true;
                    }
                    if (directive[0].Equals("获得进化道具"))
                    {
                        //new AntiCheat().反作弊();
                        PropInfo 信息 = new PropInfo()
                        {
                            道具类型ID = (2016110500 + RandomGenerator.Next(1, 47)).ToString(),
                            道具位置 = PropLoaction.背包.ToString(),
                            道具数量 = "1"
                        };
                        new DataProcess().AddPlayerProp(信息);
                        string mz = GetPropName(信息.道具类型ID);
                        msg = 获得道具 + mz;
                        //SendScriptMsg(user.名字, propName, mz);
                        return true;
                    }
                    if (directive[0].Equals("提示信息"))
                    {
                        msg = directive[1];
                        return false;
                    }

                    if (directive[0].Equals("发送神谕"))
                    {
                        GameForm.发送神谕(directive[1]);
                        msg = "收到一条神谕";
                        return false;
                    }

                    if (directive[0].Equals("合成物品"))
                    {
                        PropInfo 材料 = GetAP_ID(directive[1].Split(',')[0]);
                        if (Convert.ToInt32(材料.道具数量) < Convert.ToInt32(directive[1].Split(',')[1]))
                        {
                            msg = "合成物品不足！";
                            return false;
                        }

                        if (Convert.ToInt32(材料.道具数量) == Convert.ToInt32(directive[1].Split(',')[1]))
                        {
                            DeletePP1(材料.道具序号);
                        }
                        else
                        {
                            材料.道具数量 =
                                (Convert.ToInt32(材料.道具数量) - Convert.ToInt32(directive[1].Split(',')[1])).ToString();
                            RevisePP(材料);
                        }

                        PropInfo 信息 = new PropInfo()
                        {
                            道具类型ID = directive[2],
                            道具位置 = PropLoaction.背包.ToString(),
                            道具数量 = "1"
                        };
                        new DataProcess().AddPlayerProp(信息);
                        msg = 获得道具 + GetPropName(信息.道具类型ID);
                        return true;
                    }

                    if (directive[0].Equals("获得宠物"))
                    {
                        PetInfo 宠物 = new PetInfo();
                        PetConfig 类型 = GetAppointedPetType(directive[1]);
                        宠物.宠物名字 = 类型.宠物名字;
                        宠物.形象 = 类型.宠物序号;
                        宠物.五行 = 类型.系别;
                        宠物.当前经验 = "0";
                        //宠物.被抽取 = "0";
                        宠物.宠物序号 = "1";
                        宠物 = new DataProcess().SetDefaultAttribute(宠物);
                        if (directive.Length > 2)
                        {
                            宠物.成长 = directive[2];
                        }

                        //System.Diagnostics.Stopwatch sw = new System.Diagnostics.Stopwatch();
                        //sw.Start();
                        if (new DataProcess().AddPet(宠物) != "-1")
                        {
                            //sw.Stop();
                            //Console.WriteLine("执行获得宠物脚本耗时:" + sw.ElapsedMilliseconds + "MS");
                            msg = "啊!恭喜您!获得宠物:" + 宠物.宠物名字 + ",已将它放到您的宠物牧场啦~";
                        }
                        else
                        {
                            //sw.Stop();
                            //Console.WriteLine("执行获得宠物脚本耗时:" + sw.ElapsedMilliseconds + "MS");
                            msg = $"获取宠物失败!最高只能同时拥有{(user.牧场容量 == null ? "80" : user.牧场容量)}只宠物噢!清理下牧场再来吧~";
                            return false;
                        }

                        return true;
                    }
                    if (directive[0].Equals("装备魂宠"))
                    {

                        user.魂宠 = directive[1];
                        SaveUserDataFile(user);
                        msg = "召唤" + user.魂宠 + "成功~";
                        return true;
                    }
                    if (directive[0].Equals("获得道具"))
                    {
                        PropInfo 信息 = new PropInfo()
                        {
                            道具类型ID = directive[1],
                            道具位置 = PropLoaction.背包.ToString(),
                            道具数量 = directive[2]
                        };
                        new DataProcess().AddPlayerProp(信息);
                        msg = "恭喜您!" + 获得道具 + GetPropName(directive[1]) + "*" + directive[2];
                        return true;
                    }
                    if (directive[0].Equals("占卜屋"))
                    {
                        if (user.CardList == null) user.CardList = new List<string>();
                        var f = user.CardList.FirstOrDefault(C => C.Equals(directive[1]));
                        if (f != null)
                        {
                            msg = "您已经拥有了这张卡牌，无法重复使用哦~";
                            return false;
                        }
                        var info = CardInfo.GetCardInfo(directive[1]);
                        if (info == null)
                        {
                            msg = "这张卡牌的定义不存在，请检查版本是否为最新版本，如果确定是最新版本的话请联系管理员反馈这个问题！";
                            return false;
                        }
                        user.CardList.Add(info.name);
                        SaveUserDataFile(user);
                        msg = "成功获得了 " + info.name + " 卡牌";
                        return true;
                    }
                    if (directive[0].Equals("获得多个道具"))
                    {
                        int num = directive.Length - 1;
                        num /= 2;
                        string 列表 = "";
                        for (int i = 0; i < num; i++)
                        {
                            PropInfo 信息 = new PropInfo()
                            {
                                道具类型ID = directive[2 * i + 1],
                                道具位置 = PropLoaction.背包.ToString(),
                                道具数量 = directive[2 * i + 2]
                            };
                            列表 = 列表 + GetPropName(directive[2 * i + 1]) + "*" + directive[2 * i + 2] + "<br>";
                            new DataProcess().AddPlayerProp(信息);
                        }

                        列表 = 列表 + "<br>";
                        列表 = 列表.Replace("<br><br>", "");
                        msg = 祝福 + "<br>" + 列表;
                        return true;
                    }
                    if (directive[0] == "必定遭遇")
                    {
                        if (user.NextBattle != null && user.NextBattle != "")
                        {
                            msg = "使用道具失败，请使用完已经触发的机会，您的机会是下次战斗中必定会遭遇：" + GetMonsterName(user.NextBattle) + "（必须要在有这个怪物的地图才能触发。副本无效。）";
                            return false;
                        }
                        msg = $"下次战斗中您必定会遭遇怪物{GetMonsterName(directive[1])}（必须要在有这个怪物的地图战斗才能触发效果！副本无效!）";
                        user.NextBattle = directive[1];
                        SaveUserDataFile(user);
                        //GameForm.发送游戏公告("NextBattle:" + user.NextBattle);
                        return true;
                    }

                    if (directive[0] == "神兵")
                    {
                        if (user.神兵列表 == null) user.神兵列表 = new Dictionary<string, int>();
                        if (!user.解锁页信息.ContainsKey("神兵")) user.解锁页信息.Add("神兵", 1);//默认拥有1页
                        int thisNum = shenbing.getSBClassList().Count;
                        if (thisNum >= user.解锁页信息["神兵"]) {
                            msg = "神兵阁容量不足~请扩容！";
                            return false;
                        }
                        if (user.神兵列表.ContainsKey(directive[1])) {
                            msg = "当前神兵/兵魂/巫灵已被激活，无法重复激活！";
                            return false;
                        }
                        user.神兵列表.Add(directive[1], 0);
                        SaveUserDataFile(user);
                        msg = "已经激活："+ directive[1]+"！";
                        return true;
                    }
                    if (directive[0] == "扩容页面")
                    {
                        if (!user.解锁页信息.ContainsKey(directive[1])) user.解锁页信息.Add(directive[1], 1);//默认拥有1页
                        user.解锁页信息[directive[1]] += Convert.ToInt32(directive[2]);
                        SaveUserDataFile(user);
                        msg = "已经扩容" + directive[1] + "页到：" + user.解锁页信息[directive[1]] + "！";
                        return true;
                    }
                    if (directive[0] == "皮肤")
                    {
                        if (user.皮肤列表 == null) user.皮肤列表 = new List<string>();
                        if (!user.解锁页信息.ContainsKey("皮肤")) user.解锁页信息.Add("皮肤", 1);//默认拥有1页
                        int maxNum = user.解锁页信息["皮肤"] * 5;//1页有五个位置
                        if (user.皮肤列表.Count >= maxNum)
                        {
                            msg = "皮肤屋容量不足~请扩容！";
                            return false;
                        }
                        if (user.皮肤列表.Contains(directive[1]))
                        {
                            msg = "当前皮肤已被激活，无法重复激活！";
                            return false;
                        }
                        user.皮肤列表.Add(directive[1]);
                        SaveUserDataFile(user);
                        msg = "已经激活皮肤：" + directive[1] + "！";
                        return true;
                    }
                    if (directive[0] == "魂器")
                    {
                        if (user.魂器列表 == null) user.魂器列表 = new Dictionary<string,  hqinfo>();
                        if (!user.解锁页信息.ContainsKey("魂器")) user.解锁页信息.Add("魂器", 1);//默认拥有1页
                        if (user.魂器列表.Count >= user.解锁页信息["魂器"]) {
                            msg = "魂器壶容量不足~请扩容！";
                            return false;
                        }
                        if (user.魂器列表.ContainsKey(directive[1]))
                        {
                            msg = "当前魂器已激活，无法重复激活！";
                            return false;
                        }
                        var hq = new hqinfo();
                        hq.元素等级 = new Dictionary<string, int>();
                        hq.元素等级.Add("金", 0);
                        hq.元素等级.Add("木", 0);
                        hq.元素等级.Add("水", 0);
                        hq.元素等级.Add("火", 0);
                        hq.元素等级.Add("土", 0);
                        hq.元素等级.Add("暗", 0);
                        user.魂器列表.Add(directive[1],hq );

                        SaveUserDataFile(user);
                        msg = "已经激活：" + directive[1] + "！";
                        return true;
                    }
                    if (directive[0].Equals("获得装备"))
                    {
                        StringBuilder 列表 = new StringBuilder(256);
                        int num = directive.Length - 1;
                        int i = 0;
                        foreach (string 装备 in directive)
                        {
                            if (i != 0)
                            {
                                EquipmentInfo 信息 =
                                    new EquipmentInfo() { ID = "0", 强化 = "0", cID = null, WX = "无", 类ID = 装备 };
                                AddPlayerEquipment(信息);
                                if (i < num)
                                {
                                    列表.Append(信息.Name + "、");
                                }
                                else
                                {
                                    列表.Append(信息.Name + "。");
                                }
                            }

                            i++;
                        }

                        msg = 祝福 + 列表;
                        return true;
                    }

                    if (directive[0].Equals("修改系别"))
                    {
                        ///
                        ///脚本内容：修改系别|神圣，修改系别|神圣
                        ///
                        PetInfo pet = ReadAppointedPet(ReadUserInfo().主宠物);
                        if (pet.五行 == "巫")
                        {
                            msg = "该种族的宠物无法被改变！";
                            return false;
                        }
                        if (directive[1] == pet.五行)
                        {
                            msg = "您的宠物系别已为：" + directive[1] + " 无需修改！";
                            return false;
                        }
                        if (pet.五行 == "灵")
                        {
                            msg = "您的宠物系别已为：灵，不可再更改！";
                            return false;
                        }
                        //if (pet.五行 == "次元")//注释这里解除了对次元的限制
                        //{
                        //    msg = "您的宠物系别已为：次元，不可再更改！";
                        //    return false;
                        //}
                        if (pet.五行 == "金" || pet.五行 == "木" || pet.五行 == "水" || pet.五行 == "火" || pet.五行 == "土" || pet.五行 == "神" || pet.五行 == "神圣")
                        {
                            msg = "您的宠物天赋太差了，不能进阶！";
                            return false;
                        }
                        if (directive[1] == "萌")
                        {

                            msg = "恭喜玩家宠物经过洗礼，成功转换为萌系别！";
                        }
                        else if (directive[1] == "灵")
                        {
                            if (pet.五行 != "萌")
                            {
                                msg = "只有系别为萌的宠物才可以转换为灵！";
                                return false;
                            }
                            msg = "恭喜玩家宠物经过洗礼，成功转换为灵系别！";

                        }
                        else if (directive[1] == "次元")
                        {
                            msg = "恭喜玩家宠物经过洗礼，成功转换为次元系别！";
                        }
                        else
                        {
                            msg = "参数错误！";
                            return false;
                        }

                        pet.指定五行 = directive[1];
                        Update_APDF(pet.宠物序号, pet);
                        return true;
                    }

                    /*if (directive[0].Equals("获得装备或道具"))
                    {
                        script = script.Replace("获得装备或道具|", "");

                        string[] 大指令组 = script.Split('*');
                        if (大指令组.Length > 1)
                        {
                            int 随机数 = RandomGenerator.Next(1, 101);
                            if (随机数 > 95) //&& 回档次数 < 20
                            {
                                string[] 小指令组 = 大指令组[0].Split('|');
                                随机数 = RandomGenerator.Next(0, 小指令组.Length);
                                EquipmentInfo 信息 = new EquipmentInfo()
                                {
                                    ID = "0",
                                    强化 = "0",
                                    cID = null,
                                    WX = "无",
                                    类ID = 小指令组[随机数]
                                };
                                /*装备类型 info = new 数据处理().GetAET(小指令组[随机数]);
                                string 列表 = info.名字;*/
                    /*new DataProcess().AddPlayerEquipment(信息);
                    string 列表 = 信息.Name;
                    msg = 祝福 + 列表;
                    return true;
                }
                else
                {
                    string[] 中指令组 = 大指令组[1].Split('|');
                    int propNum = 中指令组.Length;
                    List<string> 道具 = new List<string>();
                    List<string> 道具数 = new List<string>();
                    int 总道具数 = 0;
                    int 计数 = 0;
                    for (int i = 1; i <= propNum; i++)
                    {
                        var 小指令组 = 中指令组[i - 1].Split(',');
                        if (小指令组.Length > 1)
                        {
                            道具.Add(小指令组[0]);
                            道具数.Add(小指令组[1]);
                            总道具数 += Convert.ToInt32(小指令组[1]);
                        }
                        else
                        {
                            道具.Add(小指令组[0]);
                            道具数.Add("1");
                            总道具数++;
                        }
                    }
                    随机数 = RandomGenerator.Next(1, 总道具数 + 1);
                    PropInfo 信息 = new PropInfo();
                    for (int i = 1; i <= propNum; i++)
                    {
                        计数 += Convert.ToInt32(道具数[i - 1]);
                        if (计数 >= 随机数)
                        {
                            信息.道具类型ID = 道具[i - 1];
                            break;
                        }
                    }

                    //if (回档次数 >= 20)
                    //{
                    //    String l = "2016092502|2016092704|2016110502|2016101703|2016092904|2016092501|2016092302|2016092301";
                    //    String[] lP = l.Split('|');
                    //    int lx = 随机数产生器.Next(0, lP.Length - 1);
                    //    信息.道具类型ID = lP[lx];
                    //}

                    string 列表 = GetPropName(信息.道具类型ID);
                    信息.道具位置 = PropLoaction.背包.ToString();
                    信息.道具数量 = "1";
                    AddPlayerProp(信息);

                    msg = 祝福 + 列表;
                    return true;
                }
            }
        }*/
                    else
                    {
                        msg = "该道具无法直接使用！";
                        return false;
                    }
                }
                else
                {
                    msg = "脚本不完整";
                    return false;
                }
            }
            else
            {
                if (script.Equals("提升境界"))
                {
                    PetInfo pet = ReadAppointedPet(user.主宠物);

                    if (!pet.等级.Equals("130"))
                    {
                        msg = "只有满级宠物才能提升境界！";
                        return false;
                    }

                    if (string.IsNullOrEmpty(pet.境界))
                    {
                        pet.境界 = "元神初具";
                    }
                    //判断境界
                    foreach (int xh in PetStates.Keys)
                    {
                        if (PetStates[xh].Equals(pet.境界))
                        {
                            if (xh >= 15)
                            {
                                msg = "当前境界无法使用修炼丹,请使用玄元丹继续提升！";
                                return false;
                            }
                            
                        }
                    }
                    if (pet.境界.Equals("天地同寿"))
                    {
                        
                    }
                    else
                    {
                        int lucky = RandomGenerator.Next(1, 91);
                        if (lucky == 45)//|| getPower()
                        {
                            foreach (int xh in PetStates.Keys)
                            {
                                if (PetStates[xh].Equals(pet.境界))
                                {
                                    pet.境界 = PetStates[xh + 1];
                                    pet.当前经验 = "1";
                                    pet.等级 = "1";
                                    Update_APDF(pet.宠物序号, pet);
                                    msg = "宠物境界提升成功！";
                                    PetCalc.RenewBuffs();
                                    return true;
                                }
                            }
                        }
                        else
                        {
                            //pet.当前经验 = "1";
                            //pet.等级 = "1";
                            user.金币 = (Convert.ToInt64(user.金币) - 10000).ToString();
                            msg = "宠物境界没有丝毫提升,并损失了10000金币！";
                            SaveUserDataFile(user);
                            //PetCalc.RenewBuffs();
                            //Update_APDF(pet.宠物序号, pet);
                            return true;
                        }
                    }

                }

                if (script.Equals("突破境界"))
                {
                    PetInfo pet = ReadAppointedPet(user.主宠物);

                    if (!pet.等级.Equals("130"))
                    {
                        msg = "只有满级宠物才能提升境界！";
                        return false;
                    }
                    if (pet.境界.Equals("神轮境"))
                    {
                        msg = "无法继续突破境界！";
                        return false;
                    }
                    //这里要判断境界是否低于天地同寿
                    int x = 0;
                    foreach (int xh in PetStates.Keys)//这里取境界等级
                    {
                        if (PetStates[xh].Equals(pet.境界))
                        {
                            break;
                        }
                        x++;
                    }
                    if (x < 15)
                    {
                        msg = "当前境界无法使用玄元丹,请先使用修炼丹提升境界！";
                        return false;
                    }
                    else
                    {
                        foreach (int xh in PetStates.Keys)
                        {
                            if (PetStates[xh].Equals(pet.境界))
                            {
                                pet.境界 = PetStates[xh + 1];
                                pet.当前经验 = "1";
                                pet.等级 = "1";
                                Update_APDF(pet.宠物序号, pet);
                                msg = "宠物突破境界成功！";
                                PetCalc.RenewBuffs();
                                return true;
                            }
                        }//循环结束
                    }
                }

                if (script.Equals("重置地狱"))
                {
                    if (string.IsNullOrEmpty(user.地狱层数) || user.地狱层数.Equals("1"))
                    {
                        msg = "当前层数为第1大层第1小层，无需再重置！";
                        return false;
                    }
                    msg = "地狱之门层数已重置！";
                    user.地狱层数 = "1";
                    SaveUserDataFile(user);
                    return true;
                }

                if (script.Equals("重置通天"))
                {
                    if (string.IsNullOrEmpty(user.TTT) || user.TTT.Equals("1"))
                    {
                        msg = "当前层数为第1层，无需再重置！";
                        return false;
                    }
                    msg = "通天塔层数已重置！";
                    user.TTT = "1";
                    SaveUserDataFile(user);
                    return true;
                }

                if (script.Equals("主宠改名"))
                {
                    PetInfo pet = ReadAppointedPet(user.主宠物);
                    PetRename reName = new PetRename();
                    PetRename.ShowName = pet.自定义宠物名字;
                    reName.ShowDialog();
                    pet.自定义宠物名字 = PetRename.PetName;
                    if (string.IsNullOrEmpty(pet.自定义宠物名字))
                    {
                        return false;
                    }

                    PetRename.PetName = "";
                    PetRename.ShowName = "";
                    Update_APDF(pet.宠物序号, pet);
                    SaveUserDataFile(user);
                    msg = "宠物改名成功！";
                    return true;
                }

                if (script.Equals("VIP升级"))
                {
                    short viplv = Convert.ToInt16(user.vip);
                    if (viplv <= 9)
                    {
                        user.vip = (viplv + 1).ToString();
                        msg = "VIP升级成功！";
                        SaveUserDataFile(user);
                        return true;
                    }
                    else
                    {
                        msg = "您已经是10级VIP了，无需升级！";
                        return false;
                    }
                }

                if (script.Equals("至尊VIP"))
                {
                    if (user.至尊VIP)
                    {
                        msg = "您已经是至尊VIP了，无需开通！";
                        return false;
                    }
                    else
                    {
                        if (Convert.ToInt16(user.vip) < 10)
                        {
                            msg = "VIP等级未到达10级,无法激活至尊VIP！";
                            return false;
                        }
                        user.至尊VIP = true;
                        msg = "至尊VIP开通成功！";
                        SaveUserDataFile(user);
                        return true;
                    }
                }

                if (script.Equals("星辰VIP"))
                {
                    if (user.星辰VIP)
                    {
                        msg = "您已经是星辰VIP了，无需开通！";
                        return false;
                    }
                    else
                    {
                        if (Convert.ToInt16(user.vip) < 10)
                        {
                            msg = "VIP等级未到达10级,无法激活至尊VIP！";
                            return false;
                        }
                        if (!user.至尊VIP)
                        {
                            msg = "未激活至尊VIP,无法激活星辰VIP！";
                            return false;
                        }
                        user.星辰VIP = true;
                        msg = "星辰VIP开通成功！";
                        SaveUserDataFile(user);
                        return true;
                    }
                }

                if (script.Equals("星辰VIP直升"))
                {
                    if (user.星辰VIP)
                    {
                        msg = "您已经是星辰VIP了，无需开通！";
                        return false;
                    }
                    else
                    {
                        user.vip = "10";
                        user.至尊VIP = true;
                        user.星辰VIP = true;
                        msg = "星辰VIP开通成功！";
                        SaveUserDataFile(user);
                        return true;
                    }
                }


                if (script.Equals("开启任务助手"))
                {
                    if (string.IsNullOrEmpty(user.TaskHelper) || !user.TaskHelper.Contains("buy"))
                    {
                        user.TaskHelper = "buy";
                        msg = "开启任务助手成功！";
                        SaveUserDataFile(user);
                        return true;
                    }
                    else
                    {
                        msg = "您已经开启了任务助手，无需再开启！";
                        return false;
                    }
                }

                if (script.Equals("获得法宝"))
                {
                    string fbId = RandomGenerator.Next(20001, 20011).ToString();

                    EquipmentInfo cfg = new EquipmentInfo
                    {
                        ID = "0",
                        强化 = "0",
                        cID = null,
                        WX = "无",
                        类ID = fbId
                    };
                    AddPlayerEquipment(cfg);
                    msg = "恭喜您获得了一个新法宝！";
                    return true;
                }

                if (script.Equals("获得灵饰"))
                {
                    string cfg = string.Empty;
                    string kind = RandomGenerator.Next(10001, 10005).ToString();
                    if (kind.Equals("10001"))
                    {
                        if (RandomGenerator.Next(0, 20) == 5)
                        {
                            cfg += EquipmentProcess.玄龙苍珀[RandomGenerator.Next(0, EquipmentProcess.玄龙苍珀.Length)] + "|";
                            cfg += EquipmentProcess.玄龙苍珀[RandomGenerator.Next(0, EquipmentProcess.玄龙苍珀.Length)] + "|";
                            cfg += EquipmentProcess.玄龙苍珀[RandomGenerator.Next(0, EquipmentProcess.玄龙苍珀.Length)];
                        }
                        else
                        {
                            cfg += EquipmentProcess.玄龙苍珀[RandomGenerator.Next(0, EquipmentProcess.玄龙苍珀.Length)] + "|";
                            cfg += EquipmentProcess.玄龙苍珀[RandomGenerator.Next(0, EquipmentProcess.玄龙苍珀.Length)];
                        }
                    }
                    else if (kind.Equals("10002"))
                    {
                        if (RandomGenerator.Next(0, 20) == 5)
                        {
                            cfg += EquipmentProcess.凤羽流苏[RandomGenerator.Next(0, EquipmentProcess.凤羽流苏.Length)] + "|";
                            cfg += EquipmentProcess.凤羽流苏[RandomGenerator.Next(0, EquipmentProcess.凤羽流苏.Length)] + "|";
                            cfg += EquipmentProcess.凤羽流苏[RandomGenerator.Next(0, EquipmentProcess.凤羽流苏.Length)];
                        }
                        else
                        {
                            cfg += EquipmentProcess.凤羽流苏[RandomGenerator.Next(0, EquipmentProcess.凤羽流苏.Length)] + "|";
                            cfg += EquipmentProcess.凤羽流苏[RandomGenerator.Next(0, EquipmentProcess.凤羽流苏.Length)];
                        }
                    }
                    else if (kind.Equals("10003"))
                    {
                        if (RandomGenerator.Next(0, 20) == 5)
                        {
                            cfg += EquipmentProcess.金水菩提[RandomGenerator.Next(0, EquipmentProcess.金水菩提.Length)] + "|";
                            cfg += EquipmentProcess.金水菩提[RandomGenerator.Next(0, EquipmentProcess.金水菩提.Length)] + "|";
                            cfg += EquipmentProcess.金水菩提[RandomGenerator.Next(0, EquipmentProcess.金水菩提.Length)];
                        }
                        else
                        {
                            cfg += EquipmentProcess.金水菩提[RandomGenerator.Next(0, EquipmentProcess.金水菩提.Length)] + "|";
                            cfg += EquipmentProcess.金水菩提[RandomGenerator.Next(0, EquipmentProcess.金水菩提.Length)];
                        }
                    }
                    else if (kind.Equals("10004"))
                    {
                        if (RandomGenerator.Next(0, 20) == 5)
                        {
                            cfg += EquipmentProcess.九曜光华[RandomGenerator.Next(0, EquipmentProcess.九曜光华.Length)] + "|";
                            cfg += EquipmentProcess.九曜光华[RandomGenerator.Next(0, EquipmentProcess.九曜光华.Length)] + "|";
                            cfg += EquipmentProcess.九曜光华[RandomGenerator.Next(0, EquipmentProcess.九曜光华.Length)];
                        }
                        else
                        {
                            cfg += EquipmentProcess.九曜光华[RandomGenerator.Next(0, EquipmentProcess.九曜光华.Length)] + "|";
                            cfg += EquipmentProcess.九曜光华[RandomGenerator.Next(0, EquipmentProcess.九曜光华.Length)];
                        }
                    }
                    else if (kind.Equals("10007"))
                    {
                        if (RandomGenerator.Next(0, 20) == 5)
                        {
                            cfg += EquipmentProcess.四魂之玉[RandomGenerator.Next(0, EquipmentProcess.九曜光华.Length)] + "|";
                            cfg += EquipmentProcess.四魂之玉[RandomGenerator.Next(0, EquipmentProcess.九曜光华.Length)];
                        }
                        else
                        {
                            cfg += EquipmentProcess.四魂之玉[RandomGenerator.Next(0, EquipmentProcess.九曜光华.Length)];
                        }
                    }


                    EquipmentInfo 信息 = new EquipmentInfo
                    {
                        ID = "0",
                        强化 = "0",
                        cID = null,
                        WX = "无",
                        类ID = kind,
                        LSSX = cfg
                    };
                    AddPlayerEquipment(信息);
                    msg = "恭喜您获得了一个新灵饰！";
                    return true;
                }
                if (script.Contains("获得指定灵饰"))
                {
                    var id = script.Replace("获得指定灵饰", "");
                    string cfg = string.Empty;
                    string kind = id;
                    if (kind.Equals("10001"))
                    {
                        if (RandomGenerator.Next(0, 20) == 5)
                        {
                            cfg += EquipmentProcess.玄龙苍珀[RandomGenerator.Next(0, EquipmentProcess.玄龙苍珀.Length)] + "|";
                            cfg += EquipmentProcess.玄龙苍珀[RandomGenerator.Next(0, EquipmentProcess.玄龙苍珀.Length)] + "|";
                            cfg += EquipmentProcess.玄龙苍珀[RandomGenerator.Next(0, EquipmentProcess.玄龙苍珀.Length)];
                        }
                        else
                        {
                            cfg += EquipmentProcess.玄龙苍珀[RandomGenerator.Next(0, EquipmentProcess.玄龙苍珀.Length)] + "|";
                            cfg += EquipmentProcess.玄龙苍珀[RandomGenerator.Next(0, EquipmentProcess.玄龙苍珀.Length)];
                        }
                    }
                    else if (kind.Equals("10002"))
                    {
                        if (RandomGenerator.Next(0, 20) == 5)
                        {
                            cfg += EquipmentProcess.凤羽流苏[RandomGenerator.Next(0, EquipmentProcess.凤羽流苏.Length)] + "|";
                            cfg += EquipmentProcess.凤羽流苏[RandomGenerator.Next(0, EquipmentProcess.凤羽流苏.Length)] + "|";
                            cfg += EquipmentProcess.凤羽流苏[RandomGenerator.Next(0, EquipmentProcess.凤羽流苏.Length)];
                        }
                        else
                        {
                            cfg += EquipmentProcess.凤羽流苏[RandomGenerator.Next(0, EquipmentProcess.凤羽流苏.Length)] + "|";
                            cfg += EquipmentProcess.凤羽流苏[RandomGenerator.Next(0, EquipmentProcess.凤羽流苏.Length)];
                        }
                    }
                    else if (kind.Equals("10003"))
                    {
                        if (RandomGenerator.Next(0, 20) == 5)
                        {
                            cfg += EquipmentProcess.金水菩提[RandomGenerator.Next(0, EquipmentProcess.金水菩提.Length)] + "|";
                            cfg += EquipmentProcess.金水菩提[RandomGenerator.Next(0, EquipmentProcess.金水菩提.Length)] + "|";
                            cfg += EquipmentProcess.金水菩提[RandomGenerator.Next(0, EquipmentProcess.金水菩提.Length)];
                        }
                        else
                        {
                            cfg += EquipmentProcess.金水菩提[RandomGenerator.Next(0, EquipmentProcess.金水菩提.Length)] + "|";
                            cfg += EquipmentProcess.金水菩提[RandomGenerator.Next(0, EquipmentProcess.金水菩提.Length)];
                        }
                    }
                    else if (kind.Equals("10004"))
                    {
                        if (RandomGenerator.Next(0, 20) == 5)
                        {
                            cfg += EquipmentProcess.九曜光华[RandomGenerator.Next(0, EquipmentProcess.九曜光华.Length)] + "|";
                            cfg += EquipmentProcess.九曜光华[RandomGenerator.Next(0, EquipmentProcess.九曜光华.Length)] + "|";
                            cfg += EquipmentProcess.九曜光华[RandomGenerator.Next(0, EquipmentProcess.九曜光华.Length)];
                        }
                        else
                        {
                            cfg += EquipmentProcess.九曜光华[RandomGenerator.Next(0, EquipmentProcess.九曜光华.Length)] + "|";
                            cfg += EquipmentProcess.九曜光华[RandomGenerator.Next(0, EquipmentProcess.九曜光华.Length)];
                        }
                    }
                    else if (kind.Equals("10007"))
                    {
                        if (RandomGenerator.Next(0, 20) == 5)
                        {
                            cfg += EquipmentProcess.四魂之玉[RandomGenerator.Next(0, EquipmentProcess.四魂之玉.Length)] + "|";
                            cfg += EquipmentProcess.四魂之玉[RandomGenerator.Next(0, EquipmentProcess.四魂之玉.Length)];
                        }
                        else
                        {
                            cfg += EquipmentProcess.四魂之玉[RandomGenerator.Next(0, EquipmentProcess.四魂之玉.Length)];
                        }
                    }



                    EquipmentInfo 信息 = new EquipmentInfo
                    {
                        ID = "0",
                        强化 = "0",
                        cID = null,
                        WX = "无",
                        类ID = kind,
                        LSSX = cfg
                    };
                    AddPlayerEquipment(信息);
                    msg = "恭喜您获得了一个新灵饰！";
                    return true;
                }
            }

            return false;
        }

        public PetConfig GetAppointedPetType(string id)
        {
            return ReadPetTypeList().FirstOrDefault(宠物 => 宠物.宠物序号.Equals(id));
        }

        public string 获取宠物名称(string pid)
        {
            return ReadPetTypeList().FirstOrDefault(宠物 => 宠物.宠物序号.Equals(pid)).宠物名字;
        }
        /// <summary>
        /// 返回宠物名字的合成公式
        /// </summary>
        /// </summary>
        /// <returns></returns>
        public List<Formula> 获得宠物合成公式()//合成公式 - 返回宠物名字
        {
            if (合成公式 != null && 合成公式.Count>0) return 合成公式;
            FormulaList = JsonConvert.DeserializeObject<List<Formula>>(FormulaJson);
            合成公式.Clear();
            foreach (var p in FormulaList)
            {
                Formula pet = new Formula();
                pet = p;
                pet.pet1 = ReadPetTypeList().FirstOrDefault(宠物 => 宠物.宠物序号.Equals(p.pet1)).宠物名字;
                pet.pet2 = ReadPetTypeList().FirstOrDefault(宠物 => 宠物.宠物序号.Equals(p.pet2)).宠物名字;
                pet.Result = ReadPetTypeList().FirstOrDefault(宠物 => 宠物.宠物序号.Equals(p.Result)).宠物名字;
                合成公式.Add(p);
            }
            return 合成公式;
        }

        public List<PetConfig> ReadPetTypeList()
        {
            try
            {
                if (PetType_List != null)
                {
                    return PetType_List;
                }

                string cfg = ReadFile(pf + PDC_Path);
                cfg = SkRC4.DES.DecryptRC4(cfg, new DataProcess().GetKey(1));
                string[] 类型数组 = cfg.Split(new[] { "\r\n" }, StringSplitOptions.None);
                PetType_List = new List<PetConfig>();
                foreach (string 类型 in 类型数组)
                {
                    string[] 数组 = 类型.Split('，');
                    if (数组.Length >= 3)
                    {
                        PetConfig 类 = new PetConfig()
                        {
                            宠物名字 = 数组[1],
                            宠物序号 = 数组[0],
                            系别 = 数组[2],
                            默认技能 = new List<string>()
                        };
                        if (数组.Length >= 5)
                        {
                            string[] 技能组 = 数组[4].Split('|');
                            foreach (string j in 技能组)
                            {
                                if (SkTools.JudgeObjectType.NumOrNot(j))
                                {
                                    类.默认技能.Add(j);
                                }
                            }
                        }

                        类.阶数 = 数组.Length >= 4 ? 数组[3] : "11";
                        PetType_List.Add(类);
                    }
                }

                return PetType_List;
            }
            catch (Exception)
            {
                return new List<PetConfig>();
            }
        }
        public static String MaindatData = null;
        public string ReadFile(string name)
        {
            StreamReader sr = null;
            try
            {
                if (!File.Exists(name))
                {
                    //if (!r)
                    //{
                    //    return null;
                    //}
                    return null;
                }
                String text = "";
                using (var file = new FileStream(name, FileMode.Open, FileAccess.ReadWrite, FileShare.ReadWrite))
                {
                    using (sr = new StreamReader(file))
                    {
                        string str;
                        text = sr.ReadToEnd();
                        sr.Close();

                    }

                }

                return text;
            }
            catch (Exception ex)
            {
                LogSystem.JoinLog(LogSystem.EventKind.加入日志, "函数 数据处理.读取文件 出现错误：【" + ex.StackTrace + "】\r\n【" + ex.Message + "】");
                return null;
            }
            finally
            {
                if (sr != null) sr.Close();
            }
        }
        /// <summary>
        /// 弃用，读取过程中占用文件
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        public string ReadFileOld(string name)
        {
            if (name == pf + PF_Path && MaindatData != null)
            {
                return MaindatData;
            }
            if (!File.Exists(name))
            {
                return null;
            }

            string text = "";
            using (StreamReader sr = new StreamReader(name))
            {
                string str;
                while ((str = sr.ReadLine()) != null)
                {
                    text += str;
                }
            }

            return text;
        }

        public string ReadFile(string name, bool utf8)
        {
            if (!File.Exists(name))
            {
                return null;
            }

            string text;
            using (StreamReader sr = new StreamReader(name))
            {
                text = sr.ReadToEnd();
            }

            return text;
        }

        internal List<PetInfo> ReadPlayerPetList(bool forcefresh = false)
        {
            //      System.Diagnostics.Stopwatch sw = new System.Diagnostics.Stopwatch();
            //sw.Start();
            if (forcefresh == false)
            {
            }

            /*if (PetList.Count != 0 && z)
            {
                return PetList;
            }*/
            string 存档 = GetStr();
            if (存档 == null)
            {
                new DataProcess().ThrowError(1);
            }

            // ReSharper disable once PossibleNullReferenceException
            string[] 存档组 = 存档.Split(new[] { "O4F89" }, StringSplitOptions.None);
            存档 = 存档组[2];
            List<PetInfo> 宠物信息数组 = new List<PetInfo>();
            if (!string.IsNullOrEmpty(存档))
            {
                存档 = SkRC4.DES.DecryptRC4(存档, new DataProcess().GetKey(1, true) + GetKey1(2)); //这里调用了RC4解密,存档我们是用的RC4进行加密的
                存档 = new CompressJson().UncompressPetJson(存档);
                存档 = 存档.Replace("\"\"", "\"0\"");
                JsonSerializerSettings jsetting = new JsonSerializerSettings()
                {
                    DefaultValueHandling = DefaultValueHandling.Include,
                    NullValueHandling = NullValueHandling.Ignore
                };
                宠物信息数组 = JsonConvert.DeserializeObject<List<PetInfo>>(存档, jsetting);
            }

            //  sw.Stop();
            //      Console.WriteLine("Total：" + sw.ElapsedMilliseconds + "ms");
            PetList = 宠物信息数组;
            return 宠物信息数组;
        }

        //private const bool z = false;
        /*public String CompressPetJson(String json)
        {
            String[] 宠物序号 = { "宠物序号", "id" };
            String[] 形象 = { "形象", "xx" };
            String[] 等级 = { "等级", "dj" };
            String[] 五行 = { "五行", "wx" };
            String[] 生命 = { "生命", "sm" };
            String[] 魔法 = { "魔法", "mf" };
            String[] 当前经验 = { "当前经验", "jy" };
            String[] 最大生命 = { "最大生命", "sm1" };

            String[] 最大魔法 = { "最大魔法", "mf1" };
            String[] 攻击 = { "攻击", "gj" };
            String[] 防御 = { "防御", "fy" };
            String[] 闪避 = { "闪避", "sb" };
            String[] 速度 = { "速度", "sd" };
            String[] 状态 = { "状态", "zt" };

            String[] 宠物名字 = { "宠物名字", "mz" };
            String[] 自定义宠物名字 = { "自定义宠物名字", "mz1" };
            String[] 位置 = { "位置", "wz" };
            String[] 成长 = { "成长", "cz" };
            String[] 命中 = { "命中", "mz1" };

            //String[] 被抽取 = { "被抽取", "bcq" };

            String[] 压缩1 = { "\"},{\"", "fg" };
            String[] 压缩2 = { "\":\"", "'" };

            json = json.Replace(宠物序号[0], 宠物序号[1]);
            json = json.Replace(形象[0], 形象[1]);
            json = json.Replace(等级[0], 等级[1]);
            json = json.Replace(五行[0], 五行[1]);
            json = json.Replace(最大生命[0], 最大生命[1]);
            json = json.Replace(最大魔法[0], 最大魔法[1]);
            json = json.Replace(生命[0], 生命[1]);
            json = json.Replace(魔法[0], 魔法[1]);


            json = json.Replace(攻击[0], 攻击[1]);
            json = json.Replace(防御[0], 防御[1]);
            json = json.Replace(闪避[0], 闪避[1]);
            json = json.Replace(速度[0], 速度[1]);
            json = json.Replace(状态[0], 状态[1]);
            json = json.Replace(宠物名字[0], 宠物名字[1]);
            json = json.Replace(自定义宠物名字[0], 自定义宠物名字[1]);

            json = json.Replace(位置[0], 位置[1]);
            json = json.Replace(成长[0], 成长[1]);
            json = json.Replace(命中[0], 命中[1]);
            json = json.Replace(当前经验[0], 当前经验[1]);

            //json = json.Replace(被抽取[0], 被抽取[1]);

            json = json.Replace(压缩1[0], 压缩1[1]);
            json = json.Replace(压缩2[0], 压缩2[1]);

            return json;
        }*/

        internal UserInfo ReadUserInfo1()
        {
            UserInfo user = ReadUserInfo();
            user.b = "2";
            user.名字 = "呵呵";
            user.版本号 = "....";
            new DataProcess().SaveUserDataFile(user);
            return user;
        }

        //internal static 用户信息 _用户存档;
        public static String mid = null;
        internal UserInfo ReadUserInfo()
        {
            /*if (_用户存档 != null && z)
            {
                return _用户存档;
            }*/
            string 存档 = GetStr();
            if (存档 == null)
            {
                new DataProcess().ThrowError(1);
            }

            string[] 存档组 = 存档.Split(new[] { "O4F89" }, StringSplitOptions.None);
            存档 = 存档组[0];
            存档 = SkRC4.DES.DecryptRC4(存档, new DataProcess().GetKey(1, true) + GetKey1(0)); //这里调用了RC4解密,存档我们是用的RC4进行加密的
            UserInfo 用户存档 = new UserInfo();
            if (!string.IsNullOrEmpty(存档))
            {
                用户存档 = JsonConvert.DeserializeObject<UserInfo>(存档);

                var p = ReadAppointedPet(用户存档.主宠物, PetList);
                if (p != null)
                {
                    mid = p.形象;
                }
            }
            if (用户存档.魂器列表 == null) 用户存档.魂器列表 = new Dictionary<string, hqinfo>();
            if (用户存档.神兵列表 == null) 用户存档.神兵列表 = new Dictionary<string, int>();
            if (用户存档.皮肤列表 == null) 用户存档.皮肤列表 = new List<string>();
            if (用户存档.解锁页信息 == null) 用户存档.解锁页信息 = new Dictionary<string, int>();
            return 用户存档;
        }
    

        internal PetInfo ReadAppointedPet(string 宠物序号, List<PetInfo> 宠物列表 = null)
        {
            if (宠物列表 == null)
            {
                宠物列表 = ReadPlayerPetList();
            }

            return 宠物列表.FirstOrDefault(当前 => 当前.宠物序号.Equals(宠物序号));
        }

        internal PetInfo GetCCMAXPet()
        {
            List<PetInfo> 宠物列表 = ReadPlayerPetList();
            宠物列表 = 宠物列表.OrderByDescending(宠物信息 => Convert.ToDouble(宠物信息.成长)).ToList();
            return 宠物列表[0];
        }

        internal List<PetInfo> ReadPets_Bag()
        {
            List<PetInfo> bag = new List<PetInfo>();
            UserInfo user = ReadUserInfo();
            if (user.宠物1 != null && user.宠物1 != "Null")
            {
                PetInfo 位置1 = ReadAppointedPet(user.宠物1);
                if (位置1 != null) bag.Add(位置1);
            }

            if (user.宠物2 != null && user.宠物2 != "Null")
            {
                PetInfo 位置2 = ReadAppointedPet(user.宠物2);
                if (位置2 != null) bag.Add(位置2);
            }

            if (user.宠物3 != null && user.宠物3 != "Null")
            {
                PetInfo 位置3 = ReadAppointedPet(user.宠物3);
                if (位置3 != null) bag.Add(位置3);
            }

            foreach (PetInfo pet in bag)
            {
                pet.状态 = "1";
                if (pet.宠物序号 == user.主宠物)
                {
                    pet.状态 = "0";
                }
            }

            return bag;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="宠物列表"></param>
        /// <param name="强制更新">这个参数暂时没起到作用</param>
        /// <returns></returns>
        internal bool SavePetDateFile(List<PetInfo> 宠物列表) //, bool 强制更新 = false)
        {
            //System.Diagnostics.Stopwatch sw = new System.Diagnostics.Stopwatch();
            //sw.Start();
            try
            {
                if (宠物列表 == null)
                {
                    return false;
                }

                ReadPlayerPetList();
                PetList = 宠物列表;

                //  String 存档 = jsonTo.ListToJSON(宠物列表,false,true,"宠物名字,等级,五行");
                string 存档 = jsonTo.GetPetJson(宠物列表);

                //    存档 = RC4.EncryptRC4wq(存档, 数据处理.new 数据处理().获取密钥(1));
                string 拼接 = GetStr();
                string[] 存档组 = 拼接.Split(new[] { "O4F89" }, StringSplitOptions.None);
                存档组[2] = 存档;
                if (AdminMode == 0)
                {
                    JointDataFile(存档组, true);
                }
                else
                {
                    存档 = JointDataFile(存档组, true);
                    SaveFile(存档, pf + PF_Path);
                }

                //sw.Stop();
                //Console.WriteLine("Total：" + sw.ElapsedMilliseconds + "ms");
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        internal bool Update_APDF(string 宠物序号, PetInfo pet) //更新指定宠物存档
        {
            //System.Diagnostics.Stopwatch sw = new System.Diagnostics.Stopwatch();
            //sw.Start();
            List<PetInfo> petList = ReadPlayerPetList();
            bool r = false;
            for (int i = 0; i < petList.Count; i++)
            {
                if (petList[i].宠物序号.Equals(宠物序号))
                {
                    petList[i] = pet;
                    r = true;
                }
            }

            r = SavePetDateFile(petList);
            //sw.Stop();
            //Console.WriteLine("Total：" + sw.ElapsedMilliseconds + "ms");
            return r;
        }

        /*internal bool ToLv1(string 宠物序号)
        {
            PetInfo pet = ReadAppointedPet(宠物序号);
            pet.当前经验 = "1";
            pet.等级 = "1";
            return Update_APDF(宠物序号, pet);
        }*/

        private bool AddExp(string 宠物序号, long amplification)
        {
            PetInfo pet = ReadAppointedPet(宠物序号);
            if (!SkTools.JudgeObjectType.NumOrNot(pet.当前经验)) pet.当前经验 = "0";
            pet.当前经验 = (Convert.ToInt64(pet.当前经验) + amplification).ToString();
            if (Convert.ToInt64(pet.当前经验) < -100000000)
            {
                pet.当前经验 = int.MaxValue.ToString();
            }

            return Update_APDF(宠物序号, pet);
        }
        private bool SubCC(string 宠物序号, long CC)
        {
            PetInfo pet = ReadAppointedPet(宠物序号);
            pet.成长 = (Convert.ToInt64(pet.成长) - CC).ToString();
            return Update_APDF(宠物序号, pet);
        }

        public bool saveDat()
        {
            string 存档 = GetStr();
            if (存档 == null)
            {
                new DataProcess().ThrowError(1);
            }

            string[] 存档组 = 存档.Split(new[] { "O4F89" }, StringSplitOptions.None);

            for (int j = 0; j < 存档组.Length; j++)
            {

                存档 = 存档组[j];
                存档 = SkRC4.DES.DecryptRC4(存档, new DataProcess().GetKey(1, true) + GetKey1(j)); //这里调用了RC4解密,存档我们是用的RC4进行加密的
                存档组[j] = 存档;

            }

            old = true;
            var info = ReadUserInfo();
            old = false;

            存档 = JointDataFile(存档组, true);
            SaveFile(存档, pf + PF_Path);
            return true;
        }
        internal bool SaveUserDataFile(UserInfo user, bool oold = false)
        {
            try
            {
                string 存档 = jsonTo.EntityToJson(user);
                string 存档1 = 存档;
                //   存档 = RC4.EncryptRC4wq(存档, 数据处理.new 数据处理().获取密钥(1));
                string 拼接 = GetStr(oold);
                string[] 存档组 = { "", "", "", "", "" };
                if (拼接 != null)
                {
                    存档组 = 拼接.Split(new[] { "O4F89" }, StringSplitOptions.None);
                }

                if (string.IsNullOrEmpty(存档))
                {
                    if (存档1.Length != 0)
                    {
                        return false;
                    }
                }

                存档组[0] = 存档;
                if (AdminMode == 0)
                {
                    JointDataFile(存档组, true);
                }
                else
                {
                    存档 = JointDataFile(存档组, true);
                    SaveFile(存档, pf + PF_Path);
                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        public string GetKey1(int 下标)
        {
            string 拼接;
            if (Res.ResourceContiainer == null || AdminMode == 1)
            {
                拼接 = ReadFile(pf + PF_Path);
            }
            else
            {
                拼接 = Res.ResourceContiainer;
            }

            if (拼接 == null)
            {
                return "";
            }

            string 密钥 = "";
            if (拼接.Contains("9527OA"))
            {
                string[] 分割 = 拼接.Split(new[] { "9527OA" }, StringSplitOptions.None);
                拼接 = 分割[0];
                密钥 = 分割[1];
            }

            string[] 存档组 = 拼接.Split(new[] { "O4F89" }, StringSplitOptions.None);
            if (存档组.Length > 下标)
            {
                string[] 分割 = 存档组[下标].Split(new[] { "O19A87" }, StringSplitOptions.None);
                if (分割.Length != 2)
                {
                    return "";
                }

                密钥 = 分割[1];
            }

            return 密钥;
        }

        private string GetHash(string 拼接)
        {



            if (拼接 == null)
            {
                return "";
            }

            string 哈希 = "";
            if (拼接.IndexOf("9527OA", StringComparison.Ordinal) != -1)
            {
                string[] 分割 = 拼接.Split(new[] { "9527OA" }, StringSplitOptions.None);
                哈希 = 分割[1];
            }

            return 哈希;
        }

        //public static int 回档次数 = 0;
        public string GetStr(bool oold = false)
        {
            /*String 当前存档Hash = GetFileHash(PF_Path);
            if (当前存档Hash != 存档Hash && 存档Hash != null) 
            {
                NativeMethods.AutoClosedMsgBox.Show("游戏运行时不能SL,由此产生的存档损坏概不修复!","警告",2000);
                Environment.Exit(0);
            }*/
            string 拼接;
            if (Res.ResourceContiainer == null || AdminMode == 1)
            {
                拼接 = ReadFile(pf + PF_Path);
            }
            else
            {
                拼接 = Res.ResourceContiainer;
            }

            if (拼接 == null)
            {
                return null;
            }

            //String 密钥 = "";
            if (拼接.IndexOf("9527OA", StringComparison.Ordinal) != -1)
            {
                string[] 分割 = 拼接.Split(new[] { "9527OA" }, StringSplitOptions.None);
                string hs = GetHash(拼接);
                拼接 = 分割[0];
                string key = new DataProcess().GetKey(1, true) + "MaskSB";
                if (oold)
                {
                    key = new DataProcess().GetKey(1) + "MaskSB";
                }
                //string str0 = 拼接;
                //Console.WriteLine("拼接:" + str0);
                //string str1 = SkCryptography.GetHash.GetStringHash(拼接);
                //Console.WriteLine("加密拼接:" + str1);
                //string str = SkRC4.DES.EncryptRC4(SkCryptography.GetHash.GetStringHash(拼接), key);
                //Console.WriteLine("Mask校验:"+str);

                if (!hs.Equals(SkRC4.DES.EncryptRC4(SkCryptography.GetHash.GetStringHash(拼接), key)))
                {
                    Tools.ForcedExit("大帅比青衫校验异常");//存档损坏
                }

                //密钥 = 分割[1];
            }

            return 拼接;
        }
        public static bool old = true;
        public string JointDataFile(string[] saveList, bool key, string 存档版本 = null)
        {
            string 装备存档 = "";
            string 进度存档 = "";
            string 任务存档 = "";
            string 加密密钥 = "";
            if (key)
            {
                加密密钥 = RandomGenerator.Next(10000000, 99000000).ToString();
                加密密钥 = SkRC4.DES.EncryptRC4(加密密钥, new DataProcess().GetKey(1, true));
            }

            for (int i = 0; i < saveList.Length; i++)
            {
                if (saveList[i] == null) saveList[i] = "[]";
                if (saveList[i].IndexOf("{", StringComparison.Ordinal) != -1 ||
                    saveList[i].IndexOf("[", StringComparison.Ordinal) != -1)
                {
                    string my = new DataProcess().GetKey(1, true) + 加密密钥;

                    saveList[i] = SkRC4.DES.EncryptRC4(saveList[i], my) + "O19A87" + 加密密钥;
                }
            }

            if (存档版本 == null)
            {
                存档版本 =
                    SkRC4.DES.EncryptRC4(DFV.ToString(CultureInfo.InvariantCulture), new DataProcess().GetKey(1, true) + 加密密钥) +
                    "O19A87" + 加密密钥;
            }
            if (saveList.Length >= 5)
            {
                装备存档 = saveList[4];
            }

            if (saveList.Length >= 6)
            {
                进度存档 = saveList[5];
            }

            if (saveList.Length >= 7)
            {
                任务存档 = saveList[6];
            }

            var 存档 = saveList[0] + "O4F89" + saveList[1] + "O4F89" + saveList[2] + "O4F89" + 存档版本 + "O4F89" + 装备存档 +
                     "O4F89" + 进度存档 + "O4F89" + 任务存档;
            string 哈希 = SkCryptography.GetHash.GetStringHash(存档);
            //  存档 = RC4.EncryptRC4wq(存档, 加密密钥);
            存档 += "9527OA" + SkRC4.DES.EncryptRC4(哈希, new DataProcess().GetKey(1, true) + "MaskSB");
            if (AdminMode != 1)
            {
                Res.ResourceContiainer = 存档;
            }

            return 存档;
        }

        internal string GetDfv() //获取存档版本号
        {
            if (!File.Exists(pf + PF_Path))
            {
                return null;
            }

            string 存档 = "";
            using (StreamReader sr = new StreamReader(pf + PF_Path))
            {
                string str;
                while ((str = sr.ReadLine()) != null)
                {
                    存档 += str;
                }
            }

            /*if (存档 == null)
            {
                new 数据处理().ThrowError(1);
            }*/
            string 拼接 = 存档;
            string 密钥 = "";
            if (拼接.IndexOf("9527OA", StringComparison.Ordinal) != -1)
            {
                string[] 分割 = 拼接.Split(new[] { "9527OA" }, StringSplitOptions.None);
                密钥 = 分割[1];
            }

            string[] 存档组 = 存档.Split(new[] { "O4F89" }, StringSplitOptions.None);
            if (存档组.Length > 3)
            {
                密钥 = 存档组[3].Split(new[] { "O19A87" }, StringSplitOptions.None)[1];
            }

            try
            {
                存档 = SkRC4.DES.DecryptRC4(存档组[3], new DataProcess().GetKey(1, true) + 密钥);
            }
            catch
            {
                存档 = "broken";
            }

            if (Convert.ToDouble(存档) >= 1.3 || 存档 == "broken")
            {
                return 存档;
            }
            else
            {
                SkTools.AutoClosedMsgBox.Show(Res.RM.GetString("存档版本过低"), Res.RM.GetString("警告"), 2000);
                Tools.ForcedExit("存档版本过低");
                return 存档;
            }
        }

        internal bool ChangeMainPet(string petXh)
        {
            if (ReadAppointedPet(petXh) == null) return false;
            UserInfo user = ReadUserInfo();
            user.主宠物 = petXh;
            SaveUserDataFile(user);
            return true;
        }

        internal List<PetInfo> ReadPets_Depot()
        {
            //这里的逻辑是:只要不在背包内的宠物,就都在仓库中.
            List<PetInfo> petList = ReadPlayerPetList();
            UserInfo user = ReadUserInfo();
            List<PetInfo> depotList = petList.Where(pet =>
                    pet.宠物序号 != user.宠物1 && pet.宠物序号 != user.宠物2 && pet.宠物序号 != user.宠物3 && pet.宠物序号 != user.主宠物)
                .ToList();
            PetDepotCache(depotList);
            return depotList;
        }

        /// <summary>
        /// 宠物仓库一次性要读取的JSON太长,实时加载效果不太行,所以测试一下写缓存的方式
        /// </summary>
        /// <returns></returns>
        private void PetDepotCache(List<PetInfo> petList)
        {
            //System.Diagnostics.Stopwatch sw = new System.Diagnostics.Stopwatch();
            //sw.Start();
            //  宠物列表 = 宠物属性计算.计算宠物属性(宠物列表); //计算属性
            string html = ReadFile(ProgramPath + @"\" + @"\PageMain\Pasture.html", true);
            string json = JsonConvert.SerializeObject(petList);
            json = json.Replace("\"", "\\\"");
            html = html.Replace("%load%", json);
            string rl= ReadUserInfo().牧场容量;
            if (rl == null || rl == "") rl = "80";
            html = html.Replace("%maxRL%",rl);
            SaveFile(html, pf + PDB_Path);
            //Console.WriteLine("保存仓库耗时:" + sw.ElapsedMilliseconds + "ms");
        }

        internal bool PutPetToDepot(string petXh)//存放宠物
        {
            UserInfo user = ReadUserInfo();
            if (user.宠物3 == petXh)
            {
                user.宠物3 = null;
            }

            if (user.宠物2 == petXh)
            {
                user.宠物2 = null;
            }

            if (user.宠物1 == petXh)
            {
                user.宠物1 = null;
            }

            return petXh != user.主宠物 && SaveUserDataFile(user);
        }

        private bool TaskPetProcess(string 宠物)
        {
            if (宠物 == null || 宠物 == "Null" || 宠物 == "0")
            {
                return true;
            }

            return false;
        }

        internal bool TakePet(string petXh)
        {
            UserInfo user = ReadUserInfo();
            if (ReadAppointedPet(petXh) != null)
            {
                if (user.宠物1 != petXh && user.宠物2 != petXh && user.宠物3 != petXh)
                {
                    if (TaskPetProcess(user.宠物1))
                    {
                        user.宠物1 = petXh;
                    }
                    else if (TaskPetProcess(user.宠物2))
                    {
                        user.宠物2 = petXh;
                    }
                    else if (TaskPetProcess(user.宠物3))
                    {
                        user.宠物3 = petXh;
                    }
                    else
                    {
                        return false; //背包没有空位
                    }
                }
                else
                {
                    return false; //宠物已经在背包中
                }
            }
            else
            {
                return false; //宠物不存在
            }

            return SaveUserDataFile(user);
        }

        internal bool AbandonPet(string petXh)
        {
            if (ReadAppointedPet(petXh) != null)
            {
                UserInfo user = ReadUserInfo();
                if (user.主宠物.Equals(petXh))
                {
                    return false;
                }

                List<PetInfo> petList = ReadPlayerPetList();
                List<PetInfo> newList = new List<PetInfo>();
                if (user.宠物1 != null && user.宠物1.Equals(petXh))
                {
                    user.宠物1 = "Null";
                }
                else if (user.宠物2 != null && user.宠物2.Equals(petXh))
                {
                    user.宠物2 = "Null";
                }
                else if (user.宠物3 != null && user.宠物3.Equals(petXh))
                {
                    user.宠物3 = "Null";
                }

                SaveUserDataFile(user);
                bool r = false;
                foreach (PetInfo t in petList)
                {
                    if (t.宠物序号.Equals(petXh)) continue;
                    newList.Add(t);
                    r = true;
                }

                if (r)
                {
                    Undress_Pet(petXh);
                }

                r = SavePetDateFile(newList);
                if (!newList.Count.Equals(petList.Count))
                {
                    return false;
                }

                return r;
            }

            return false;
        }

        public string AddPet(PetInfo pet)//添加宠物到牧场-放到牧场-宠物上限-牧场上限
        {
            List<PetInfo> petList = ReadPlayerPetList() ?? new List<PetInfo>();
            var u = ReadUserInfo();
            if (petList.Count >= (u.牧场容量==null?80: Convert.ToInt16(u.牧场容量)))
            {
                return "-1";
            }

            int i = 0;
            //System.Diagnostics.Stopwatch sw = new System.Diagnostics.Stopwatch();
            //sw.Start();
            while (ReadAppointedPet(pet.宠物序号, petList) != null)
            {
                i++;
                pet.宠物序号 = i.ToString();
            }
            //sw.Stop();
            //Console.WriteLine("自增ID耗时:" + sw.ElapsedMilliseconds + "MS");

            petList.Add(pet);
            SavePetDateFile(petList);
            StudyDefaultSkill(pet.宠物序号);
            return pet.宠物序号;
        }

        internal FBROP GetFBROP(string mapId)
        {
            string 存档 = GetStr();
            if (存档 == null)
            {
                new DataProcess().ThrowError(1);
            }

            // ReSharper disable once PossibleNullReferenceException
            string[] 存档组 = 存档.Split(new[] { "O4F89" }, StringSplitOptions.None);
            存档 = 存档组[5];
            if (string.IsNullOrEmpty(存档)) return null;
            存档 = SkRC4.DES.DecryptRC4(存档, new DataProcess().GetKey(1, true) + GetKey1(5)); //这里调用了RC4解密,存档我们是用的RC4进行加密的
            try
            {
                var 进度 = JsonConvert.DeserializeObject<List<FBROP>>(存档);
                foreach (FBROP 副本 in 进度)
                {
                    if (副本.id == mapId)
                    {
                        return 副本;
                    }
                }
            }
            catch
            {
                return null;
            }

            return null;
        }

        private List<FBROP> GetFBROPList()
        {
            string 存档 = GetStr();
            if (存档 == null)
            {
                return null;
            }

            string[] 存档组 = 存档.Split(new[] { "O4F89" }, StringSplitOptions.None);
            存档 = 存档组[5];
            if (!string.IsNullOrEmpty(存档))
            {
                存档 = SkRC4.DES.DecryptRC4(存档, new DataProcess().GetKey(1, true) + GetKey1(5)); //这里调用了RC4解密,存档我们是用的RC4进行加密的
                try
                {
                    var rop = JsonConvert.DeserializeObject<List<FBROP>>(存档);
                    return rop;
                }
                catch
                {
                    return new List<FBROP>();
                }
            }

            return new List<FBROP>();
        }

        internal bool ChangeROP(string mapId, string floor)
        {
            FBROP fbInfo = GetFBROP(mapId);
            List<FBROP> rop = GetFBROPList();
            if (fbInfo == null)
            {
                fbInfo = new FBROP() { id = mapId, num = floor };
                rop.Add(fbInfo);
            }
            else
            {
                foreach (FBROP t in rop)
                {
                    if (t.id!=null && t.id.Equals(mapId))
                    {
                        t.num = floor;
                    }
                }
            }

            return SaveFBROPList(rop);
        }

        internal bool PromoteROP(string mapId)
        {
            FBROP fbInfo = GetFBROP(mapId);
            List<FBROP> rop = GetFBROPList();
            int Floor = 1;
            if (fbInfo == null)
            {
                fbInfo = new FBROP() { id = mapId, num = Floor.ToString() };
                rop.Add(fbInfo);
            }
            else
            {
                foreach (FBROP t in rop)
                {
                    if (t.id!=null &&t.id.Equals(mapId))
                    {
                        t.num = (Convert.ToInt32(t.num) + Floor).ToString();
                    }
                }
            }

            return SaveFBROPList(rop);
        }

        private bool SaveFBROPList(List<FBROP> 进度) //保存进度列表
        {
            string 存档 = jsonTo.ListToJson(进度);

            //    存档 = RC4.EncryptRC4wq(存档, new 数据处理().获取密钥(1));
            string 拼接 = GetStr();
            string[] 存档组 = 拼接.Split(new[] { "O4F89" }, StringSplitOptions.None);
            存档组[5] = 存档;
            if (AdminMode == 0)
            {
                JointDataFile(存档组, true);
            }
            else
            {
                存档 = JointDataFile(存档组, true);
                SaveFile(存档, pf + PF_Path);
            }

            return true;
        }

        public List<EvolutionWay> GetAllEW()
        {
            string cfg = ReadFile(pf + EC_Path);
            if (cfg == null || cfg.Length <= 0)
            {
                return new List<EvolutionWay>();
            }

            cfg = SkRC4.DES.DecryptRC4(cfg, new DataProcess().GetKey(1));
            var way = JsonConvert.DeserializeObject<List<EvolutionWay>>(cfg);
            return way;
        }
        public static string stringLoadSBJSON = null;

        public static string stringLoadLZJSON = null;

        public bool AddEW(EvolutionWay way)
        {
            List<EvolutionWay> 进化组 = GetAllEW();
            for (int i = 0; i < 进化组.Count; i++)
            {
                if (进化组[i].petID.Equals(way.petID))
                {
                    进化组[i] = way;
                    SaveAllEW(进化组);
                    return true;
                }
            }

            进化组.Add(way);
            SaveAllEW(进化组);
            return true;
        }

        private void SaveAllEW(List<EvolutionWay> way)
        {
            string 存档 = jsonTo.ListToJson(way);
            存档 = SkRC4.DES.EncryptRC4(存档, new DataProcess().GetKey(1));
            SaveFile(存档, pf + EC_Path);
        }

        public EvolutionWay GetAppointedEW(string petId) //神系如果AB路线为空则默认A消耗玉露，B消耗天仙；五系则对应为A丹，B丹
        {
            List<EvolutionWay> 进化组 = GetAllEW();
            string 宠物系别 = GetAppointedPetType(petId).系别;
            foreach (EvolutionWay t in 进化组)
            {
                if (t.petID.Equals(petId))
                {
                    if (t.AI == null || t.AI == "0" || t.AI == "Null" || t.AI.Length == 0)
                    {
                        t.AI = petId;
                        if (宠物系别.Equals("神") || 宠物系别.Equals("神圣"))
                        {
                            t.ALV = "60";
                            t.AP = "2016110545"; //玉露结晶
                        }
                        else
                        {
                            t.ALV = "40";
                            t.AP = "2016110512"; //A丹
                        }
                    }

                    if (t.BI == null || t.BI == "0" || t.BI == "Null" || t.BI.Length == 0)
                    {
                        t.BI = petId;
                        if (宠物系别.Equals("神") || 宠物系别.Equals("神圣"))
                        {
                            t.BLV = "60";
                            t.BP = "2016110546"; //天仙玉露
                        }
                        else
                        {
                            t.BLV = "40";
                            t.BP = "2016110513"; //B丹
                        }
                    }

                    return t;
                }
            }

            EvolutionWay 路线 = new EvolutionWay() { AI = petId, BI = petId };
            if (宠物系别.Equals("神") || 宠物系别.Equals("神圣"))
            {
                路线.ALV = "60";
                路线.AP = "2016110545"; //玉露结晶
                路线.BLV = "60";
                路线.BP = "2016110546"; //天仙玉露
            }
            else
            {
                路线.ALV = "40";
                路线.AP = "2016110512"; //A丹
                路线.BLV = "40";
                路线.BP = "2016110513"; //B丹
            }

            return 路线;
        }
        /*public bool AddSW(合成公式 公式)
        {
            List<合成公式> 合成公式组 = GetAllSW();
            for (int i = 0; i < 合成公式组.Count; i++)
            {
                if (合成公式组[i].AID.Equals(公式.AID))
                {
                    合成公式组[i] = 公式;
                    SaveAllSW(合成公式组);
                    return true;
                }
            }
            合成公式组.Add(公式);
            SaveAllSW(合成公式组);
            return true;
        }*/
        /*public void SaveAllSW(List<合成公式> 公式)
        {
            string 存档 = jsonTo.ListToJson(公式);
            存档 = RC4.EncryptRC4wq(存档, new DataProcess().GetKey(1));
            SaveFile(存档, SC_Path);
        }*/
        /*public List<合成公式> GetAllSW()
        {
            string 配置 = ReadFile(SC_Path);
            if (配置 == null || 配置.Length <= 0)
            {
                return new List<合成公式>();
            }
            配置 = RC4.DecryptRC4wq(配置, new DataProcess().GetKey(1));
            var 路线 = JsonConvert.DeserializeObject<List<合成公式>>(配置);
            return 路线;
        }*/

        /// <summary>
        /// 这个还没写完
        /// </summary>
        /// <param name="main"></param>
        /// <param name="vice"></param>
        /// <param name="god"></param>
        /// <returns></returns>
        /*internal string GetAppointedSW(PetInfo main, PetInfo vice, int god)
        {
            string result;
            string tmp1 = Enum.Parse(typeof(PetProcess.五行序号), main.五行).ToString();
            string tmp2 = Enum.Parse(typeof(PetProcess.五行序号), vice.五行).ToString();

            string path = Path.Combine(SF_Path, tmp1 + tmp2, ".sys");


            string config = ReadFile(path);
            if (string.IsNullOrEmpty(config))
            {
                return null;
            }
            string[] allsw = config.Split(new[] { "\r\n" }, StringSplitOptions.None);
            foreach (string sw in allsw)
            {
                string[] 因果 = sw.Split('=');
                string[] 详因 = 因果[0].Split('+');

                int c = 0;

                if (详因[0].Contains('|'))
                {
                    string[] mc = 详因[0].Split('|');
                    if (mc.Contains(main.形象))
                    {
                        c += 1;
                    }
                }
                else
                {
                    if (main.形象.Equals(详因[0]))
                    {
                        c += 1;
                    }
                }
                if (c == 1)
                {
                    if (详因[1].Contains('|'))
                    {
                        string[] vc = 详因[1].Split('|');
                        if (vc.Contains(vice.形象))
                        {
                            c += 1;
                        }
                    }
                    else
                    {
                        if (vice.形象.Equals(详因[1]))
                        {
                            c += 1;
                        }
                    }
                }
                if (c == 2)
                {
                    result = 因果[1];
                    break;
                }
            }


            /*List<合成公式> allSw = GetAllSW();
            foreach (合成公式 sw in allSw)
            {
                string[] aidList = null;
                if (sw.AID.Contains('|'))
                {
                    aidList = sw.AID.Split('|');
                }
                else
                {
                    aidList[0] = sw.AID;
                }
                foreach (string aid in aidList)
                {
                    if (aid.Equals(main.形象))
                    {
                        if (sw.ACC != null && Convert.ToDouble(sw.ACC) > Convert.ToDouble(main.成长))
                        {
                            return "主宠成长未达到要求!";
                        }
                        string[] bidList = null;
                        if (sw.BID.Contains('|'))
                        {
                            bidList = sw.BID.Split('|');
                        }
                        else
                        {
                            bidList[0] = sw.BID;
                        }
                        foreach (string bid in bidList)
                        {
                            if (bid.Equals(main.形象))
                            {
                                if (sw.BCC != null && Convert.ToDouble(sw.BCC) > Convert.ToDouble(main.成长))
                                {
                                    return "副宠成长未达到要求!";
                                }
                            }
                        }
                        if (!sw.CID.Contains('|'))
                        {
                            return sw.CID;
                        }
                        string[] cidList = sw.CID.Split('|');
                        if (god == 0)
                        {
                            List<string> 结果列表 = null;
                            string[] cidGl = sw.GL.Split('|');
                            for (int i = 0; i < cidList.Length; i++)
                            {
                                for (int ii = 0; ii < Convert.ToInt32(cidGl[i]); ii++)
                                {
                                    结果列表.Add(cidList[i]);
                                }
                            }
                            return 结果列表[RandomGenerator.Next(0, 结果列表.Count)];
                        }
                        if (god == 1)
                        {
                            foreach (string CID in cidList)
                            {
                                if (GetAppointedPetType(CID).系别.Equals("神"))
                                {
                                    return CID;
                                }
                            }

                        }
                    }
                    else
                    {
                        return null;
                    }
                }
            }
            return null;
        }*/
        public static bool isLocalOnlineTask = false;
        public List<TaskInfo> GetAllTaskAim()
        {
            string 存档 = ReadFile(pf + TDC_Path + @"\_0.task");
            List<TaskInfo> 任务 = new List<TaskInfo>();
            if (string.IsNullOrEmpty(存档)) return 任务;
            存档 = SkRC4.DES.DecryptRC4(存档, new DataProcess().GetKey(1));
            任务 = JsonConvert.DeserializeObject<List<TaskInfo>>(存档);
            if (isLocalOnlineTask && getPower())
            {
                string json = SkRC4.DES.DecryptRC4(ReadFile(@"D:\code\寒假代码\VS\ShikongTools\ShikongTools\bin\Debug\PageMain\online\tlinfo.ini"), new DataProcess().GetKey(1));
                var zxtask = JsonConvert.DeserializeObject<List<TaskInfo>>(json);
                for (var i = 0; i < zxtask.Count; i++)
                {
                    zxtask[i].网络任务 = true;
                }
                任务.AddRange(zxtask);
            }
            else
            {
                var zxtask = getTaskServerStr();
                for (var i = 0; i < zxtask.Count; i++)
                {
                    zxtask[i].网络任务 = true;
                }
                任务.AddRange(zxtask);
            }
            
            /*TaskInfo[] TLIST = new TaskInfo[D_taskList.Count];
            D_taskList.CopyTo(TLIST);
            if (TLIST != null)
            {
                任务.AddRange(TLIST);
            }*/

            return 任务;
        }
        /// <summary>
        /// 调试本地活动任务
        /// </summary>
        /// <returns></returns>
        public bool debug_onlineTask()
        {
            isLocalOnlineTask = !isLocalOnlineTask;
            return isLocalOnlineTask;
        }
        internal List<TaskInfo> GetAllTask_AT() //取所有可领取任务
        {

            List<TaskInfo> 任务1 = GetTasks_PHR();
            List<TaskInfo> 新列表 = new List<TaskInfo>();

            var 任务 = GetAllTaskAim();
            /*TaskInfo[] TLIST = new TaskInfo[D_taskList.Count];
            D_taskList.CopyTo(TLIST);
            if (TLIST != null)
            {
                任务.AddRange(TLIST);
            }*/

            foreach (TaskInfo 信息 in 任务)
            {
                bool or = true;
                bool or1 = false;
                foreach (TaskInfo 玩家 in 任务1)
                {
                    if (!string.IsNullOrEmpty(信息.前置任务) && 信息.前置任务 != "-1")
                    {
                        if (信息.前置任务 == 玩家.任务序号 && 玩家.已完成 == "0")
                        {
                            or1 = true;
                        }
                    }

                    if (信息.任务序号 != 玩家.任务序号) continue;
                    or = false;
                    break;
                }

                if (!string.IsNullOrEmpty(信息.前置任务) && 信息.前置任务 != "-1" && or)
                {
                    or = or1;
                }

                if (TD) or = true;
                if (!or) continue;
                if (!信息.任务名.Contains("联网"))
                {
                    信息.任务目标 = null;
                    信息.任务奖励 = "";
                    信息.允许重复 = "";
                }

                新列表.Add(信息);
            }


            return 新列表;
        }

        private TaskInfo GetAppointedTaskAim(string 序号)
        {
            List<TaskInfo> 列表 = GetAllTaskAim();
            return 列表.FirstOrDefault(信息 => 信息.任务序号 == 序号);
        }

        public bool AddTaskAim(TaskInfo 任务, string 任务说明)
        {
            List<TaskInfo> 列表 = GetAllTaskAim();
            if (列表.Any(信息 => 信息.任务序号 == 任务.任务序号))
            {
                return false;
            }

            SaveFile(SkRC4.DES.EncryptRC4(任务说明, new DataProcess().GetKey(1)), pf + TDC_Path + @"/" + 任务.任务序号 + "_.dat");
            列表.Add(任务);
            SaveTaskAim(列表);
            return true;
        }

        private string GetTaskIntroduction(string 任务id)
        {
            try
            {
                if (!File.Exists(pf + TDC_Path + @"\" + 任务id + "_.dat"))
                {
                    return "该任务没有更多的线索提示";
                }
                string 存档 = ReadFile(pf + TDC_Path + @"\" + 任务id + "_.dat");
                存档 = SkRC4.DES.DecryptRC4(存档, new DataProcess().GetKey(1));
                return 存档;
            }
            catch
            {
                return "任务不存在";
            }
        }

        internal TaskPanel GetTaskPanelInfo(string 任务序号, bool 已接受)
        {
            var 信息 = 已接受 ? GetAppointedTask_HR(任务序号) : GetAppointedTaskAim(任务序号);
            if (信息?.任务序号 == null)
            {
                return new TaskPanel() { 任务介绍 = "没有获取到任务（如果该任务为活动任务，请检查网络）" };
            }
            if (信息.网络任务!=null && Convert.ToBoolean(信息.网络任务) && GetAppointedTaskAim(任务序号) == null)
            {
                return new TaskPanel() { 任务介绍 = "该任务为网络任务，无法在服务器中获得该任务，如果你确定该任务没下架，请检查网络问题", 位置 = true };
            }
            TaskPanel 面板 = new TaskPanel() { 位置 = 已接受, 任务名字 = 信息.任务名 };

            if (信息.任务介绍 == null)
            {
                面板.任务介绍 = GetTaskIntroduction(任务序号);
            }
            else
            {
                面板.任务介绍 = 信息.任务介绍;
            }

            string 奖励 = string.Empty;
            if (信息.任务名 == null)
            {
                Console.WriteLine("ID:"+信息.任务序号);
                return new TaskPanel() { 是否完成 = "1" };
            }
            string[] 分割 = 信息.任务奖励.Split('|');
            foreach (string i in 分割)
            {
                string[] 子分割 = i.Split(',');
                if (子分割.Length == 3)
                {
                    if (子分割[0].Equals("道具"))
                    {
                        奖励 += $"{GetAPType(子分割[1]).道具名字} {子分割[2]} 个<br/>";
                    }
                    else if (子分割[0].Equals("装备"))
                    {
                        奖励 += $"{GetAET(子分割[1]).名字} {子分割[2]} 件<br/>";
                    }
                }

                if (子分割.Length < 2) continue;
                if (子分割[0].Equals("金币"))
                {
                    奖励 += $"金币 {子分割[1]} 个<br/>";
                }
                else if (子分割[0].Equals("元宝"))
                {
                    奖励 += $"元宝 {子分割[1]} 个<br/>";
                }
                else if (子分割[0].Equals("水晶"))
                {
                    奖励 += $"水晶 {子分割[1]} 个<br/>";
                }
                else if (子分割[0].Equals("默认技能"))
                {
                    奖励 += "刷新宠物的默认技能(如若没有默认技能或者已经习得技能则不会获得技能)<br/>";
                }
                else if (子分割[0].Equals("卸下魂宠"))
                {
                    奖励 += "卸载已装备的魂宠<br/>";
                }
            }

            面板.任务奖励 = 奖励;
            string 目标 = "";
            List<task> tasks = 信息.任务目标;
            bool yj = false;
            try
            {
                foreach (task task in tasks)
                {
                    if (task.Type.Equals("等级"))
                    {
                        目标 += $"宠物等级达到 {task.Num} 级<br/>";
                    }
                    else if (task.Type.Equals("击杀"))
                    {
                        目标 += $"击杀 {Get_SMT(task.ID).怪物名字} {task.Num} 个<br/>";
                    }
                    else if (task.Type.Equals("收集"))
                    {
                        目标 += $"收集 {GetAPType(task.ID).道具名字} {task.Num} 个<br/>";
                    }
                    else if (task.Type.Equals("一键完成道具"))
                    {
                        目标 += $"一键完成需要收集： {GetAPType(task.ID).道具名字} {task.Num} 个<br/>";
                    }
                    else if (task.Type.Equals("金币"))
                    {
                        目标 += $"收集 金币 {task.Num} 个<br/>";
                    }
                    else if (task.Type.Equals("元宝"))
                    {
                        目标 += $"收集 元宝 {task.Num} 个<br/>";
                    }
                    else if (task.Type.Equals("水晶"))
                    {
                        目标 += $"收集 水晶 {task.Num} 个<br/>";
                    }
                    else if (task.Type.Equals("时之券"))
                    {
                        目标 += $"收集 时之券 {task.Num} 个<br/>";
                    }
                    else if (task.Type.Equals("威望"))
                    {
                        目标 += $"威望 达到 {task.Num} 点<br/>";
                    }
                    else if (task.Type.Equals("宠物"))
                    {
                        目标 += $"宠物 {GetAppointedPetType(task.ID).宠物名字} 达到 {task.Num} CC<br/>";
                    }
                    else if (task.Type.Equals("主宠达到成长"))
                    {
                        目标 += $"主宠物达到 {task.Num} CC<br/>";
                    }
                    else if (task.Type.Equals("扣除成长"))
                    {
                        目标 += $"宠物 {GetAppointedPetType(task.ID).宠物名字} 达到 {task.Num} CC（任务完成后保留10%）<br/>";
                    }
                    else if (task.Type.Equals("扣除成长2"))
                    {
                        目标 += $"宠物 {GetAppointedPetType(task.ID).宠物名字} 达到 {task.Num} CC（任务完成后保留1%）<br/>";
                    }
                    else if (task.Type.Equals("保留成长"))//数量=达到的成长，ID=保留的值
                    {
                        目标 += $"{GetAppointedPetType(信息.指定宠物).宠物名字} 达到 {task.Num} CC（任务完成后保留{task.ID}%）<br/>";
                    }
                    else if (task.Type.Equals("装备"))
                    {
                        目标 += $"收集 {GetAET(task.ID).名字} 1件<br/>";
                    }
                    else if (task.Type.Equals("时间"))
                    {
                        string time = DateTime.ParseExact(task.Num, "yyyyMMdd", null).ToShortDateString();//任务时间
                        目标 += $"任务必须在 {time} 之前完成(包含当天)<br/>";
                    }
                    else if (task.Type.Equals("地狱"))
                    {
                        目标 += $"地狱之门达到 {task.Num} 层<br/>";
                    }
                    else if (task.Type.Equals("通天"))
                    {
                        目标 += $"通天塔达到 {task.Num} 层<br/>";
                    }
                    else if (task.Type.Equals("VIP"))
                    {
                        目标 += $"VIP等级达到 {task.Num} 级<br/>";
                    }
                    else if (task.Type.Equals("至尊VIP"))
                    {
                        目标 += $"激活至尊VIP<br/>";
                    }
                    else if (task.Type.Equals("星辰VIP"))
                    {
                        目标 += $"激活星辰VIP<br/>";
                    }
                    else if (task.Type.Equals("积分"))
                    {
                        目标 += $"VIP积分达到 {task.Num} 分<br/>";
                    }
                    else if (task.Type.Equals("自动合宠经验"))
                    {
                        目标 += $"拥有自动合宠经验 {task.Num} 点<br/>";
                    }
                    else if (task.Type.Equals("自动合宠次数"))
                    {
                        目标 += $"拥有自动合宠次数 {task.Num} 次<br/>";
                    }
                    else if (task.Type.Equals("多个主宠"))
                    {
                        string[] petID = task.ID.Split(',');
                        string petName = "";
                        for (int i = 0; i < petID.Length; i++)
                        {
                            petName += GetAppointedPetType(petID[i]).宠物名字 + "、";
                        }
                        petName = petName.Remove(petName.Length - 1, 1);
                        目标 += $"需要主宠为：{petName} <br/>";
                    }
                }
            }
            catch (Exception)
            {
                ;
            }

            面板.任务目标 = 目标;
            string 进度 = "";
            UserInfo user = ReadUserInfo();
            TaskInfo 任务 = GetAppointedTask_HR(任务序号);
            int j = 0;
            int count = 0;
            foreach (task task in tasks)
            {

                if (task.Type.Equals("自动合宠经验"))
                {
                    进度 += $"已拥有 ({Convert.ToInt64(user.AutoExp)}/{task.Num})点自动合宠经验";
                    if (Convert.ToInt64(user.AutoExp) >= Convert.ToInt64(task.Num))
                    {
                        count++;
                        进度 += " 已完成";
                    }
                    else
                    {
                        进度 += " 未完成";
                    }

                    进度 += "<br/>";
                }
                else if (task.Type.Equals("自动合宠次数"))
                {
                    进度 += $"已拥有 ({Convert.ToInt32(user.AutoTime)}/{task.Num})次自动合宠次数";
                    if (Convert.ToInt32(user.AutoTime) >= Convert.ToInt32(task.Num))
                    {
                        count++;
                        进度 += " 已完成";
                    }
                    else
                    {
                        进度 += " 未完成";
                    }

                    进度 += "<br/>";
                }
                else if (task.Type.Equals("等级"))
                {
                    PetInfo pet = ReadAppointedPet(user.主宠物);
                    进度 += $"宠物等级达到 ({pet.等级}/{task.Num})级";
                    if (Convert.ToInt32(pet.等级) >= Convert.ToInt32(task.Num))
                    {
                        count++;
                        进度 += " 已完成";
                    }
                    else
                    {
                        进度 += " 未完成";
                    }

                    进度 += "<br/>";
                }
                else if (task.Type.Equals("击杀"))
                {
                    if (任务 == null)
                    {
                        进度 += $"已击杀 {Get_SMT(task.ID).怪物名字} (0/{task.Num})个";
                        进度 += " 未完成";
                    }
                    else
                    {
                        if (string.IsNullOrEmpty(任务.任务目标[j].inNum))
                        {
                            进度 += $"已击杀 {Get_SMT(task.ID).怪物名字} (0/{task.Num})个";
                        }
                        else
                        {
                            进度 += $"已击杀 {Get_SMT(task.ID).怪物名字} ({任务.任务目标[j].inNum}/{task.Num})个";
                        }

                        if (Convert.ToInt32(任务.任务目标[j].inNum) >= Convert.ToInt32(task.Num))
                        {
                            count++;
                            进度 += " 已完成";
                        }
                        else
                        {
                            进度 += " 未完成";
                        }
                    }

                    进度 += "<br/>";
                }
                else if (task.Type.Equals("收集"))
                {
                    PropInfo 道具 = GetAP_ID(task.ID) ?? new PropInfo() { 道具数量 = "0" };
                    进度 += $"已收集 {GetAPType(task.ID).道具名字} ({道具.道具数量}/{task.Num})个";
                    if (道具.道具位置 != "2" && Convert.ToInt32(道具.道具数量) >= Convert.ToInt32(task.Num))
                    {
                        count++;
                        进度 += " 已完成";
                    }
                    else
                    {
                        进度 += " 未完成";
                        if (道具.道具位置 == "2") 进度 += "（请把道具从仓库取出）";
                    }

                    进度 += "<br/>";
                }
                else if (task.Type.Equals("一键完成道具"))
                {
                    PropInfo 道具 = GetAP_ID(task.ID) ?? new PropInfo() { 道具数量 = "0" };
                    进度 += $"一键完成需要收集： {GetAPType(task.ID).道具名字} ({道具.道具数量}/{task.Num})个";
                    if (Convert.ToInt32(道具.道具数量) >= Convert.ToInt32(task.Num) && 道具.道具位置 == "1")
                    {
                        yj = true;
                        count = 9999;
                        进度 += " 已完成";
                    }
                    else
                    {
                        count++;//
                        进度 += " 未完成";
                    }

                    进度 += "<br/>";
                }
                else if (task.Type.Equals("金币"))
                {
                    进度 += $"已收集 金币 ({user.金币}/{task.Num})个";
                    if (Convert.ToInt64(user.金币) >= Convert.ToInt64(task.Num))
                    {
                        count++;
                        进度 += " 已完成";
                    }
                    else
                    {
                        进度 += " 未完成";
                    }

                    进度 += "<br/>";
                }
                else if (task.Type.Equals("元宝"))
                {
                    进度 += $"已收集 元宝 ({user.元宝}/{task.Num})个";
                    if (Convert.ToInt32(user.元宝) >= Convert.ToInt32(task.Num))
                    {
                        count++;
                        进度 += " 已完成";
                    }
                    else
                    {
                        进度 += " 未完成";
                    }

                    进度 += "<br/>";
                }
                else if (task.Type.Equals("水晶"))
                {
                    进度 += $"已收集 水晶 ({user.水晶}/{task.Num})个";
                    if (Convert.ToInt32(user.水晶) >= Convert.ToInt32(task.Num))
                    {
                        count++;
                        进度 += " 已完成";
                    }
                    else
                    {
                        进度 += " 未完成";
                    }

                    进度 += "<br/>";
                }
                else if (task.Type.Equals("时之券"))
                {
                    进度 += $"已收集 时之券 ({user.时之券}/{task.Num})个";
                    if (Convert.ToInt32(user.时之券) >= Convert.ToInt32(task.Num))
                    {
                        count++;
                        进度 += " 已完成";
                    }
                    else
                    {
                        进度 += " 未完成";
                    }

                    进度 += "<br/>";
                }
                else if (task.Type.Equals("威望"))
                {
                    进度 += $"威望 达到 ({user.威望}/{task.Num})点";
                    if (Convert.ToInt32(user.威望) >= Convert.ToInt32(task.Num))
                    {
                        count++;
                        进度 += " 已完成";
                    }
                    else
                    {
                        进度 += " 未完成";
                    }

                    进度 += "<br/>";
                }
                else if (task.Type.Equals("宠物"))
                {
                    PetInfo 宠物 = ReadAppointedPet(user.主宠物);
                    PetConfig cw = GetAppointedPetType(宠物.形象);

                    进度 += $"宠物 {GetAppointedPetType(task.ID).宠物名字} 达到 {task.Num} CC";

                    if (cw.宠物序号 != task.ID)
                    {
                        进度 += " 未完成";
                    }
                    else
                    {
                        if (Convert.ToDouble(宠物.成长) >= Convert.ToDouble(task.Num))
                        {
                            count++;
                            进度 += " 已完成";
                        }
                        else
                        {
                            进度 += " 未完成";
                        }
                    }

                    进度 += "<br/>";
                }
                else if (task.Type.Equals("主宠达到成长"))
                {
                    PetInfo 宠物 = ReadAppointedPet(user.主宠物);
                    if (信息.指定宠物 == "" || 信息.指定宠物 == null)
                    {
                        进度 += $"主宠达到 {task.Num} CC";
                        if (Convert.ToDouble(宠物.成长) < Convert.ToDouble(task.Num))
                        {
                            进度 += " 未完成";
                        }
                        else
                        {
                            if (Convert.ToDouble(宠物.成长) >= Convert.ToDouble(task.Num))
                            {
                                count++;
                                进度 += " 已完成";
                            }
                            else
                            {
                                进度 += " 未完成";
                            }
                        }
                    }
                    else
                    {
                        string Tmsg = "";
                        foreach (var nt in 信息.任务目标)
                        {
                            if (nt.Type.Equals("隐藏主宠提示"))
                            {
                                Tmsg = $"指定宠物 达到 {task.Num} CC";
                                count++;
                                break;
                            }
                            else
                            {
                                Tmsg = $"宠物 {GetAppointedPetType(信息.指定宠物).宠物名字} 达到 {task.Num} CC";
                            }
                        }
                        进度 += Tmsg;
                        if (宠物.形象 != 信息.指定宠物)
                        {
                            进度 += " 未完成";
                        }
                        else
                        {
                            if (Convert.ToDouble(宠物.成长) >= Convert.ToDouble(task.Num))
                            {
                                count++;
                                进度 += " 已完成";
                            }
                            else
                            {
                                进度 += " 未完成";
                            }
                        }
                    }


                    进度 += "<br/>";
                }
                else if (task.Type.Equals("扣除成长"))
                {
                    PetInfo 宠物 = ReadAppointedPet(user.主宠物);
                    进度 += $"宠物 {GetAppointedPetType(task.ID).宠物名字} 达到 {task.Num} CC";

                    if (宠物.形象 != task.ID)
                    {
                        进度 += " 未完成";
                    }
                    else
                    {
                        if (Convert.ToDouble(宠物.成长) >= Convert.ToDouble(task.Num))
                        {
                            count++;
                            进度 += " 已完成";
                        }
                        else
                        {
                            进度 += " 未完成";
                        }
                    }

                    进度 += "<br/>";
                }
                else if (task.Type.Equals("扣除成长2"))
                {
                    PetInfo 宠物 = ReadAppointedPet(user.主宠物);

                    进度 += $"宠物 {GetAppointedPetType(task.ID).宠物名字} 达到 {task.Num} CC";

                    if (宠物.形象 != task.ID)
                    {
                        进度 += " 未完成";
                    }
                    else
                    {
                        if (Convert.ToDouble(宠物.成长) >= Convert.ToDouble(task.Num))
                        {
                            count++;
                            进度 += " 已完成";
                        }
                        else
                        {
                            进度 += " 未完成";
                        }
                    }

                    进度 += "<br/>";
                }
                else if (task.Type.Equals("保留成长"))//数量=达到的成长，ID=保留的值 - 这个必须指定主宠物
                {
                    PetInfo 宠物 = ReadAppointedPet(user.主宠物);
                    PetConfig cw = GetAppointedPetType(宠物.形象);
                    进度 += $"宠物 {GetAppointedPetType(信息.指定宠物).宠物名字} ({宠物.成长} CC / {task.Num} CC)";
                    if (宠物.形象 != 信息.指定宠物)
                    {
                        进度 += " 未完成";
                    }
                    else
                    {
                        //进度 = 进度.Replace("[宠物成长]",宠物.成长);
                        if (Convert.ToDouble(宠物.成长) >= Convert.ToDouble(task.Num))
                        {
                            count++;
                            进度 += " 已完成";
                        }
                        else
                        {
                            进度 += " 未完成";
                        }
                    }

                    进度 += "<br/>";
                }
                else if (task.Type.Equals("多个主宠"))//用,来区分多只主宠
                {
                    string[] petID = task.ID.Split(',');
                    string petName = "";
                    for (int i = 0; i < petID.Length; i++)
                    {
                        petName += GetAppointedPetType(petID[i]).宠物名字 + "、";
                    }
                    petName = petName.Remove(petName.Length - 1, 1);
                    UserInfo 用户 = ReadUserInfo();
                    PetInfo 宠物 = ReadAppointedPet(user.主宠物);
                    int Z_pet = Array.IndexOf(petID, 宠物.形象);//Z_pet=-1时则未完成
                    进度 += $"需要主宠为：{petName} ";
                    if (Z_pet == -1)
                    {
                        进度 += " 未完成";
                    }
                    else
                    {
                        count++;
                        进度 += " 已完成";
                    }
                }
                else if (task.Type.Equals("地狱"))
                {
                    进度 += $"地狱之门达到 {task.Num} 层";
                    if (Convert.ToInt64(user.地狱层数) / 10 + 1 <= Convert.ToInt64(task.Num))
                    {
                        进度 += " 未完成";
                    }
                    else
                    {
                        count++;
                        进度 += " 已完成";
                    }

                    进度 += "<br/>";
                }
                else if (task.Type.Equals("通天"))
                {
                    进度 += $"通天塔达到 {task.Num} 层";
                    if (Convert.ToInt64(user.TTT) < Convert.ToInt64(task.Num))
                    {
                        进度 += " 未完成";
                    }
                    else
                    {
                        count++;
                        进度 += " 已完成";
                    }

                    进度 += "<br/>";
                }
                else if (task.Type.Equals("装备"))
                {
                    List<EquipmentInfo> 装备列表 = GetUnusedEquipment();
                    string n = "0";
                    if (装备列表.Any(zz => zz.类ID == task.ID))
                    {
                        n = "1";
                    }

                    进度 += $"已收集 {GetAET(task.ID).名字} ({n}/1)件";
                    if (n.Equals("0"))
                    {
                        进度 += "未完成";
                    }
                    else
                    {
                        count++;
                        进度 += "已完成";
                    }

                    进度 += "<br/>";
                }
                else if (task.Type.Equals("时间"))
                {
                    int now = Tools.GetTime.GetNtp();
                    int aim = Convert.ToInt32(task.Num);
                    string time = DateTime.ParseExact(task.Num, "yyyyMMdd", null).ToShortDateString();
                    if (now == -1)
                    {
                        进度 += "获取网络时间失败，请检查网络<br/>";
                    }
                    else
                    {
                        if (now <= aim)
                        {
                            count++;
                            进度 += $"任务时限 {time} 已完成";
                        }
                        else
                        {
                            进度 += $"任务时限 {time} 已超过";
                        }
                    }

                    进度 += "<br/>";
                }
                else if (task.Type.Equals("VIP"))
                {
                    进度 += $"VIP等级达到 ({user.vip}/{task.Num})级";
                    if (Convert.ToInt16(user.vip) >= Convert.ToInt16(task.Num))
                    {
                        count++;
                        进度 += " 已完成";
                    }
                    else
                    {
                        进度 += " 未完成";
                    }

                    进度 += "<br/>";
                }
                else if (task.Type.Equals("至尊VIP"))
                {
                    string 激活状态 = "否";
                    if (user.至尊VIP) 激活状态 = "是";
                    进度 += $"激活至尊VIP ({激活状态}/是)";
                    if (user.至尊VIP)
                    {
                        count++;
                        进度 += " 已完成";
                    }
                    else
                    {
                        进度 += " 未完成";
                    }

                    进度 += "<br/>";
                }
                else if (task.Type.Equals("星辰VIP"))
                {
                    string 激活状态 = "否";
                    if (user.星辰VIP) 激活状态 = "是";
                    进度 += $"激活星辰VIP ({激活状态}/是)";
                    if (user.星辰VIP)
                    {
                        count++;
                        进度 += " 已完成";
                    }
                    else
                    {
                        进度 += " 未完成";
                    }

                    进度 += "<br/>";
                }
                else if (task.Type.Equals("积分"))
                {
                    进度 += $"VIP积分达到 ({user.VIP积分}/{task.Num})分";
                    if (Convert.ToInt32(user.VIP积分) >= Convert.ToInt32(task.Num))
                    {
                        count++;
                        进度 += " 已完成";
                    }
                    else
                    {
                        进度 += " 未完成";
                    }

                    进度 += "<br/>";
                }

                j++;
            }
            if (信息.前置任务 != null && 信息.前置任务 != "")
            {
                var tlist = GetAllTaskAim();
                var t = tlist.FirstOrDefault(C => C.任务序号 == 信息.前置任务);

                目标 = "需要完成任务 " + t.任务名 + "<br/>" + 目标;

                进度 = "需要完成任务 " + t.任务名 + "<br/>" + 进度;
            }
            if (!string.IsNullOrEmpty(信息.指定宠物) && !信息.指定宠物.Equals("-1"))//指定宠物不为空
            {
                PetConfig 指定宠物 = GetAppointedPetType(信息.指定宠物);
                bool 宠物完成 = false;
                string Tmsg = "", Tmsg1 = "";
                foreach (var nt in 信息.任务目标)
                {
                    if (nt.Type.Equals("隐藏主宠提示"))
                    {
                        Tmsg = "主宠物已被隐藏 ";
                        Tmsg1 = "主宠物已被隐藏,请在任务介绍中获取线索吧! ";
                        break;
                    }
                    else
                    {
                        Tmsg = "主宠物需要为 " + 指定宠物.宠物名字;
                        Tmsg1 = "主宠物需要为 " + 指定宠物.宠物名字;
                    }
                }

                进度 += Tmsg;
                目标 += Tmsg1;
                面板.任务目标 = 目标;
                PetInfo 宠物 = ReadAppointedPet(user.主宠物);
                if (宠物.形象 == 指定宠物.宠物序号)
                {
                    宠物完成 = true;
                    进度 += " 已完成";
                }
                else
                {
                    进度 += " 未完成";
                }

                进度 += "<br/>";
                if (count >= tasks.Count && 宠物完成)
                {
                    面板.是否完成 = "0";
                }
                else
                {
                    面板.是否完成 = "1";
                }
            }
            else
            {
                面板.是否完成 = count >= tasks.Count ? "0" : "1";
            }
            if (信息.任务名.Contains("【活动】"))
            {
                if (BanTask == null || BanTask.Length == 0)
                {
                    面板.任务介绍 += "<br><span style='color:red'>因网络问题，任务无法完成，请重启游戏后再试。</span>";
                }
            }
            if (BanTask != null && BanTask.Length != 0)
            {
                if (BanTask.Contains(信息.任务序号))
                {
                    面板.任务介绍 += "<br><span style='color:red'>该任务已经过了时效，无法完成。</span>";
                }
            }
            if (count >= 999)
            {

                面板.任务介绍 += "<br><span style='color:red'>因为您拥有该任务的一键完成道具，该任务可直接完成</span>";

            }
            面板.一键完成 = yj;
            面板.任务进度 = 进度;
            return 面板;
        }

        private string FulfilTaskProcess(string 用户属性, string 数量, bool 加)
        {
            if (加)
            {
                return (Convert.ToInt64(用户属性) + Convert.ToInt64(数量)).ToString();
            }

            return (Convert.ToInt64(用户属性) - Convert.ToInt64(数量)).ToString();
        }

        internal bool FulfilTask(string 任务序号)//任务奖励脚本
        {

            var RESULT = GetTaskPanelInfo(任务序号, true);
            if (RESULT.是否完成 != "0") return false;
            var info = GetAppointedTask_HR(任务序号);
            if (info?.任务序号 == null)
            {
                return false;
            }
            if (info.任务名.Contains("【活动】"))
            {
                if (BanTask == null || BanTask.Length == 0)
                {
                    return false;
                }
            }
            if (BanTask != null && BanTask.Length != 0)
            {
                if (BanTask.Contains(info.任务序号))
                {
                    return false;
                }
            }
            TaskPanel 面板 = new TaskPanel() { 位置 = true, 任务名字 = info.任务名, 任务介绍 = GetTaskIntroduction(任务序号) };
            string 奖励 = "";
            string[] 分割 = info.任务奖励.Split('|');
            foreach (string i in 分割)
            {
                string[] 子分割 = i.Split(',');
                if (子分割.Length == 3)
                {
                    if (子分割[0].Equals("道具"))
                    {
                        PropInfo 信息 = new PropInfo()
                        {
                            道具类型ID = 子分割[1],
                            道具位置 = PropLoaction.背包.ToString(),
                            道具数量 = 子分割[2]
                        };
                        new DataProcess().AddPlayerProp(信息);
                    }
                    else if (子分割[0].Equals("装备"))
                    {
                        EquipmentInfo 装备 = new EquipmentInfo() { ID = "0", 类ID = 子分割[1], 强化 = "0", WX = "无" };
                        new DataProcess().AddPlayerEquipment(装备);
                    }
                }

                if (子分割.Length >= 2)
                {
                    if (子分割[0].Equals("金币"))
                    {
                        UserInfo user = ReadUserInfo();
                        user.金币 = FulfilTaskProcess(user.金币, 子分割[1], true);
                        SaveUserDataFile(user);
                    }
                    else if (子分割[0].Equals("元宝"))
                    {
                        UserInfo user = ReadUserInfo();
                        user.元宝 = FulfilTaskProcess(user.元宝, 子分割[1], true);
                        SaveUserDataFile(user);
                    }
                    else if (子分割[0].Equals("水晶"))
                    {
                        UserInfo user = ReadUserInfo();
                        user.水晶 = FulfilTaskProcess(user.水晶, 子分割[1], true);
                        SaveUserDataFile(user);
                    }
                    else if (子分割[0].Equals("经验"))
                    {
                        RunPropScript("宠物当前经验|" + 子分割[1], out _);
                    }
                    else if (子分割[0].Equals("默认技能"))
                    {
                        UserInfo user = ReadUserInfo();
                        StudyDefaultSkill(user.主宠物);
                    }
                    else if (子分割[0].Equals("卸下魂宠"))
                    {
                        UserInfo user = ReadUserInfo();
                        user.魂宠 = null;
                        SaveUserDataFile(user);
                    }
                }
            }

            面板.任务奖励 = 奖励;
            string 目标 = "";
            List<task> tasks = info.任务目标;
            if (!RESULT.一键完成)
            {
                foreach (task task in tasks)
                {
                    if (task.Type.Equals("收集"))
                    {
                        ReducePropNum_ID(task.ID, Convert.ToInt32(task.Num));
                    }
                    if (task.Type.Equals("一键完成道具"))
                    {
                        //ReducePropNum_ID(task.ID, Convert.ToInt32(task.Num));
                    }
                    else if (task.Type.Equals("金币"))
                    {
                        UserInfo 用户 = ReadUserInfo();
                        用户.金币 = FulfilTaskProcess(用户.金币, task.Num, false);
                        SaveUserDataFile(用户);
                    }
                    else if (task.Type.Equals("元宝"))
                    {
                        UserInfo 用户 = ReadUserInfo();
                        用户.元宝 = FulfilTaskProcess(用户.元宝, task.Num, false);
                        SaveUserDataFile(用户);
                    }
                    else if (task.Type.Equals("水晶"))
                    {
                        UserInfo 用户 = ReadUserInfo();
                        用户.水晶 = FulfilTaskProcess(用户.水晶, task.Num, false);
                        SaveUserDataFile(用户);
                    }
                    else if (task.Type.Equals("宠物"))
                    {
                        UserInfo 用户 = ReadUserInfo();
                        PetInfo 宠物 = ReadAppointedPet(用户.主宠物);
                        宠物.成长 = (1 * Convert.ToDouble(宠物.成长)).ToString(CultureInfo.InvariantCulture);
                        Update_APDF(宠物.宠物序号, 宠物);
                    }
                    else if (task.Type.Equals("扣除成长"))
                    {
                        UserInfo 用户 = ReadUserInfo();
                        PetInfo 宠物 = ReadAppointedPet(用户.主宠物);
                        宠物.成长 = (0.1 * Convert.ToDouble(宠物.成长)).ToString(CultureInfo.InvariantCulture);
                        Update_APDF(宠物.宠物序号, 宠物);
                    }
                    else if (task.Type.Equals("扣除成长2"))
                    {
                        UserInfo 用户 = ReadUserInfo();
                        PetInfo 宠物 = ReadAppointedPet(用户.主宠物);
                        宠物.成长 = (0.01 * Convert.ToDouble(宠物.成长)).ToString(CultureInfo.InvariantCulture);
                        Update_APDF(宠物.宠物序号, 宠物);
                    }
                    else if (task.Type.Equals("保留成长"))
                    {
                        UserInfo 用户 = ReadUserInfo();
                        PetInfo 宠物 = ReadAppointedPet(用户.主宠物);
                        宠物.成长 = ((Convert.ToInt32(task.Num) / 100) * Convert.ToDouble(宠物.成长)).ToString(CultureInfo.InvariantCulture);
                        Update_APDF(宠物.宠物序号, 宠物);
                    }
                    else if (task.Type.Equals("装备"))
                    {
                        List<EquipmentInfo> 装备列表 = GetUnusedEquipment();
                        foreach (EquipmentInfo zz in 装备列表)
                        {
                            if (zz.类ID != task.ID) continue;
                            DeleteEquipment(zz.ID);
                            break;
                        }
                    }
                    else if (task.Type.Equals("积分"))
                    {
                        UserInfo 用户 = ReadUserInfo();
                        用户.VIP积分 = FulfilTaskProcess(用户.VIP积分, task.Num, false);
                        SaveUserDataFile(用户);
                    }
                    else if (task.Type.Equals("时之券"))
                    {
                        UserInfo 用户 = ReadUserInfo();
                        用户.时之券 = FulfilTaskProcess(用户.时之券, task.Num, false);
                        SaveUserDataFile(用户);
                    }
                    else if (task.Type.Equals("威望"))
                    {
                        UserInfo 用户 = ReadUserInfo();
                        用户.威望 = FulfilTaskProcess(用户.威望, task.Num, false);
                        SaveUserDataFile(用户);
                    }
                    else if (task.Type.Equals("自动合宠次数"))
                    {
                        UserInfo 用户 = ReadUserInfo();
                        用户.AutoTime = FulfilTaskProcess(用户.AutoTime, task.Num, false);
                        SaveUserDataFile(用户);
                    }
                }
            }
            else
            {
                foreach (task task in tasks)
                {
                    if (task.Type.Equals("一键完成道具"))
                    {
                        ReducePropNum_ID(task.ID, Convert.ToInt32(task.Num));
                    }
                }
            }
            面板.任务目标 = 目标;

            //  String 进度 = "";
            info.已完成 = "0";
            info.任务目标 = new List<task>();
            info.任务奖励 = "";
            if (info.允许重复 == "1")
            {
                AbortTask(任务序号);
            }
            else
            {
                ChangeAppointedTask(任务序号, info);
            }

            return true;
        }

        internal bool RefreshTask()
        {
            List<TaskInfo> 列表 = GetTasks_PHR();
            List<TaskInfo> 新列表 = new List<TaskInfo>();
            foreach (TaskInfo 信息 in 列表)
            {
                if (string.IsNullOrEmpty(信息.任务奖励) && 信息.已完成 == "0" && 信息.允许重复 == "1")
                {
                }
                else
                {
                    新列表.Add(信息);
                }
            }

            return SaveTask_HR_List(新列表);
        }

        private void SaveTaskAim(List<TaskInfo> tasklist)
        {
            string cfg = JsonConvert.SerializeObject(tasklist);
            cfg = SkRC4.DES.EncryptRC4(cfg, new DataProcess().GetKey(1));
            SaveFile(cfg, pf + TDC_Path + @"\_0.task");
        }

        internal string GetTaskName(string taskid)
        {
            foreach (TaskInfo task in Task_TaskHelper)
            {
                if (task.任务序号.Equals(taskid))
                {
                    return task.任务名;
                }
            }

            return null;
        }

        /*public void DeleteTaskAim(string 任务id)
        {
            List<任务信息> 列表 = GetAllTaskAim();
            List<任务信息> 新列表 = 列表.Where(信息 => 信息.任务序号 != 任务id).ToList();
            SaveTaskAim(新列表);
        }*/
        internal bool ReceiveTask(string taskid)
        {
            TaskInfo 定义 = GetAppointedTaskAim(taskid);
            return 定义 != null && AddTask_HR(定义);
        }

        private bool AddTask_HR(TaskInfo 任务) //增加领取的任务
        {
            if (任务.任务名.Contains("【活动】"))
            {
                if (BanTask == null || BanTask.Length == 0)
                {
                    return false;
                }
            }
            if (BanTask != null && BanTask.Length != 0)
            {
                if (BanTask.Contains(任务.任务序号))
                {
                    return false;
                }
            }
            List<TaskInfo> 列表 = GetTasks_PHR();
            if (列表.Any(信息 => 信息.任务序号 == 任务.任务序号))
            {
                return false;
            }
            var 新增 = JsonConvert.DeserializeObject<TaskInfo>(JsonConvert.SerializeObject(任务));
            新增.已完成 = "1";
            列表.Add(新增);
            return SaveTask_HR_List(列表);
        }

        internal bool AbortTask(string 任务序号)
        {
            List<TaskInfo> 列表 = GetTasks_PHR();
            List<TaskInfo> 新列表 = 列表.Where(信息 => 信息.任务序号 != 任务序号).ToList();
            return SaveTask_HR_List(新列表);
        }

        private bool ChangeAppointedTask(string 任务序号, TaskInfo 任务)
        {
            List<TaskInfo> 列表 = GetTasks_PHR();
            List<TaskInfo> 新列表 = 列表.Select(信息 => 信息.任务序号 != 任务序号 ? 信息 : 任务).ToList();
            return SaveTask_HR_List(新列表);
        }

        internal bool SaveTask_HR_List(List<TaskInfo> 任务) //保存已领任务列表
        {

            for (int i = 0;i<任务.Count;i++) {
                if (任务[i].已完成 == "0")
                {
                    //任务已经完成的清空下，不必要的东西一律不保存
                    任务[i].任务目标 = null;
                    任务[i].指定宠物 = null;
                    任务[i].任务奖励 = null;
                    任务[i].前置任务 = null;
                    任务[i].任务介绍 = null;
                    任务[i].网络任务 = null;
                    任务[i].任务名 = null;
                    任务[i].已完成 = null;
                }
                else if (任务[i].已完成 == null) {
                    任务[i].已完成 = "1";
                }
            }
            string 存档 = JsonConvert.SerializeObject(任务, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            //Tasks_HR = 任务;
            存档 = SkRC4.DES.EncryptRC4(存档, new DataProcess().GetKey(1, true));
            string 拼接 = GetStr();
            string[] 存档组 = 拼接.Split(new[] { "O4F89" }, StringSplitOptions.None);
            while (存档组.Length < 7)
            {
                存档组 = (拼接 + "O4F89").Split(new[] { "O4F89" }, StringSplitOptions.None);
            }

            存档组[6] = 存档;
            if (AdminMode == 0)
            {
                JointDataFile(存档组, true);
            }
            else
            {
                存档 = JointDataFile(存档组, true);
                SaveFile(存档, pf + PF_Path);
            }

            return true;
        }
        public static string taskServerStr = "";//在线任务的字符串
        internal List<TaskInfo> getTaskServerStr()
        {
            if (taskServerStr == null || taskServerStr == "") return new List<TaskInfo>();
            string json = SkRC4.DES.DecryptRC4(taskServerStr, new DataProcess().GetKey(1));
            return JsonConvert.DeserializeObject<List<TaskInfo>>(json);
        }
        //private static List<任务信息> Tasks_HR; //已领任务
        internal List<TaskInfo> GetTasks_PHR() //取玩家已领的任务
        {
            /*if (Tasks_HR != null && z)
            {
                return Tasks_HR;
            }*/
            string 存档 = GetStr();
            if (存档 == null)
            {
                new DataProcess().ThrowError(1);
            }

            // ReSharper disable once PossibleNullReferenceException
            string[] 存档组 = 存档.Split(new[] { "O4F89" }, StringSplitOptions.None);
            存档 = 存档组[6];
            if (!string.IsNullOrEmpty(存档))
            {
                存档 = SkRC4.DES.DecryptRC4(存档, new DataProcess().GetKey(1, true) + GetKey1(6)); //这里调用了RC4解密,存档我们是用的RC4进行加密的

                try
                {
                    var 进度 = JsonConvert.DeserializeObject<List<TaskInfo>>(存档);
                    //Tasks_HR = 进度;
                    return 进度;
                }
                catch
                {
                    MessageBox.Show("任务存档已经损坏，请重新更换存档。");
                    //File.Delete("Pagemain/Main.dat");
                    Tools.ForcedExit("存档版本过低");
                    Application.Exit();
                    return new List<TaskInfo>();
                }
            }

            return new List<TaskInfo>();
        }

        private TaskInfo GetAppointedTask_HR(string 序号) // 取指定已领任务
        {
            List<TaskInfo> 列表 = GetTasks_PHR();
            return 列表.FirstOrDefault(信息 => 信息.任务序号 == 序号);
        }

        public List<suits> GetAllSuits()
        {
            DirectoryInfo dir = new DirectoryInfo(Path.Combine(ProgramPath, SSDC_Path));
            //path为某个目录，如： “D:\Program Files”
            FileInfo[] inf = dir.GetFiles();
            //List<文件验证> 文件 = new List<文件验证>();
            return (from finf in inf
                    where finf.Extension.Equals(".dat")
                    select new SkFileCheck.FileCheck() { 特征 = SkCryptography.GetHash.GetFileHash(finf.FullName), 文件名 = pf + SSDC_Path + finf.Name }
                into 验证
                    select SkRC4.DES.DecryptRC4(ReadFile(pf + 验证.文件名), new DataProcess().GetKey(1))
                into json
                    select JsonConvert.DeserializeObject<suits>(json)).ToList();
        }

        internal suits GetAppointedSuit(string suitId)
        {
            suits suit = new suits();
            if (string.IsNullOrEmpty(suitId))
            {
                return new suits();
            }

            if (!File.Exists(pf + SSDC_Path + SkCryptography.GetHash.GetStringHash(SkRC4.DES.EncryptRC4(suitId, new DataProcess().GetKey(2))) + ".dat"))
            {
                return suit;
            }

            string json =
                SkRC4.DES.DecryptRC4(
                    ReadFile(pf + SSDC_Path + SkCryptography.GetHash.GetStringHash(SkRC4.DES.EncryptRC4(suitId, new DataProcess().GetKey(2))) + ".dat"),
                    new DataProcess().GetKey(1));
            if (json != null)
            {
                suit = JsonConvert.DeserializeObject<suits>(json);
            }

            return suit;
        }

        public bool SaveSuit(suits suit)
        {
            if (File.Exists(
                pf + SSDC_Path + SkCryptography.GetHash.GetStringHash(SkRC4.DES.EncryptRC4(suit.套装序号, new DataProcess().GetKey(2))) + ".dat"))
            {
                return false;
            }

            SaveFile(SkRC4.DES.EncryptRC4(jsonTo.EntityToJson(suit), new DataProcess().GetKey(1)),
                pf + SSDC_Path + SkCryptography.GetHash.GetStringHash(SkRC4.DES.EncryptRC4(suit.套装序号, new DataProcess().GetKey(2))) + ".dat");
            return true;
        }

        private static List<SkillConfig> SkillList = new List<SkillConfig>();

        private List<SkillConfig> GetSCList()
        {
            string dat = SkRC4.DES.DecryptRC4(ReadFile(SDC_Path), new DataProcess().GetKey(1));
            List<SkillConfig> cfg = new List<SkillConfig>();
            if (dat != null)
            {
                string[] dats = dat.Split(new[] { "\r\n" }, StringSplitOptions.None);
                foreach (string d in dats)
                {
                    string[] datss = d.Split('，');
                    if (datss.Length < 5) continue;
                    SkillConfig 技能 = new SkillConfig()
                    {
                        技能ID = datss[0],
                        技能名字 = datss[1],
                        技能百分比 = datss[2],
                        技能附带效果 = datss[3],
                        附带效果增量 = datss[4],
                        耗蓝量 = datss[5],

                    };
                    if (datss.Length >= 7) 技能.限制五行 = datss[6];
                    技能.BUFF = 技能.技能附带效果 != "null" ? "true" : "false";
                    cfg.Add(技能);
                    /*else
                    {
                        //Console.Write("配置出错,内容为:" + d);
                    }*/
                }
            }

            SkillList = cfg;
            return cfg;
        }
        internal SkillConfig GetASC(string 技能id) //取指定技能配置
        {
            if (SkillList == null || SkillList.Count == 0)
            {
                GetSCList();
            }

            return SkillList.FirstOrDefault(技能 => 技能.技能ID.Equals(技能id));
        }
        internal List<SkillConfig> 获取技能配置() //取技能配置
        {
            if (SkillList == null || SkillList.Count == 0)
            {
                GetSCList();
            }

            return SkillList;
        }

        private bool StudyDefaultSkill(string 宠物id)
        {
            PetInfo 信息 = ReadAppointedPet(宠物id);
            PetConfig 类型 = GetAppointedPetType(信息.形象);
            if (信息.技能列表.Equals("0"))
            {
                信息.技能列表 = "";
            }

            foreach (string s in 类型.默认技能)
            {
                if (s.Length == 0)
                {
                    continue;
                }

                bool or = 信息.信息.All(技能信息 => !技能信息.技能序号.Equals(s));
                if (or)
                {
                    信息.技能列表 += ",|" + s + "|0";
                }
                else
                {
                    return false;
                }
            }

            Update_APDF(信息.宠物序号, 信息);
            return true;
        }

        internal int DeleteJN(string petid, string skillid)
        {
            PetInfo petInfo = ReadAppointedPet(petid);
            if (petInfo != null)
            {
                string[] allskills = petInfo.技能列表.Split(',');
                string newcfg = string.Empty;
                foreach (string skill in allskills)
                {
                    if (!string.IsNullOrEmpty(skill))
                    {
                        string[] cfg = skill.Split('|');
                        if (!cfg[1].Equals(skillid))
                        {
                            newcfg += ",|" + cfg[1] + "|" + cfg[2];
                        }
                    }
                }

                petInfo.技能列表 = newcfg;
                //Console.WriteLine(newcfg);
                Update_APDF(petInfo.宠物序号, petInfo);
                return 1;
            }
            else
            {
                return 2;
            }
        }

        private string StudySkill(string 宠物id, string 技能id)
        {
            PetInfo petInfo = ReadAppointedPet(宠物id);
            List<SkillInfo> skillInfo = petInfo.信息;

            if (skillInfo.Any(技能信息 => 技能信息.信息 != null && 技能信息.信息.技能ID == 技能id))
            {
                return "宠物已经掌握此技能了！";
            }

            if (GetASC(技能id) == null)
            {
                return "游戏内部错误！";
            }
            var sInfo = GetASC(技能id);
            if (sInfo.限制五行 != null && sInfo.限制五行 != "" && petInfo.五行 != sInfo.限制五行)
            {
                return "该技能只能" + sInfo.限制五行 + "系学习。";
            }
            if (petInfo.五行 == "巫" && sInfo.限制五行 != "巫")
            {
                return "巫系宠物只能学习巫系专属技能！";
            }
            int skillCount_zd = 0;
            int skillCount_bd = 0;
            foreach (SkillInfo info in skillInfo)
            {
                if (info.信息.技能附带效果 == "null" && GetASC(技能id).技能附带效果 == "null")
                {
                    return "宠物只能学习一个主动技能！";
                }
                else
                {
                    skillCount_bd++;
                }
            }

            if (skillCount_bd >= 15)
            {
                // 已经有14个被动了
                return "宠物技能已满，学习失败";
            }
            petInfo.技能列表 += ",|" + 技能id + "|0";
            Update_APDF(petInfo.宠物序号, petInfo);
            return "成功";
        }

        private int UpgradeSkillProcess(string 宠物id, string 技能id)
        {
            PetInfo 宠物 = ReadAppointedPet(宠物id);
            string 技能配置 = 宠物.技能列表;
            string[] 技能配置数组 = 技能配置.Split(',');
            //,|7|0,|8|0
            string 技能配置串 = "";
            int returns = -1;
            foreach (string str in 技能配置数组)
            {
                string[] s = str.Split('|');
                if (s.Length >= 2)
                {
                    if (s[1] == 技能id)
                    {
                        s[2] = (Convert.ToInt32(s[2]) + 1).ToString();
                        returns = Convert.ToInt32(s[2]);
                        if (returns > 18)
                        {
                            return -2;
                        }
                    }

                    技能配置串 += ",|" + s[1] + "|" + s[2];
                }
            }

            if (returns != -1)
            {
                宠物.技能列表 = 技能配置串;
                Update_APDF(宠物id, 宠物);
            }

            return returns;
        }

        internal int UpgradeSkill(string petId, string sid, string sType)
        {
            var prop = GetAP_ID(sType.Equals("BUFF") ? "2022030409" : "2022030408");
            PetInfo 宠物 = ReadAppointedPet(petId);
            if (宠物.五行 == "巫")
            {
                prop = GetAP_ID(sType.Equals("BUFF") ? "2022030409" : "2022030408");
                if (prop == null)
                {
                    return -1;
                }
            }
            else
            {
                prop = GetAP_ID(sType.Equals("BUFF") ? "2017021601" : "2017021602");
                if (prop == null)
                {
                    return -1;
                }
            }
            
            
            int r = UpgradeSkillProcess(petId, sid);
            if (r < 0)
            {
                return r;
            }

            new DataProcess().ReviseOrDeletePP(prop, 1);

            return r;
        }


        private static object _locker = new object();

        internal static void ReceiveDailyGift()//每日礼包
        {
            lock (_locker)
            {
                int nts = Tools.GetTime.GetNtp();
                if (nts != -1)
                {
                    UserInfo user = new DataProcess().ReadUserInfo();
                    if (string.IsNullOrEmpty(user.每日礼包时间) || nts - Convert.ToDouble(user.每日礼包时间) >= 1)
                    {
                        user.每日礼包时间 = nts.ToString();
                        PropInfo info = new PropInfo
                        {
                            道具类型ID = GetInt("58ED918A9CAEAC212095").ToString(),
                            道具位置 = PropLoaction.背包.ToString(),
                            道具数量 = 1.ToString()
                        };
                        //在这里新增每日礼包
                        new DataProcess().AddPlayerProp(info);
                        //PropInfo info1 = new PropInfo
                        //{
                        //    道具类型ID = "2017101301",
                        //    道具位置 = PropLoaction.背包.ToString(),
                        //    道具数量 = 1.ToString()
                        //};
                        //new DataProcess().AddPlayerProp(info1);
                        new DataProcess().SaveUserDataFile(user);
                        LogSystem.JoinLog(LogSystem.EventKind.每日礼包);
                        MessageBox.Show(Res.RM.GetString("每日礼包领取成功"), Res.RM.GetString("每日礼包"));
                    }
                    else
                    {
                        MessageBox.Show(Res.RM.GetString("每日礼包已领取"), Res.RM.GetString("每日礼包"));
                    }
                }
                else
                {
                    MessageBox.Show(Res.RM.GetString("每日礼包领取失败"), Res.RM.GetString("每日礼包"));
                }
            }
        }
        private static object _locker2 = new object();
        internal static void ReceiveDailyGift2()//每日福利
        {
            lock (_locker2)
            {
                int nts = Tools.GetTime.GetNtp();
                if (nts != -1)
                {
                    UserInfo user = new DataProcess().ReadUserInfo();
                    if (string.IsNullOrEmpty(user.每日福利时间) || nts - Convert.ToDouble(user.每日福利时间) >= 1)
                    {
                        string script = "", msg = "", t = "限时福利";
                        if (user.星辰VIP)
                        {
                            t = "星辰VIP限时福利";
                            msg = "您获得了星辰VIP专属福利:\r\n";
                            script = SkRC4.DES.DecryptRC4(new ConvertJson().GetWeb("http://update.shikong.info:9696/sk/xcgift2.ini"), new DataProcess().GetKey(1));

                        }
                        else if (user.至尊VIP)
                        {
                            t = "至尊VIP限时福利";
                            msg = "您获得了至尊VIP专属福利:\r\n";
                            script = SkRC4.DES.DecryptRC4(new ConvertJson().GetWeb("http://update.shikong.info:9696/sk/zzgift2.ini"), new DataProcess().GetKey(1));
                        }
                        else
                        {
                            MessageBox.Show("该福利只能至尊VIP或者星辰VIP才能领取噢~", "限时福利");
                            return;
                        }
                        if (script != "" && script != null)
                        {
                            string _;
                            script = script.Replace("\r", "");
                            string[] scriptList = script.Split('\n');
                            user.每日福利时间 = nts.ToString();
                            new DataProcess().SaveUserDataFile(user);
                            foreach (string s in scriptList)
                            {
                                new DataProcess().RunPropScript(s, out _, "限时福利");
                                msg += _.Replace("您得到了自然女神的祝福,获得了:<br>", "").Replace("<br>", "\r\n") + "\r\n";
                            }
                            MessageBox.Show(msg.Replace(":<br>", ":\r\n").Replace("<br>", "\r\n"), t);
                        }
                        else
                        {
                            MessageBox.Show("未获取到限时福利信息,请检查网络!\r\n如果不是网络问题则暂时取消限时福利!", "限时福利");
                        }

                    }
                    else
                    {
                        MessageBox.Show("限时福利礼包已领取", "每日福利");
                    }
                }
                else
                {
                    MessageBox.Show("限时福利礼包领取失败!", "每日福利");
                }
            }
        }

        internal void Feedback()
        {
            UserInfo user = ReadUserInfo();
            short t = Convert.ToInt16(user.支持次数);
            if (t < 4) return;
            PropInfo info = new PropInfo
            {
                道具类型ID = GetInt("58ED918A9CAEAC212095").ToString(),
                道具位置 = PropLoaction.背包.ToString(),
                道具数量 = 1.ToString()
            };
            user.金币 = (Convert.ToInt64(user.金币) + 4000000).ToString();
            AddPlayerProp(info);
            user.支持次数 = (t - 4).ToString();
            SaveUserDataFile(user);
            MessageBox.Show(Res.RM.GetString("感谢支持"));
        }

        internal static string IP = null;
        internal static string DiskInfo = null;     
        internal static string GetDiskInfo()
        {
            try
            {
                ManagementObjectSearcher searcher = new ManagementObjectSearcher("SELECT * FROM Win32_PhysicalMedia");
                string diskinfo = string.Empty;
                foreach (var o in searcher.Get())
                {
                    var mo = (ManagementObject)o;
                    diskinfo = mo["SerialNumber"].ToString().Trim();
                    break;
                }

                if (string.IsNullOrEmpty(diskinfo))
                {
                    SkTools.AutoClosedMsgBox.Show(Res.RM.GetString("反虚拟机"), Res.RM.GetString("提示"), 5000);
                    Tools.ForcedExit("虚拟机");
                }
                return diskinfo;
            }
            catch
            {
                return "qsdashuaibi";
            }
        }

        internal static string GetMachineInfo()
        {
            UserInfo user = new DataProcess().ReadUserInfo();
            if (EnvironmentMode == 1)
            {
                if (!string.IsNullOrEmpty(user.NB1) && user.NB1.Contains("."))
                {
                    try
                    {
                        string ip = new ConvertJson().GetWeb("http://shikong.info/api/getip");
                        ip = ip.Replace("\"", "");
                        //NativeMethods.AutoClosedMsgBox.Show(ip+"=="+user.NB1, Res.RM.GetString("提示"), 5000);
                        if (string.IsNullOrEmpty(ip))
                        {
                            SkTools.AutoClosedMsgBox.Show(Res.RM.GetString("获取ip失败"), Res.RM.GetString("提示"),
                                10000);
                            Tools.ForcedExit("云端获取ip失败");
                        }

                        if (user.NB1.Equals(ip))
                        {
                            IP = ip;
                        }

                        return ip;
                    }
                    catch
                    {
                        SkTools.AutoClosedMsgBox.Show(Res.RM.GetString("获取ip失败"), Res.RM.GetString("提示"), 10000);
                        Tools.ForcedExit("云端获取ip失败");
                    }
                }
            }
            else if (EnvironmentMode == 0)
            {
                try
                {
                    ManagementObjectSearcher searcher =
                        new ManagementObjectSearcher("SELECT * FROM Win32_PhysicalMedia");
                    string diskinfo = string.Empty;
                    foreach (var o in searcher.Get())
                    {
                        var mo = (ManagementObject)o;
                        diskinfo = mo["SerialNumber"].ToString().Trim();
                        break;
                    }

                    if (string.IsNullOrEmpty(diskinfo))
                    {
                        SkTools.AutoClosedMsgBox.Show(Res.RM.GetString("反虚拟机"), Res.RM.GetString("提示"), 5000);
                        Tools.ForcedExit("虚拟机");
                    }

                    if (user.NB1 == diskinfo || user.NB2 == diskinfo || user.NB3 == diskinfo)
                    {
                        DiskInfo = diskinfo;
                    }

                    return diskinfo;
                }
                catch
                {
                    SkTools.AutoClosedMsgBox.Show(Res.RM.GetString("反虚拟机"), Res.RM.GetString("提示"), 5000);
                    Tools.ForcedExit("虚拟机");
                }
            }

            return null;
        }

        internal void AntiBug1()
        {
            GetPAP();
            foreach (PropInfo prop in PP_List)
            {
                if (prop.道具名字!=null && prop.道具名字.Equals(Error) && !prop.道具序号.Contains("992"))
                {
                    DeletePP1(prop.道具序号);
                }
            }
        }

        internal void AntiBug2()
        {
            List<TaskInfo> TaskList_HR = GetTasks_PHR();

            if (TaskList_HR == null || TaskList_HR.Count == 0)
            {
                return;
            }

            List<TaskInfo> allTaskList = GetAllTaskAim();
            foreach (TaskInfo Task_HR in TaskList_HR)
            {
                bool abandon = true;
                foreach (TaskInfo eachTask in allTaskList)
                {
                    if (Task_HR.任务序号.Equals(eachTask.任务序号))
                    {
                        abandon = false;
                    }
                }
                //删除任务 - 放弃任务
                //if (abandon && !Task_HR.网络任务 != null && Convert.ToBoolean(Task_HR.网络任务))
                //{
                //    AbortTask(Task_HR.任务序号);
                //}
                if (abandon && !Task_HR.网络任务 != null && !Convert.ToBoolean(Task_HR.网络任务))
                {
                    if (Task_HR.已完成 != "0") AbortTask(Task_HR.任务序号);
                }

            }
        }

        internal static int[] Gdsjsz(int min, int max, int number)
        {
            //Random ran = new Random();
            //long tick = DateTime.Now.Ticks;
            //Random ran = new Random((int)(tick & 0xffffffffL) | (int)(tick >> 32));
            int[] inumber = new int[number];
            for (int i = 0; i < number; i++)
            {
                inumber[i] = RandomGenerator.Next(min, max);
                for (int j = 0; j < i; j++)
                {
                    if (inumber[i] == inumber[j])
                    {
                        inumber[i] = RandomGenerator.Next(min, max);
                    }
                }
            }

            return inumber;
        }
        internal void ThrowError(short errorCode)
        {
            if (errorCode == 1)
            {
                throw new ArgumentNullException("错误代码：1", "读取不到存档！");
            }

            if (errorCode == 2)
            {
                throw new ArgumentNullException("错误代码：2", "游戏配置文件出错！");
            }
        }

        internal bool NameRulerMsg(string name, int special = 0)
        {
            short c = 0;
            if (string.IsNullOrEmpty(name))
            {
                c = 1;
            }

            if (name.Length > 20 || name.Length == 0)
            {
                c = 1;
            }

            foreach (string zz in Reg.SpecialChar)
            {
                if (name.Contains(zz))
                {
                    c = 1;
                }
            }

            if (special == 1)
            {
                if (name.Equals("0"))
                {
                    c = 1;
                }
            }

            if (c == 0)
            {
                return true;
            }

            MessageBox.Show(Res.RM.GetString("命名规则提示"), Res.RM.GetString("提示"));
            return false;
        }

        internal void SetPetStateDict()
        {
            string[] petState = SkRC4.DES.DecryptRC4(ReadFile(@"PageMain/PetState.wad"), new DataProcess().GetKey(1)).Split('|');
            foreach (string cfgs in petState)
            {
                string[] cfg = cfgs.Split(',');
                PetStates.Add(Convert.ToInt32(cfg[0]), cfg[1]);
            }
        }
        public void getOnlineShop_()
        {
            try
            {
                getOnlineShop();
            }
            catch(Exception ex){
                LogSystem.JoinLog(LogSystem.EventKind.加入日志, "结晶商店线程异常:【" + ex.StackTrace + "】\r\n【" + ex.Message+"】");
            }
            
        }
        public void getOnlineShop(bool reload = false)//读取商店配置
        {
            //GameForm.发送红色公告("正在获取商店信息...");
            JF_SHOP = SkRC4.DES.EncryptRC4(DZ.GetOnlineMalls(0), GetKey(1));
            JJ_SHOP = SkRC4.DES.EncryptRC4(DZ.GetOnlineMalls(1), GetKey(1));
            JC_SHOP = SkRC4.DES.EncryptRC4(DZ.GetOnlineMalls(2), GetKey(1));
            if (reload)
            {
                JF_SHOP = SkRC4.DES.EncryptRC4(DZ.GetOnlineMalls(0), GetKey(1));
                JJ_SHOP = SkRC4.DES.EncryptRC4(DZ.GetOnlineMalls(1), GetKey(1));
                JC_SHOP = SkRC4.DES.EncryptRC4(DZ.GetOnlineMalls(2), GetKey(1));
                //MS_IP_List = JsonConvert.DeserializeObject<List<GoodsInfo>>(SkRC4.DES.DecryptRC4(JF_SHOP, GetKey(1)));  //积分道具
                //MS_CP_List = JsonConvert.DeserializeObject<List<GoodsInfo>>(SkRC4.DES.DecryptRC4(JJ_SHOP, GetKey(1)));  // 结晶道具
                //MS_IP_List = SetShopName(MS_IP_List);
                //MS_CP_List = SetShopName(MS_CP_List);
            }
            //if (JF_SHOP == "" || JF_SHOP == null)
            //{

            //}
            //if (JJ_SHOP == "" || JJ_SHOP == null)
            //{

            //}
        }
        public string getOnlineShopCFG(int i)//获取商店配置
        {
            //Thread thread = new Thread(delegate ()
            //{
            //    getOnlineShop();
            //});
            //thread.Start();
            string cfg = "";
            if (i == 0)
            {
                //getOnlineShop();
                cfg = SkRC4.DES.DecryptRC4(JF_SHOP, GetKey(1));
            }
            if (i == 1)
            {
                //getOnlineShop();
                cfg = SkRC4.DES.DecryptRC4(JJ_SHOP, GetKey(1));
            }
            if (i == 2)
            {
                //getOnlineShop();
                cfg = SkRC4.DES.DecryptRC4(JC_SHOP, GetKey(1));
            }
            return cfg;
        }

        public Int64 getMaxValue(int i)//1金币,2元宝,水晶
        {
            Int64 Max = 0;
            UserInfo user = ReadUserInfo();
            if (i == 1)
            {
                if (user.星辰VIP) Max = 15000000000;
                else if (user.至尊VIP && !user.星辰VIP) Max = 10000000000;
                else Max = 5000000000;
            }
            if (i == 2)
            {
                if (user.星辰VIP) Max = 60000000;
                else if (user.至尊VIP && !user.星辰VIP) Max = 40000000;
                else Max = 20000000;
            }
            return Max;
        }
    }
}
