﻿
@{
    Layout = null;
}

<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>home</title>
    <script src="http://***************:8089/Content/Javascript/jquery-1.8.3.min.js"></script>
    <script>
        function upMain() {
            var str = $("[name=dat]").val();
            $.ajax({
                url: "/home/<USER>",
                data: { str: str },
                type:"post",
                success: function (t) {
                    alert(t);
                }

            })

        }
        function getPropList() {
            $.ajax({
                url: "/home/<USER>",
                success: function (t) {
                    $("[name=dat]").val(t);
                }

            })

        }
        function getUserInfo() {
            $.ajax({
                url: "/home/<USER>",
                success: function (t) {
                    $("[name=dat]").val(t);
                }

            })

        }
        function getMain() {
            $.ajax({
                url: "/home/<USER>",
                success: function (t) {
                    $("[name=dat]").val(t);
                }

            })

        }
        function setUserInfo() {
            var str = $("[name=dat]").val();
            $.ajax({
                url: "/home/<USER>",
                data: { str: str },
                type: "post",
                success: function (t) {
                    alert(t);
                }

            })

        }
        function setPropList() {
            var str = $("[name=dat]").val();
            $.ajax({
                url: "/home/<USER>",
                data: { str: str },
                type: "post",
                success: function (t) {
                    alert(t);
                }

            })

        }
        function insertProp() {
            var pid = $("[name=pid]").val();
            var pnum = $("[name=pnum]").val();
            $.ajax({
                url: "/home/<USER>",
                data: {
                    pid: pid,
                    num: pnum
                },
                success: function (t) {
                    alert(t);
                }

            })

        }
    </script>
</head>
<body>
    <div>
        <textarea name="dat" style="height:400px;width:100%"></textarea>
        <button onclick="upMain()">提交存档</button>
        <button onclick="getMain()">获取存档</button><br />
        <button onclick="getPropList()">背包</button>
        <button onclick="setPropList()">保存背包</button>
        <br />
        <button onclick="getUserInfo()">信息</button>
        <button onclick="setUserInfo()">保存信息</button><br />
        道具ID：<input name="pid" type="text" value="20" />数量：<input style="width:50px" name="pnum" type="text" value="1" /><button onclick="insertProp()">添加道具</button>
    </div>
</body>
</html>
