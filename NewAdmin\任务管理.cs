﻿
using System;
using System.Collections.Generic;
using System.Windows.Forms;
using 怪物信息 = Shikong.Pokemon2.PCG.MonsterInfo;
using 用户信息 = Shikong.Pokemon2.PCG.UserInfo;
using 宠物信息 = Shikong.Pokemon2.PCG.PetInfo;
using 宠物类型 = Shikong.Pokemon2.PCG.PetConfig;
using 道具信息 = Shikong.Pokemon2.PCG.PropInfo;
using 地图信息 = Shikong.Pokemon2.PCG.MapInfo;
using 装备类型 = Shikong.Pokemon2.PCG.EquipmentType;
using 装备信息 = Shikong.Pokemon2.PCG.EquipmentInfo;
using 道具类型 = Shikong.Pokemon2.PCG.PropType;
using 道具具体信息 = Shikong.Pokemon2.PCG.PropConfig;
using 数据处理 = Shikong.Pokemon2.PCG.DataProcess;
using 任务信息 = Shikong.Pokemon2.PCG.TaskInfo;
using 任务目标类型 = Shikong.Pokemon2.PCG.task;
using 商店道具信息 = Shikong.Pokemon2.PCG.GoodsInfo;
using Shikong.Pokemon2.PCG;
using PetShikongTools;
using System.Linq;
using System.IO;

namespace Admin
{
    public partial class 任务管理 : Form
    {
        public List<任务信息> 任务列表 = new 数据处理().GetAllTaskAim();
        任务信息 现编辑任务;
        int 现编辑任务索引;
        public 任务管理()
        {
            InitializeComponent();
        }

        private void linkLabel10_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            宠物列表 列表 = new 宠物列表();
            列表.类型 = "宠物序号";
            列表.TEXT = 宠物;
            列表.ShowDialog();
        }

        private void linkLabel1_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            道具列表 列表 = new 道具列表();
            列表.TEXT = 奖励;
            列表.Show();
        }
        List<task> task = new List<task>();
        private void button1_Click(object sender, EventArgs e)
        {
            task t = new task();
            t.Type = comboBox1.Text;
            t.Num = textBox2.Text;
            t.ID = textBox3.Text;
            dataGridView2.Rows.Add(t.Type, t.Num, t.ID);
            // task.Add(t);
            //dataGridView2.DataSource = null;
            // dataGridView2.DataSource = task;

        }

        private void textBox1_TextChanged(object sender, EventArgs e)
        {
            seachText();
        }
        public void seachText()
        {
            List<任务信息> 新列表 = new List<任务信息>();
            for (int i = 0; i < 任务列表.Count; i++)
            {
                if (任务列表[i].任务名.IndexOf(textBox1.Text) != -1 || textBox1.Text.Length == 0 || 任务列表[i].任务序号.IndexOf(textBox1.Text) != -1)
                {
                    新列表.Add(任务列表[i]);
                }
            }
            dataGridView1.DataSource = 新列表;
        }
        private void linkLabel2_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            dataGridView2.Rows.Clear();
        }

        private void linkLabel3_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            comboBox1.Text = "击杀";
            怪物列表 列表 = new 怪物列表();
            列表.类型 = "怪物序号";
            列表.TEXT = textBox3;
            列表.ShowDialog();
        }

        private void linkLabel4_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            comboBox1.Text = "收集";
            道具列表 列表 = new 道具列表();
            列表.TEXT = textBox3;
            列表.清空 = true;
            列表.Show();
        }
        public List<task> getTaskTarget()
        {
            task = new List<task>();
            for (int i = 0; i < dataGridView2.Rows.Count; i++)
            {
                task t = new task();
                try
                {
                    t.Type = dataGridView2.Rows[i].Cells[0].Value.ToString();
                }
                catch
                {
                    t.Type = "";
                }

                try
                {
                    t.Num = dataGridView2.Rows[i].Cells[1].Value.ToString();
                }
                catch
                {
                    t.Num = "";
                }

                try
                {
                    t.ID = dataGridView2.Rows[i].Cells[2].Value.ToString();
                }
                catch
                {
                    t.ID = "";
                }
                task.Add(t);

            }
            return task;
        }
        private void button2_Click(object sender, EventArgs e)
        {
            任务信息 任务 = new 任务信息();
            任务.任务奖励 = 奖励.Text;
            任务.任务名 = 名字.Text;
            任务.任务目标 = getTaskTarget();
            任务.任务序号 = 序号.Text;
            任务.指定宠物 = 宠物.Text;
            任务.前置任务 = textBox5.Text;
            if (checkBox1.Checked)
            {
                任务.允许重复 = "1";
            }
            if (textBox4.Text == "")
            {
                textBox4.Text = "亲爱的口袋勇士！我这里有一些精美礼品送给你们哦，但是也不能不劳而获，来帮助自然女神做几件事情吧，完成后这些精美礼品就属于你的啦...";
            }
            textBox4.Text = textBox4.Text.Replace("\r\n", "<br>");
            if (new 数据处理().AddTaskAim(任务, textBox4.Text))
            {
                MessageBox.Show("添加任务成功!");
                task = new List<task>();
                dataGridView2.Rows.Clear();
                任务列表 = new 数据处理().GetAllTaskAim();
                dataGridView1.DataSource = 任务列表;

            }
            else
            {
                MessageBox.Show("添加任务失败!序号已经存在!");
            }
        }

        private void 任务管理_Load(object sender, EventArgs e)
        {

            任务列表 = new 数据处理().GetAllTaskAim();
            dataGridView1.DataSource = 任务列表;
        }
        private void dataGridView1_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {
            string typeID = dataGridView1.Rows[dataGridView1.CurrentRow.Index].Cells["任务序号"].Value.ToString();
            int i = 0;
            dataGridView2.Rows.Clear();
            foreach (任务信息 任务 in 任务列表)
            {
                if (typeID == 任务.任务序号)
                {
                    现编辑任务 = 任务;
                    序号.Text = 任务.任务序号;
                    名字.Text = 任务.任务名;
                    宠物.Text = 任务.指定宠物;
                    textBox5.Text = 任务.前置任务;
                    奖励.Text = 任务.任务奖励;
                    宠物.Text = 任务.指定宠物;
                    现编辑任务索引 = i;
                    textBox4.Text = GetTaskIntroduction(任务.任务序号);

                    checkBox1.Checked = 任务.允许重复 == "1" ? true : false;
                    foreach (var rw in 任务.任务目标)
                    {
                        dataGridView2.Rows.Add(rw.Type, rw.Num, rw.ID);
                    }
                    task = 任务.任务目标;
                    break;
                }
                ++i;
            }
        }
        private string GetTaskIntroduction(string 任务id)
        {
            try
            {
                if (!File.Exists(@"PageMain\task" + @"\" + 任务id + "_.dat"))
                {
                    return "该任务没有更多的线索提示";
                }
                string 存档 = new 数据处理().ReadFile(@"PageMain\task" + @"\" + 任务id + "_.dat");
                存档 = SkRC4.DES.DecryptRC4(存档, @"qiqiwan.2016.2017.2018.2020.2021.2022");
                return 存档;
            }
            catch
            {
                return "任务不存在";
            }
        }
        private void checkBox1_CheckedChanged(object sender, EventArgs e)
        {

        }

        private void button4_Click(object sender, EventArgs e)
        {

            if (现编辑任务索引 == -1)
            {
                MessageBox.Show("请先选择一个任务");
                return;
            }
            if (任务列表[现编辑任务索引].任务序号 != 序号.Text)
            {
                MessageBox.Show("任务序号不一致，无法直接替换。");
                return;
            }
            if (MessageBox.Show("你确定要替换任务" + 任务列表[现编辑任务索引].任务名 + "吗？", "提示", MessageBoxButtons.YesNo) == DialogResult.No)
            {
                return;
            }

            任务信息 任务 = new 任务信息();
            List<task> tTask = new List<task>();

            int Num = dataGridView2.Rows.Count;
            for (int row = 0; row < Num; ++row)
            {
                task t = new task();
                t.Type = dataGridView2.Rows[row].Cells[0].Value.ToString();
                t.Num = dataGridView2.Rows[row].Cells[1].Value.ToString();
                try
                {
                    t.ID = dataGridView2.Rows[row].Cells[2].Value.ToString();
                }
                catch (Exception)
                {
                    t.ID = "";
                }

                tTask.Add(t);
            }
            任务.任务目标 = tTask;
            任务.任务奖励 = 奖励.Text;
            任务.任务名 = 名字.Text;
            任务.任务序号 = 序号.Text;
            任务.指定宠物 = 宠物.Text;
            任务.前置任务 = textBox5.Text;
            if (checkBox1.Checked)
            {
                任务.允许重复 = "1";
            }
            var name = 任务列表[现编辑任务索引].任务名;

            任务列表[现编辑任务索引] = 任务;
            T.SaveTaskAim(任务列表);
            new 数据处理().SaveFile(SkRC4.DES.EncryptRC4(textBox4.Text = textBox4.Text.Replace("\r\n", "<br>"), @"qiqiwan.2016.2017.2018.2020.2021.2022"), @"PageMain\task" + @"\" + 任务.任务序号 + "_.dat");
            MessageBox.Show("成功替换了任务 " + 任务列表[现编辑任务索引].任务名);

        }

        private void linkLabel5_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            try
            {
                dataGridView2.Rows.Remove(dataGridView2.CurrentRow);
            }
            catch (Exception)
            {

            }


        }

        private void dataGridView2_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {

        }

        private void textBox6_TextChanged(object sender, EventArgs e)
        {

        }

        private void button3_Click(object sender, EventArgs e)
        {
            String str = textBox6.Text.ToUpper();
            while (true)
            {
                str = str.Replace("  ", " ");
                if (!str.Contains("  ")) break;
            }
            if (str.Substring(0, 1) == " ") str = str.Substring(1, str.Length - 1);
            str = str.Replace("X", " ");
            str = str.Replace("  ", ",");//临时把  替换成,
            str = str.Replace("\r\n", "|");
            str = str.Replace("| ", "|");
            str = str.Replace("级", "").Replace("等达到", " ").Replace("层", "").Replace("个", "").Replace("%", "");
            str = str.Replace(" ", ",");
            str = str.Replace(",,", ",");//临时
            textBox6.Text = str;
            String p = textBox6.Text;
            String[] sp = p.Split('|');
            String err = "";
            foreach (var s in sp)
            {
                String[] ss = s.Split(',');
                if (ss.Length >= 3)
                {
                    if (ss[0] == "击杀")
                    {
                        task t = new task();
                        t.Type = "击杀";
                        t.Num = ss[2];
                        var m = T.getMInfo(ss[1]);
                        if (m != null)
                        {
                            t.ID = m.怪物序号;
                            dataGridView2.Rows.Add(t.Type, t.Num, t.ID);
                            //task.Add(t);
                            //dataGridView2.DataSource = null;
                            //dataGridView2.DataSource = task;
                        }
                        else
                        {
                            if (err != "")
                            {
                                err += "|" + s;
                            }
                            else
                            {
                                err += s;
                            }
                        }

                    }

                    else if (ss[0] == "保留成长")
                    {
                        task t = new task();
                        t.Type = "保留成长";
                        t.Num = ss[1];
                        t.ID = ss[2];
                        dataGridView2.Rows.Add(t.Type, t.Num, t.ID);
                        //task.Add(t);
                        //dataGridView2.DataSource = null;
                        //dataGridView2.DataSource = task;
                    }
                    else if (ss[0] == "收集")
                    {
                        task t = new task();
                        t.Type = "收集";
                        t.Num = ss[2];
                        if (ss[1] == "元宝")
                        {

                            t.Type = "元宝";
                            t.Num = ss[2];
                            t.ID = ss[2];
                            dataGridView2.Rows.Add(t.Type, t.Num, t.ID);
                            //task.Add(t);
                            //dataGridView2.DataSource = null;
                            //dataGridView2.DataSource = task;

                        }
                        else
                        {
                            var m = T.getPInfo1(ss[1]);
                            if (m == null)
                            {
                                m = T.getPInfo(ss[1]);
                            }
                            if (m != null)
                            {
                                t.ID = m.道具序号;
                                dataGridView2.Rows.Add(t.Type, t.Num, t.ID);
                                //task.Add(t);
                                //dataGridView2.DataSource = null;
                                //dataGridView2.DataSource = task;
                            }
                            else if (m == null)
                            {
                                if (ss[1] == "成长抽取石（卍）") dataGridView2.Rows.Add(t.Type, t.Num, "2017052501");
                                else if (ss[1] == "成长抽取石（卐）") dataGridView2.Rows.Add(t.Type, t.Num, "2017052502");
                            }
                            else
                            {
                                if (err != "")
                                {
                                    err += "|" + s;
                                }
                                else
                                {
                                    err += s;
                                }
                            }

                        }

                    }

                    else
                    {

                        if (err != "")
                        {
                            err += "|" + s;
                        }
                        else
                        {
                            err += s;
                        }
                    }
                }
                else if (ss.Length >= 2 && (ss[0] == "VIP等级达到" || ss[0] == "VIP等级" || ss[0] == "VIP"))
                {
                    task t = new task();
                    t.Type = "VIP";
                    t.Num = ss[1];
                    t.ID = ss[1];
                    dataGridView2.Rows.Add(t.Type, t.Num, t.ID);
                    //task.Add(t);
                    //dataGridView2.DataSource = null;
                    //dataGridView2.DataSource = task;

                }
                else if (ss.Length >= 2 && (ss[0] == "宠物"))
                {
                    task t = new task();
                    t.Type = "等级";
                    t.Num = ss[1];
                    t.ID = ss[1];
                    dataGridView2.Rows.Add(t.Type, t.Num, t.ID);
                    //task.Add(t);
                    //dataGridView2.DataSource = null;
                    //dataGridView2.DataSource = task;

                }
                else if (ss.Length >= 2 && (ss[0] == "地狱之门达到" || ss[0] == "地狱之门" || ss[0] == "地狱"))
                {
                    task t = new task();
                    t.Type = "地狱";
                    t.Num = ss[1];
                    t.ID = ss[1];
                    dataGridView2.Rows.Add(t.Type, t.Num, t.ID);
                    //task.Add(t);
                    //dataGridView2.DataSource = null;
                    //dataGridView2.DataSource = task;

                }
                else if (ss.Length >= 2 && (ss[0] == "元宝"))
                {
                    task t = new task();
                    t.Type = "元宝";
                    t.Num = ss[1];
                    t.ID = ss[1];
                    dataGridView2.Rows.Add(t.Type, t.Num, t.ID);
                    //task.Add(t);
                    //dataGridView2.DataSource = null;
                    //dataGridView2.DataSource = task;

                }
                else if (ss.Length >= 2 && (ss[0] == "水晶"))
                {
                    task t = new task();
                    t.Type = "水晶";
                    t.Num = ss[1];
                    t.ID = ss[1];
                    dataGridView2.Rows.Add(t.Type, t.Num, t.ID);
                    //task.Add(t);
                    //dataGridView2.DataSource = null;
                    //dataGridView2.DataSource = task;

                }
                else if (ss.Length >= 2 && (ss[0] == "金币"))
                {
                    task t = new task();
                    t.Type = "金币";
                    t.Num = ss[1];
                    t.ID = ss[1];
                    dataGridView2.Rows.Add(t.Type, t.Num, t.ID);
                    //task.Add(t);
                    //dataGridView2.DataSource = null;
                    //dataGridView2.DataSource = task;

                }
                else if (ss[0] == "至尊VIP")
                {
                    task t = new task();
                    t.Type = "至尊VIP";
                    t.Num = "";
                    t.ID = "";
                    dataGridView2.Rows.Add(t.Type, t.Num, t.ID);
                    //task.Add(t);
                    //dataGridView2.DataSource = null;
                    //dataGridView2.DataSource = task;
                }
                else if (ss[0] == "星辰VIP")
                {
                    task t = new task();
                    t.Type = "星辰VIP";
                    t.Num = "";
                    t.ID = "";
                    dataGridView2.Rows.Add(t.Type, t.Num, t.ID);
                    //task.Add(t);
                    //dataGridView2.DataSource = null;
                    //dataGridView2.DataSource = task;
                }
                else
                {
                    if (err != "")
                    {
                        err += "|" + s;
                    }
                    else
                    {
                        err += s;
                    }
                }
            }
            textBox6.Text = err;
            if (err != "")
            {
                MessageBox.Show("有指令无法寻找到对应的道具或者怪物，已经显示在指令框中，请根据实际情况自己手动添加。");
            }
        }

        private void button5_Click(object sender, EventArgs e)
        {
            String str = textBox7.Text.Replace(" ", "");//.ToUpper()
            while (true)
            {
                str = str.Replace("  ", " ");
                if (!str.Contains("  ")) break;
            }
            str = str.Replace(" ", ",");
            str = str.Replace("\r\n", "|道具,");
            str = str.Replace("X", ",");
            str = str.Replace("x", ",");
            str = str.Replace("个", "");
            str = str.Replace("修练仙册[嗔]", "修炼仙册");
            str = str.Replace("修炼仙册.嗔", "修炼仙册");
            str = str.Replace("修炼仙册·［嗔］", "修炼仙册");
            str = "|道具," + str;
            textBox7.Text = str;
            String p = textBox7.Text;
            String[] sp = p.Split('|');
            String err = "";
            String jl = "";
            foreach (var s in sp)
            {
                String[] ss = s.Split(',');
                if (ss.Length >= 2)
                {
                    if (ss[0] == "道具")
                    {
                        var m = T.getPInfo(ss[1]);
                        //这里对获取不到道具ID的道具进行处理
                        if (m == null)
                        {
                            if (ss[1].Equals("小神龙琅琊之卵60CC") || ss[1].Equals("60CC小神龙之卵"))
                            {
                                m = new 道具类型() { 道具序号 = "2017080712" };
                            }
                            else if (ss[1].Equals("mask之卵") || ss[1].Equals("mask之卵"))
                            {
                                m = new 道具类型() { 道具序号 = "201811102" };
                            }
                            else if (ss[1].Equals("残酷之卵") || ss[1].Equals("残酷之卵"))
                            {
                                m = new 道具类型() { 道具序号 = "2018121201" };
                            }
                            else if (ss[1].Equals("≮残酷≯之卵") || ss[1].Equals("≮残酷≯之卵"))
                            {
                                m = new 道具类型() { 道具序号 = "2018121202" };
                            }
                            else if (ss[1].Equals("爱丽雅之卵") || ss[1].Equals("爱莉雅之卵"))
                            {
                                m = new 道具类型() { 道具序号 = "2016100901" };
                            }

                        }
                        string num = "1";
                        if (ss.Length >= 3) num = ss[2];
                        if (m != null)
                        {
                            if (jl != "")
                            {
                                jl += "|道具," + m.道具序号 + "," + num;
                            }
                            else
                            {
                                jl = "道具," + m.道具序号 + "," + num;
                            }
                        }
                        else
                        {
                            if (err != "")
                            {
                                err += "|" + s;
                            }
                            else
                            {
                                err += s;
                            }
                        }
                    }
                    else
                    {//1

                        if (err != "")
                        {
                            err += "|" + s;
                        }
                        else
                        {
                            err += s;
                        }
                    }

                }
                else
                {
                    if (err != "")
                    {
                        err += "|" + s;
                    }
                    else
                    {
                        err += s;
                    }
                }
            }
            奖励.Text = jl;
            textBox7.Text = err;
            if (err != "")
            {
                MessageBox.Show("有指令无法寻找到对应的道具信息，已经显示在指令框中，请根据实际情况自己手动添加。");
            }
        }

        private void label2_Click(object sender, EventArgs e)
        {
            String id = DateTime.Now.ToString("yyyyMMdd");
            int i = 0;
            var taskList = new DataProcess().GetAllTaskAim();
            while (true)
            {
                i++;
                String ii = i.ToString();
                if (ii.Length < 2) ii = "0" + ii;
                var task = taskList.FirstOrDefault(C => C.任务序号 == id + ii);
                if (task == null)
                {
                    序号.Text = id + ii;
                    break;
                }
            }

        }

        private void label4_Click(object sender, EventArgs e)
        {

        }

        private void label11_Click(object sender, EventArgs e)
        {
            if (序号.Text != "")
            {
                int id = Convert.ToInt32(序号.Text);
                id = id - 1;
                textBox5.Text = id.ToString();
            }
        }

        private void label1_Click(object sender, EventArgs e)
        {

        }

        private void groupBox1_Enter(object sender, EventArgs e)
        {

        }

        private void textBox7_TextChanged(object sender, EventArgs e)
        {

        }

        private void linkLabel10_LinkClicked_1(object sender, LinkLabelLinkClickedEventArgs e)
        {
            宠物列表 列表 = new 宠物列表();
            列表.类型 = "宠物序号";
            列表.TEXT = 宠物;
            列表.ShowDialog();
        }

        private void textBox5_TextChanged(object sender, EventArgs e)
        {

        }

        private void textBox4_TextChanged(object sender, EventArgs e)
        {

        }

        private void label10_Click(object sender, EventArgs e)
        {

        }

        private void groupBox2_Enter(object sender, EventArgs e)
        {

        }

        private void textBox3_TextChanged(object sender, EventArgs e)
        {

        }

        private void comboBox1_SelectedIndexChanged(object sender, EventArgs e)
        {

        }

        private void textBox2_TextChanged(object sender, EventArgs e)
        {

        }

        private void label8_Click(object sender, EventArgs e)
        {

        }

        private void label9_Click(object sender, EventArgs e)
        {

        }

        private void label7_Click(object sender, EventArgs e)
        {

        }

        private void label6_Click(object sender, EventArgs e)
        {

        }

        private void 奖励_TextChanged(object sender, EventArgs e)
        {

        }

        private void label5_Click(object sender, EventArgs e)
        {

        }

        private void 宠物_TextChanged(object sender, EventArgs e)
        {

        }

        private void 名字_TextChanged(object sender, EventArgs e)
        {

        }

        private void label3_Click(object sender, EventArgs e)
        {
            if (名字.Text.IndexOf("①") != -1)
            {
                名字.Text = 名字.Text.Replace("①", "②");
            }
            else if (名字.Text.IndexOf("②") != -1)
            {
                名字.Text = 名字.Text.Replace("②", "③");
            }
            else if (名字.Text.IndexOf("③") != -1)
            {
                名字.Text = 名字.Text.Replace("③", "④");
            }
            else if (名字.Text.IndexOf("④") != -1)
            {
                名字.Text = 名字.Text.Replace("④", "⑤");
            }
            else if (名字.Text.IndexOf("⑤") != -1)
            {
                名字.Text = 名字.Text.Replace("⑤", "⑥");
            }
            else if (名字.Text.IndexOf("⑥") != -1)
            {
                名字.Text = 名字.Text.Replace("⑥", "⑦");
            }
            else if (名字.Text.IndexOf("⑦") != -1)
            {
                名字.Text = 名字.Text.Replace("⑦", "⑧");
            }
            else if (名字.Text.IndexOf("⑧") != -1)
            {
                名字.Text = 名字.Text.Replace("⑧", "⑨");
            }
            else if (名字.Text.IndexOf("⑨") != -1)
            {
                名字.Text = 名字.Text.Replace("⑨", "①");
            }
            else
            {
                名字.Text += "①";
            }
        }

        private void 序号_TextChanged(object sender, EventArgs e)
        {

        }

        private void linkLabel6_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            comboBox1.Text = "装备";
            装备列表 列表 = new 装备列表();
            列表.TEXT = textBox3;
            列表.清空 = true;
            列表.Show();
        }

        private void 水晶200万ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            dataGridView2.Rows.Add("水晶", "2000000", "");
        }

        private void 水晶300万ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            dataGridView2.Rows.Add("水晶", "3000000", "");
        }

        private void 水晶500万ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            dataGridView2.Rows.Add("水晶", "5000000", "");
        }

        private void 强化石300万ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            dataGridView2.Rows.Add("收集", "3000000", "2017060302");
        }

        private void 强化石500万ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            dataGridView2.Rows.Add("收集", "5000000", "2017060302");
        }

        private void 三色龙鳞200个ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            dataGridView2.Rows.Add("收集", "200", "2016110403");
            dataGridView2.Rows.Add("收集", "200", "2018040102");
            dataGridView2.Rows.Add("收集", "200", "2018040103");
        }

        private void 三个龙鳞500个ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            dataGridView2.Rows.Add("收集", "500", "2016110403");
            dataGridView2.Rows.Add("收集", "500", "2018040102");
            dataGridView2.Rows.Add("收集", "500", "2018040103");
        }

        private void linkLabel7_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            comboBox1.Text = "装备";
            装备列表 列表 = new 装备列表();
            列表.TEXT = 奖励;
            列表.Show();
        }

        private void 觉醒任务3修正ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            任务信息 t = new 任务信息();
            t.任务目标 = (List<task>)dataGridView2.DataSource;
            for (int i = 0; i < t.任务目标.Count; i++)
            {
                if (t.任务目标[i].Type == "收集" && t.任务目标[i].Num == "3500")
                {
                    t.任务目标[i].Num = "1999";
                }
            }
            dataGridView2.DataSource = new List<task>();
            dataGridView2.DataSource = t.任务目标;

        }

        private void 通过玲珑城ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            dataGridView2.Rows.Add("击杀", "20", "413");
        }

        private void 修炼仙册188个ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            奖励.Text = "道具,2017061401,188";
        }

        private void 修炼仙册288个ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            奖励.Text = "道具,2017061401,288";
        }

        private void 重生四阶1个ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            奖励.Text = "道具,2017121301,1";
        }

        private void 删除选中任务ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            int index = dataGridView1.CurrentRow.Index;
            string typeID = dataGridView1.Rows[index].Cells["任务序号"].Value.ToString();//获取选中的任务ID
            if (MessageBox.Show("你确定要删除任务[" + typeID + "]吗？", "提示", MessageBoxButtons.YesNo) == DialogResult.No)
            {
                return;
            }
            //删除选中的任务
            dataGridView1.DataSource = new List<任务信息>();
            var currTask = 任务列表.FirstOrDefault(t => t.任务序号 == typeID);
            if (currTask == null)
            {
                MessageBox.Show("任务不存在，无法删除！");
                return;
            }

            任务列表.Remove(currTask);
            //
            T.SaveTaskAim(任务列表);//保存任务配置


            dataGridView1.DataSource = 任务列表;
            if (textBox1.Text != "") seachText();//如果有搜索词汇则自动搜索一次
            if (任务列表.Count > 0 && index > 0) dataGridView1.CurrentCell = dataGridView1.Rows[index - 1].Cells[0];
            //自动选中上次删除的任务的前一个任务，方便操作，判断条件是为了避免任务都已经删完或者选择第一个的情况
        }

        private void 任务类型说明_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            MessageBox.Show("扣除成长:保留10%CC。 ID=宠物ID，Num=达到成长\n" +
                "扣除成长2:保留1%CC。 ID=宠物ID，Num=达到成长\n" +
                "保留成长:数量=达到的成长，ID=保留的值，必须有指定宠物。+\n" +
                "隐藏主宠提示:不显示主宠是什么");
        }

        private void nextTask_Click(object sender, EventArgs e)
        {
            label2_Click(null, null);
            label3_Click(null, null);
            label11_Click(null, null);
        }

        private void button6_Click(object sender, EventArgs e)
        {
            string allTxt = "";
            string taskTxt = "{任务名}\r\n任务目标：\r\n{任务目标}\r\n任务奖励：\r\n{任务奖励}";
            foreach (var t in 任务列表)
            {
                if (t.任务名.IndexOf("【神宠专属】") != -1)
                {
                    //处理文本
                    //任务目标
                    string 目标 = "";
                    var 信息 = new Game_Helper().GetAppointedTaskAim(t.任务序号);
                    try
                    {
                        foreach (var task in t.任务目标)
                        {
                            if (task.Type.Equals("等级"))
                            {
                                目标 += $"宠物等级达到 {task.Num} 级<br/>";
                            }
                            else if (task.Type.Equals("击杀"))
                            {
                                目标 += $"击杀 {new Game_Helper().Get_SMT(task.ID).怪物名字} {task.Num} 个<br/>";
                            }
                            else if (task.Type.Equals("收集"))
                            {
                                目标 += $"收集 {new Game_Helper().GetAPType(task.ID).道具名字} {task.Num} 个<br/>";
                            }
                            else if (task.Type.Equals("一键完成道具"))
                            {
                                目标 += $"一键完成需要收集： {new Game_Helper().GetAPType(task.ID).道具名字} {task.Num} 个<br/>";
                            }
                            else if (task.Type.Equals("金币"))
                            {
                                目标 += $"收集 金币 {task.Num} 个<br/>";
                            }
                            else if (task.Type.Equals("元宝"))
                            {
                                目标 += $"收集 元宝 {task.Num} 个<br/>";
                            }
                            else if (task.Type.Equals("水晶"))
                            {
                                目标 += $"收集 水晶 {task.Num} 个<br/>";
                            }
                            else if (task.Type.Equals("时之券"))
                            {
                                目标 += $"收集 时之券 {task.Num} 个<br/>";
                            }
                            else if (task.Type.Equals("威望"))
                            {
                                目标 += $"威望 达到 {task.Num} 点<br/>";
                            }
                            else if (task.Type.Equals("宠物"))
                            {
                                目标 += $"宠物 {new DataProcess().GetAppointedPetType(task.ID).宠物名字} 达到 {task.Num} CC<br/>";
                            }
                            else if (task.Type.Equals("主宠达到成长"))
                            {
                                目标 += $"主宠物达到 {task.Num} CC<br/>";
                            }
                            else if (task.Type.Equals("扣除成长"))
                            {
                                目标 += $"宠物 {new DataProcess().GetAppointedPetType(task.ID).宠物名字} 达到 {task.Num} CC（任务完成后保留10%）<br/>";
                            }
                            else if (task.Type.Equals("扣除成长2"))
                            {
                                目标 += $"宠物 {new DataProcess().GetAppointedPetType(task.ID).宠物名字} 达到 {task.Num} CC（任务完成后保留1%）<br/>";
                            }
                            else if (task.Type.Equals("保留成长"))//数量=达到的成长，ID=保留的值
                            {
                                目标 += $"{new DataProcess().GetAppointedPetType(信息.指定宠物).宠物名字} 达到 {task.Num} CC（任务完成后保留{task.ID}%）<br/>";
                            }
                            else if (task.Type.Equals("装备"))
                            {
                                目标 += $"收集 {new DataProcess().GetAET(task.ID).名字} 1件<br/>";
                            }
                            else if (task.Type.Equals("时间"))
                            {
                                string time = DateTime.ParseExact(task.Num, "yyyyMMdd", null).ToShortDateString();//任务时间
                                目标 += $"任务必须在 {time} 之前完成(包含当天)<br/>";
                            }
                            else if (task.Type.Equals("地狱"))
                            {
                                目标 += $"地狱之门达到 {task.Num} 层<br/>";
                            }
                            else if (task.Type.Equals("通天"))
                            {
                                目标 += $"通天塔达到 {task.Num} 层<br/>";
                            }
                            else if (task.Type.Equals("VIP"))
                            {
                                目标 += $"VIP等级达到 {task.Num} 级<br/>";
                            }
                            else if (task.Type.Equals("至尊VIP"))
                            {
                                目标 += $"激活 [高级特权]至尊VIP<br/>";
                            }
                            else if (task.Type.Equals("星辰VIP"))
                            {
                                目标 += $"激活 [高级特权]星辰VIP<br/>";
                            }
                            else if (task.Type.Equals("积分"))
                            {
                                目标 += $"VIP积分达到 {task.Num} 分<br/>";
                            }
                            else if (task.Type.Equals("自动合宠经验"))
                            {
                                目标 += $"拥有自动合宠经验 {task.Num} 点<br/>";
                            }
                            else if (task.Type.Equals("自动合宠次数"))
                            {
                                目标 += $"拥有自动合宠次数 {task.Num} 次<br/>";
                            }
                            else if (task.Type.Equals("多个主宠"))
                            {
                                string[] petID = task.ID.Split(',');
                                string petName = "";
                                for (int i = 0; i < petID.Length; i++)
                                {
                                    petName += new DataProcess().GetAppointedPetType(petID[i]).宠物名字 + "、";
                                }
                                petName = petName.Remove(petName.Length - 1, 1);
                                目标 += $"需要主宠为：{petName} <br/>";
                            }
                        }
                    }
                    catch(Exception ex)
                    {
                        目标 = "出错:"+ex.Message;
                    }

                    //任务奖励
                    string 奖励 = string.Empty;
                    string[] 分割 = 信息.任务奖励.Split('|');
                    foreach (string i in 分割)
                    {
                        string[] 子分割 = i.Split(',');
                        if (子分割.Length == 3)
                        {
                            if (子分割[0].Equals("道具"))
                            {
                                奖励 += $"{new Game_Helper().GetAPType(子分割[1]).道具名字} {子分割[2]} 个<br/>";
                            }
                            else if (子分割[0].Equals("装备"))
                            {
                                奖励 += $"{new DataProcess().GetAET(子分割[1]).名字} {子分割[2]} 件<br/>";
                            }
                        }

                        if (子分割.Length < 2) continue;
                        if (子分割[0].Equals("金币"))
                        {
                            奖励 += $"金币 {子分割[1]} 个<br/>";
                        }
                        else if (子分割[0].Equals("元宝"))
                        {
                            奖励 += $"元宝 {子分割[1]} 个<br/>";
                        }
                        else if (子分割[0].Equals("水晶"))
                        {
                            奖励 += $"水晶 {子分割[1]} 个<br/>";
                        }
                        else if (子分割[0].Equals("默认技能"))
                        {
                            奖励 += "刷新宠物的默认技能(如若没有默认技能或者已经习得技能则不会获得技能)<br/>";
                        }
                        else if (子分割[0].Equals("卸下魂宠"))
                        {
                            奖励 += "卸载已装备的魂宠<br/>";
                        }
                    }

                    allTxt += taskTxt.Replace("{任务名}", t.任务名).Replace("{任务目标}", 目标).Replace("{任务奖励}",奖励) + "\r\n";

                }
            }
            Clipboard.SetText(allTxt.Replace("<br/>","\r\n"));
            MessageBox.Show("已复制");
            #region 神宠专属添加宠物名字
            ////【神宠专属】王子的召唤②

            //foreach(var t in 任务列表)
            //{
            //    if (t.任务名.IndexOf("【神宠专属】")!= -1)
            //    {
            //        string name1 = "【神宠专属】";
            //        string name2 = "({name})";
            //        string name3 = t.任务名.Replace("【神宠专属】", "");
            //        PetConfig 指定宠物 = new 数据处理().GetAppointedPetType(t.指定宠物);
            //        name2 = name2.Replace("{name}", 指定宠物.宠物名字);
            //        if (t.任务名.IndexOf(指定宠物.宠物名字) != -1)
            //        {
            //            continue;
            //        }
            //        t.任务名 = name1 + name2 + name3;
            //    }
            //}


            //T.SaveTaskAim(任务列表);//保存任务配置
            //dataGridView1.DataSource = 任务列表;
            //MessageBox.Show("处理完成");
            #endregion
        }
    }
}
