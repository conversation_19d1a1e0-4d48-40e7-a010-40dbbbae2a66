﻿using Newtonsoft.Json;
using PetShikong;
using PetShikong.成就;
using System;
using System.Collections.Generic;
using System.Windows.Forms;

namespace Admin
{
    public partial class 管理称号 : Form
    {
    
        public 管理称号()
        {
            InitializeComponent();
        }

        private void 管理称号_Load(object sender, EventArgs e)
        {
            dataGridView1.Rows.Clear();
           
            数据处理.加载成就列表();
            foreach (var 成就 in 数据处理.成就规则)
            {
                string[] str = { 成就.称号,成就.默认属性.ToString(),成就.称号效果 };
                dataGridView1.Rows.Add(str);
            }
        }
        String 当前编辑成就 = "";
        private void dataGridView1_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            var i = e.RowIndex;
            var row = dataGridView1.CurrentRow;
            if (row != null) {
                dataGridView2.Rows.Clear();
                当前编辑成就 = row.Cells[0].Value.ToString();
                foreach (var 成就 in 数据处理.成就规则)
                {
                    if (成就.称号.Equals(row.Cells[0].Value.ToString()))
                    {
                       
                        foreach (var 卡牌 in 成就.卡牌数组) {
                            string[] str = { 卡牌.名称, 卡牌.图标, 卡牌.卡牌类型id,卡牌.数量.ToString() };
                            dataGridView2.Rows.Add(str);
                        }
                        

                    }
                   
                }
            }
        }

        private void button1_Click(object sender, EventArgs e)
        {
            for (int i = 0; i <= 数据处理.成就规则.Count-1; i++) {
                if (数据处理.成就规则[i].称号.Equals(当前编辑成就)) {
                    List<卡牌> 卡牌组 = new List<卡牌>();
                    for (int j = 0; j < dataGridView2.Rows.Count-1; j++) {
                        卡牌 卡牌 = new 卡牌();
                        卡牌.名称 = dataGridView2.Rows[j].Cells[0].Value.ToString();
                        卡牌.图标 = dataGridView2.Rows[j].Cells[1].Value.ToString();
                        卡牌.卡牌类型id= dataGridView2.Rows[j].Cells[2].Value.ToString();
                        卡牌.数量 = Convert.ToInt32(dataGridView2.Rows[j].Cells[3].Value);
                        卡牌组.Add(卡牌);
                    }
                    数据处理.成就规则[i].卡牌数组 = 卡牌组;
                          
                    
                }
            }
            new 数据处理().保存文件(JsonConvert.SerializeObject(数据处理.成就规则,Formatting.Indented),数据处理.成就规则路径);


        }
    }
    public class ComboData
    {
        private string m_display = string.Empty;
        private string m_value = string.Empty;
        public ComboData(string display, string value)
        {
            this.m_display = display;
            this.m_value = value;
        }
        public string Display
        {
            get { return this.m_display; }
            set { this.m_display = value; }
        }
        public string Value
        {
            get { return this.m_value; }
            set { this.m_value = value; }
        }
    }

}
