﻿namespace Shikong.Pokemon2.PCG
{
    public class TalismanProcess
    {
        /*//法宝脚本写法

        public enum 法宝效果
        {
            抵消,
            复活,
            属性,
            会心一击,
            收益增加,
            回复,
        }


        //抵消伤害类
        //抵消|概率|抵消量 如 抵消伤害|0.02|0.5

        //复活类
        //复活|概率|恢复量 如 复活|0.05|0.01 

        //属性类
        //属性|属性种类|增加量 如 属性|攻击|0.01
        
        //会心一击
        //会心一击|触发概率|伤害倍数 如 会心一击|0.01|2

        //收益增加类
        //收益增加|收益类型|增加量 如 收益增加|金币|0.01

        //回合结束时回复类
        //回复|回复类型|回复量 如 回复|HP|0.005

        //PS 宠物信息需要添加一个字段，在计算宠物属性时候赋值，表面法宝属性



        internal string Read_TI(string 法宝id, TalismanInfo 玩家法宝, string 宠物id = null)
        {


            TalismanConfig 法宝配置 = new TalismanConfig();



            //读取配置文件的就不写了,这里这样写只是为了不报错

            if (法宝配置 == null)
            {
                return null;
            }


            string introduction = "";
            introduction += 玩家法宝.Type;   //颜色、字号你调整
            introduction += 玩家法宝.Lv;
            introduction += 玩家法宝.Nimbus;




            string[] cfg = 法宝配置.Attribute.Split('|');

            string attribute = cfg[0];

            if (attribute == 法宝效果.抵消.ToString())
            {
                introduction += "宠物每次遭受攻击时，有" + Convert.ToDouble(cfg[1]) * 100 + "%几率抵消" +
                                Convert.ToDouble(cfg[2]) * 100 + "%伤害";
            }
            else if (attribute == 法宝效果.复活.ToString())
            {
                introduction += "宠物遭受致命伤害时，有" + Convert.ToDouble(cfg[1]) * 100 + "%几率复活，并恢复" +
                                Convert.ToDouble(cfg[2]) * 100 + "%最大生命值";
            }
            else if (attribute == 法宝效果.属性.ToString())
            {
                introduction += "增加宠物" + Convert.ToDouble(cfg[2]) * 100 + "%" + cfg[1];
            }
            else if (attribute == 法宝效果.会心一击.ToString())
            {
                introduction += "宠物每次攻击时，有" + Convert.ToDouble(cfg[1]) * 100 + "%几率造成" +
                                Convert.ToDouble(cfg[2]) * 100 + "%伤害";
            }
            else if (attribute == 法宝效果.收益增加.ToString())
            {
                introduction += "每次战斗胜利后，多获得" + Convert.ToDouble(cfg[2]) * 100 + "%" + cfg[1];
            }
            else if (attribute == 法宝效果.回复.ToString())
            {
                introduction += "每回合结束时，回复相当于宠物最大" + cfg[2] + Convert.ToDouble(cfg[1]) * 100 + "%的" + cfg[2];
            }
            else
            {
                return null;
            }



            introduction += 法宝配置.Introduction;

            return introduction;
        }








        internal static List<long> ExpList = new List<long>();
        internal static int GetLv(long exp)
        {
            if (ExpList == null || ExpList.Count == 0)
            {
                string cfg = new DataProcess().ReadFile(DataProcess.LDC_Path);
                cfg = RC4.DecryptRC4wq(cfg, new DataProcess().GetKey(1));
                var explist = cfg.Split(new[] { "\r\n" }, StringSplitOptions.None);

                long 当前经验 = 0;

                foreach (string jy in explist)
                {
                    long jyz = Convert.ToInt64(jy);
                    if (jyz < 49357) 
                    {
                        continue;
                    }
                    if (jyz >= 49357 && jyz <= 535308)
                    {
                        当前经验 = 当前经验 + Convert.ToInt64(jy);
                        ExpList.Add(当前经验);
                    }

                    if (jyz > 535308)
                    {
                        break;
                    }             
                }
                for (int i = 0; i < ExpList.Count; i++)
                {
                    if (exp < ExpList[i])
                    {
                        return i;
                    }
                    if (exp > ExpList[ExpList.Count - 1])
                    {
                        return 15;
                    }
                }
            }
            return 0;
        }*/
    }
}
