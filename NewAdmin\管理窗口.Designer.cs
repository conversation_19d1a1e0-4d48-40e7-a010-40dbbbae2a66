﻿namespace Admin
{
    partial class 管理
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(管理));
            this.comboBox1 = new System.Windows.Forms.ComboBox();
            this.textBox1 = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.地图ID = new System.Windows.Forms.TextBox();
            this.button1 = new System.Windows.Forms.Button();
            this.文件 = new System.Windows.Forms.TextBox();
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.tabPage7 = new System.Windows.Forms.TabPage();
            this.button34 = new System.Windows.Forms.Button();
            this.button32 = new System.Windows.Forms.Button();
            this.button31 = new System.Windows.Forms.Button();
            this.button30 = new System.Windows.Forms.Button();
            this.button21 = new System.Windows.Forms.Button();
            this.button20 = new System.Windows.Forms.Button();
            this.button19 = new System.Windows.Forms.Button();
            this.button18 = new System.Windows.Forms.Button();
            this.button25 = new System.Windows.Forms.Button();
            this.button24 = new System.Windows.Forms.Button();
            this.button23 = new System.Windows.Forms.Button();
            this.button16 = new System.Windows.Forms.Button();
            this.button15 = new System.Windows.Forms.Button();
            this.button14 = new System.Windows.Forms.Button();
            this.信息处理 = new System.Windows.Forms.TabPage();
            this.宠物列表_300 = new System.Windows.Forms.Button();
            this.宠物列表_280 = new System.Windows.Forms.Button();
            this.宠物列表_270 = new System.Windows.Forms.Button();
            this.道具配置 = new System.Windows.Forms.TabPage();
            this.道具分类 = new System.Windows.Forms.ComboBox();
            this.linkLabel14 = new System.Windows.Forms.LinkLabel();
            this.label65 = new System.Windows.Forms.Label();
            this.linkLabel13 = new System.Windows.Forms.LinkLabel();
            this.数量上限 = new System.Windows.Forms.TextBox();
            this.label26 = new System.Windows.Forms.Label();
            this.出售价格 = new System.Windows.Forms.TextBox();
            this.label66 = new System.Windows.Forms.Label();
            this.linkLabel11 = new System.Windows.Forms.LinkLabel();
            this.linkLabel10 = new System.Windows.Forms.LinkLabel();
            this.linkLabel2 = new System.Windows.Forms.LinkLabel();
            this.linkLabel1 = new System.Windows.Forms.LinkLabel();
            this.label6 = new System.Windows.Forms.Label();
            this.道具说明 = new System.Windows.Forms.TextBox();
            this.button2 = new System.Windows.Forms.Button();
            this.label5 = new System.Windows.Forms.Label();
            this.道具脚本 = new System.Windows.Forms.TextBox();
            this.label4 = new System.Windows.Forms.Label();
            this.道具序号 = new System.Windows.Forms.TextBox();
            this.label3 = new System.Windows.Forms.Label();
            this.道具图标 = new System.Windows.Forms.TextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.道具名字 = new System.Windows.Forms.TextBox();
            this.魔法卡配置 = new System.Windows.Forms.TabPage();
            this.添加魔法卡 = new System.Windows.Forms.Button();
            this.pictureBox2 = new System.Windows.Forms.PictureBox();
            this.卡片属性 = new System.Windows.Forms.ComboBox();
            this.label49 = new System.Windows.Forms.Label();
            this.卡片图片 = new System.Windows.Forms.TextBox();
            this.label50 = new System.Windows.Forms.Label();
            this.卡片属性数值 = new System.Windows.Forms.TextBox();
            this.label51 = new System.Windows.Forms.Label();
            this.label52 = new System.Windows.Forms.Label();
            this.卡片名字 = new System.Windows.Forms.TextBox();
            this.皮肤配置 = new System.Windows.Forms.TabPage();
            this.dataGridView_pifu = new System.Windows.Forms.DataGridView();
            this.皮肤名 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.形象ID = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.contextMenu_bs = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.读取ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.宝石管理 = new System.Windows.Forms.TabPage();
            this.bs_eType_str = new System.Windows.Forms.TextBox();
            this.bs_add = new System.Windows.Forms.Button();
            this.bs_dataList = new System.Windows.Forms.DataGridView();
            this.typeName = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.bs_eType = new System.Windows.Forms.ComboBox();
            this.bs_typeClass = new System.Windows.Forms.ComboBox();
            this.bs_upType = new System.Windows.Forms.ComboBox();
            this.bs_wxz = new System.Windows.Forms.TextBox();
            this.bs_color = new System.Windows.Forms.TextBox();
            this.bs_LV = new System.Windows.Forms.TextBox();
            this.bs_upNum = new System.Windows.Forms.TextBox();
            this.bs_typeName = new System.Windows.Forms.TextBox();
            this.bs_pid = new System.Windows.Forms.TextBox();
            this.label64 = new System.Windows.Forms.Label();
            this.label63 = new System.Windows.Forms.Label();
            this.label62 = new System.Windows.Forms.Label();
            this.label61 = new System.Windows.Forms.Label();
            this.label60 = new System.Windows.Forms.Label();
            this.label59 = new System.Windows.Forms.Label();
            this.label58 = new System.Windows.Forms.Label();
            this.label57 = new System.Windows.Forms.Label();
            this.label56 = new System.Windows.Forms.Label();
            this.道具存档 = new System.Windows.Forms.TabPage();
            this.button3 = new System.Windows.Forms.Button();
            this.linkLabel3 = new System.Windows.Forms.LinkLabel();
            this.label8 = new System.Windows.Forms.Label();
            this.添加道具数量 = new System.Windows.Forms.TextBox();
            this.label7 = new System.Windows.Forms.Label();
            this.道具序号2 = new System.Windows.Forms.TextBox();
            this.宠物存档 = new System.Windows.Forms.TabPage();
            this.宠物等级 = new System.Windows.Forms.NumericUpDown();
            this.label54 = new System.Windows.Forms.Label();
            this.pictureBox1 = new System.Windows.Forms.PictureBox();
            this.button4 = new System.Windows.Forms.Button();
            this.linkLabel4 = new System.Windows.Forms.LinkLabel();
            this.label9 = new System.Windows.Forms.Label();
            this.宠物成长 = new System.Windows.Forms.TextBox();
            this.label10 = new System.Windows.Forms.Label();
            this.宠物序号 = new System.Windows.Forms.TextBox();
            this.地图配置 = new System.Windows.Forms.TabPage();
            this.tabControl2 = new System.Windows.Forms.TabControl();
            this.tabPage2 = new System.Windows.Forms.TabPage();
            this.textBox6 = new System.Windows.Forms.TextBox();
            this.label45 = new System.Windows.Forms.Label();
            this.checkBox2 = new System.Windows.Forms.CheckBox();
            this.label44 = new System.Windows.Forms.Label();
            this.textBox5 = new System.Windows.Forms.TextBox();
            this.linkLabel5 = new System.Windows.Forms.LinkLabel();
            this.button5 = new System.Windows.Forms.Button();
            this.label17 = new System.Windows.Forms.Label();
            this.地图掉落最大数量 = new System.Windows.Forms.TextBox();
            this.label18 = new System.Windows.Forms.Label();
            this.地图掉落最小数量 = new System.Windows.Forms.TextBox();
            this.地图掉落列表 = new System.Windows.Forms.TextBox();
            this.label16 = new System.Windows.Forms.Label();
            this.label13 = new System.Windows.Forms.Label();
            this.地图最大元宝 = new System.Windows.Forms.TextBox();
            this.label15 = new System.Windows.Forms.Label();
            this.地图最小元宝 = new System.Windows.Forms.TextBox();
            this.label14 = new System.Windows.Forms.Label();
            this.地图最大金币 = new System.Windows.Forms.TextBox();
            this.label12 = new System.Windows.Forms.Label();
            this.地图最小金币 = new System.Windows.Forms.TextBox();
            this.label11 = new System.Windows.Forms.Label();
            this.地图序号 = new System.Windows.Forms.TextBox();
            this.tabPage3 = new System.Windows.Forms.TabPage();
            this.button6 = new System.Windows.Forms.Button();
            this.linkLabel9 = new System.Windows.Forms.LinkLabel();
            this.怪物经验 = new System.Windows.Forms.TextBox();
            this.label25 = new System.Windows.Forms.Label();
            this.button8 = new System.Windows.Forms.Button();
            this.linkLabel7 = new System.Windows.Forms.LinkLabel();
            this.linkLabel8 = new System.Windows.Forms.LinkLabel();
            this.button7 = new System.Windows.Forms.Button();
            this.怪物掉落 = new System.Windows.Forms.TextBox();
            this.label24 = new System.Windows.Forms.Label();
            this.怪物成长 = new System.Windows.Forms.TextBox();
            this.怪物最小等级 = new System.Windows.Forms.TextBox();
            this.怪物序号 = new System.Windows.Forms.TextBox();
            this.怪物最大掉落 = new System.Windows.Forms.TextBox();
            this.label23 = new System.Windows.Forms.Label();
            this.linkLabel6 = new System.Windows.Forms.LinkLabel();
            this.label22 = new System.Windows.Forms.Label();
            this.label20 = new System.Windows.Forms.Label();
            this.怪物最大等级 = new System.Windows.Forms.TextBox();
            this.label21 = new System.Windows.Forms.Label();
            this.label19 = new System.Windows.Forms.Label();
            this.哈希验证 = new System.Windows.Forms.TabPage();
            this.button33 = new System.Windows.Forms.Button();
            this.textBox9 = new System.Windows.Forms.TextBox();
            this.label55 = new System.Windows.Forms.Label();
            this.textBox3 = new System.Windows.Forms.TextBox();
            this.label47 = new System.Windows.Forms.Label();
            this.button28 = new System.Windows.Forms.Button();
            this.button11 = new System.Windows.Forms.Button();
            this.textBox2 = new System.Windows.Forms.TextBox();
            this.button10 = new System.Windows.Forms.Button();
            this.装备管理 = new System.Windows.Forms.TabPage();
            this.五行限制 = new System.Windows.Forms.ComboBox();
            this.label53 = new System.Windows.Forms.Label();
            this.textBox4 = new System.Windows.Forms.TextBox();
            this.label42 = new System.Windows.Forms.Label();
            this.button12 = new System.Windows.Forms.Button();
            this.主属性 = new System.Windows.Forms.ComboBox();
            this.label46 = new System.Windows.Forms.Label();
            this.装备类型 = new System.Windows.Forms.ComboBox();
            this.label40 = new System.Windows.Forms.Label();
            this.装备命中 = new System.Windows.Forms.TextBox();
            this.label43 = new System.Windows.Forms.Label();
            this.装备图标 = new System.Windows.Forms.TextBox();
            this.label39 = new System.Windows.Forms.Label();
            this.装备吸血 = new System.Windows.Forms.TextBox();
            this.label38 = new System.Windows.Forms.Label();
            this.装备加深 = new System.Windows.Forms.TextBox();
            this.装备吸魔 = new System.Windows.Forms.TextBox();
            this.label35 = new System.Windows.Forms.Label();
            this.label37 = new System.Windows.Forms.Label();
            this.装备抵消 = new System.Windows.Forms.TextBox();
            this.label36 = new System.Windows.Forms.Label();
            this.装备魔法 = new System.Windows.Forms.TextBox();
            this.label34 = new System.Windows.Forms.Label();
            this.装备生命 = new System.Windows.Forms.TextBox();
            this.label33 = new System.Windows.Forms.Label();
            this.装备闪避 = new System.Windows.Forms.TextBox();
            this.label32 = new System.Windows.Forms.Label();
            this.装备速度 = new System.Windows.Forms.TextBox();
            this.label31 = new System.Windows.Forms.Label();
            this.装备防御 = new System.Windows.Forms.TextBox();
            this.label30 = new System.Windows.Forms.Label();
            this.装备攻击 = new System.Windows.Forms.TextBox();
            this.label29 = new System.Windows.Forms.Label();
            this.装备名字 = new System.Windows.Forms.TextBox();
            this.label28 = new System.Windows.Forms.Label();
            this.装备_ID = new System.Windows.Forms.TextBox();
            this.label27 = new System.Windows.Forms.Label();
            this.装备存档 = new System.Windows.Forms.TabPage();
            this.存档装备等级 = new System.Windows.Forms.TextBox();
            this.label48 = new System.Windows.Forms.Label();
            this.button13 = new System.Windows.Forms.Button();
            this.linkLabel12 = new System.Windows.Forms.LinkLabel();
            this.label41 = new System.Windows.Forms.Label();
            this.存档装备ID = new System.Windows.Forms.TextBox();
            this.生成操作 = new System.Windows.Forms.TabPage();
            this.button17 = new System.Windows.Forms.Button();
            this.tabPage9 = new System.Windows.Forms.TabPage();
            this.button22 = new System.Windows.Forms.Button();
            this.textBox8 = new System.Windows.Forms.TextBox();
            this.button9 = new System.Windows.Forms.Button();
            this.textBox7 = new System.Windows.Forms.TextBox();
            this.search = new System.Windows.Forms.Button();
            this.editor = new System.Windows.Forms.Button();
            this.button26 = new System.Windows.Forms.Button();
            this.button27 = new System.Windows.Forms.Button();
            this.button29 = new System.Windows.Forms.Button();
            this.colorDialog1 = new System.Windows.Forms.ColorDialog();
            this.button35 = new System.Windows.Forms.Button();
            this.tabControl1.SuspendLayout();
            this.tabPage7.SuspendLayout();
            this.信息处理.SuspendLayout();
            this.道具配置.SuspendLayout();
            this.魔法卡配置.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox2)).BeginInit();
            this.皮肤配置.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView_pifu)).BeginInit();
            this.contextMenu_bs.SuspendLayout();
            this.宝石管理.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.bs_dataList)).BeginInit();
            this.道具存档.SuspendLayout();
            this.宠物存档.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.宠物等级)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).BeginInit();
            this.地图配置.SuspendLayout();
            this.tabControl2.SuspendLayout();
            this.tabPage2.SuspendLayout();
            this.tabPage3.SuspendLayout();
            this.哈希验证.SuspendLayout();
            this.装备管理.SuspendLayout();
            this.装备存档.SuspendLayout();
            this.生成操作.SuspendLayout();
            this.tabPage9.SuspendLayout();
            this.SuspendLayout();
            // 
            // comboBox1
            // 
            this.comboBox1.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBox1.FormattingEnabled = true;
            this.comboBox1.Items.AddRange(new object[] {
            "宠物配置",
            "怪物配置",
            "道具配置",
            "技能配置",
            "卡牌配置",
            "宝石配置",
            "人物存档",
            "宠物存档",
            "道具存档",
            "装备存档",
            "任务存档",
            "存档版本号",
            "副本进度存档",
            "地图定义存档",
            "地图怪物定义存档",
            "神兵设定配置",
            "魂器设定配置",
            "皮肤设定配置",
            "侍灵设定配置"});
            this.comboBox1.Location = new System.Drawing.Point(13, 15);
            this.comboBox1.Name = "comboBox1";
            this.comboBox1.Size = new System.Drawing.Size(188, 20);
            this.comboBox1.TabIndex = 0;
            this.comboBox1.SelectedIndexChanged += new System.EventHandler(this.comboBox1_SelectedIndexChanged);
            // 
            // textBox1
            // 
            this.textBox1.AllowDrop = true;
            this.textBox1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.textBox1.Font = new System.Drawing.Font("宋体", 15F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.textBox1.Location = new System.Drawing.Point(13, 38);
            this.textBox1.MaxLength = 99999999;
            this.textBox1.Multiline = true;
            this.textBox1.Name = "textBox1";
            this.textBox1.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.textBox1.Size = new System.Drawing.Size(464, 419);
            this.textBox1.TabIndex = 1;
            this.textBox1.TextChanged += new System.EventHandler(this.textBox1_TextChanged);
            this.textBox1.DragDrop += new System.Windows.Forms.DragEventHandler(this.textBox1_DragDrop);
            this.textBox1.DragEnter += new System.Windows.Forms.DragEventHandler(this.textBox1_DragEnter);
            // 
            // label1
            // 
            this.label1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(463, 16);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(47, 12);
            this.label1.TabIndex = 2;
            this.label1.Text = "地图ID:";
            // 
            // 地图ID
            // 
            this.地图ID.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.地图ID.Location = new System.Drawing.Point(514, 11);
            this.地图ID.Name = "地图ID";
            this.地图ID.Size = new System.Drawing.Size(39, 21);
            this.地图ID.TabIndex = 3;
            this.地图ID.Text = "1";
            // 
            // button1
            // 
            this.button1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.button1.Location = new System.Drawing.Point(15, 463);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(171, 49);
            this.button1.TabIndex = 4;
            this.button1.Text = "保存修改";
            this.button1.UseVisualStyleBackColor = true;
            this.button1.Click += new System.EventHandler(this.button1_Click);
            // 
            // 文件
            // 
            this.文件.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.文件.Location = new System.Drawing.Point(559, 11);
            this.文件.Name = "文件";
            this.文件.Size = new System.Drawing.Size(317, 21);
            this.文件.TabIndex = 5;
            // 
            // tabControl1
            // 
            this.tabControl1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tabControl1.Controls.Add(this.tabPage7);
            this.tabControl1.Controls.Add(this.信息处理);
            this.tabControl1.Controls.Add(this.道具配置);
            this.tabControl1.Controls.Add(this.魔法卡配置);
            this.tabControl1.Controls.Add(this.皮肤配置);
            this.tabControl1.Controls.Add(this.宝石管理);
            this.tabControl1.Controls.Add(this.道具存档);
            this.tabControl1.Controls.Add(this.宠物存档);
            this.tabControl1.Controls.Add(this.地图配置);
            this.tabControl1.Controls.Add(this.哈希验证);
            this.tabControl1.Controls.Add(this.装备管理);
            this.tabControl1.Controls.Add(this.装备存档);
            this.tabControl1.Controls.Add(this.生成操作);
            this.tabControl1.Controls.Add(this.tabPage9);
            this.tabControl1.Location = new System.Drawing.Point(483, 38);
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new System.Drawing.Size(397, 474);
            this.tabControl1.TabIndex = 6;
            this.tabControl1.SelectedIndexChanged += new System.EventHandler(this.tabControl1_SelectedIndexChanged);
            this.tabControl1.Selected += new System.Windows.Forms.TabControlEventHandler(this.tabControl1_Selected);
            this.tabControl1.TabIndexChanged += new System.EventHandler(this.tabControl1_TabIndexChanged);
            // 
            // tabPage7
            // 
            this.tabPage7.Controls.Add(this.button35);
            this.tabPage7.Controls.Add(this.button34);
            this.tabPage7.Controls.Add(this.button32);
            this.tabPage7.Controls.Add(this.button31);
            this.tabPage7.Controls.Add(this.button30);
            this.tabPage7.Controls.Add(this.button21);
            this.tabPage7.Controls.Add(this.button20);
            this.tabPage7.Controls.Add(this.button19);
            this.tabPage7.Controls.Add(this.button18);
            this.tabPage7.Controls.Add(this.button25);
            this.tabPage7.Controls.Add(this.button24);
            this.tabPage7.Controls.Add(this.button23);
            this.tabPage7.Controls.Add(this.button16);
            this.tabPage7.Controls.Add(this.button15);
            this.tabPage7.Controls.Add(this.button14);
            this.tabPage7.Location = new System.Drawing.Point(4, 22);
            this.tabPage7.Name = "tabPage7";
            this.tabPage7.Size = new System.Drawing.Size(389, 448);
            this.tabPage7.TabIndex = 8;
            this.tabPage7.Text = "其他功能";
            this.tabPage7.UseVisualStyleBackColor = true;
            // 
            // button34
            // 
            this.button34.Location = new System.Drawing.Point(258, 172);
            this.button34.Name = "button34";
            this.button34.Size = new System.Drawing.Size(124, 43);
            this.button34.TabIndex = 7;
            this.button34.Text = "更新版本号";
            this.button34.UseVisualStyleBackColor = true;
            this.button34.Click += new System.EventHandler(this.button34_Click);
            // 
            // button32
            // 
            this.button32.Location = new System.Drawing.Point(8, 328);
            this.button32.Name = "button32";
            this.button32.Size = new System.Drawing.Size(124, 43);
            this.button32.TabIndex = 6;
            this.button32.Text = "修改主程序名称";
            this.button32.UseVisualStyleBackColor = true;
            this.button32.Click += new System.EventHandler(this.button32_Click);
            // 
            // button31
            // 
            this.button31.Location = new System.Drawing.Point(8, 377);
            this.button31.Name = "button31";
            this.button31.Size = new System.Drawing.Size(211, 47);
            this.button31.TabIndex = 5;
            this.button31.Text = "格式化JSON";
            this.button31.UseVisualStyleBackColor = true;
            this.button31.Click += new System.EventHandler(this.button31_Click);
            // 
            // button30
            // 
            this.button30.Location = new System.Drawing.Point(133, 172);
            this.button30.Name = "button30";
            this.button30.Size = new System.Drawing.Size(124, 43);
            this.button30.TabIndex = 4;
            this.button30.Text = "修改绑定为本机";
            this.button30.UseVisualStyleBackColor = true;
            this.button30.Click += new System.EventHandler(this.button30_Click);
            // 
            // button21
            // 
            this.button21.Location = new System.Drawing.Point(133, 74);
            this.button21.Name = "button21";
            this.button21.Size = new System.Drawing.Size(124, 43);
            this.button21.TabIndex = 4;
            this.button21.Text = "管理地图";
            this.button21.UseVisualStyleBackColor = true;
            this.button21.Click += new System.EventHandler(this.button21_Click);
            // 
            // button20
            // 
            this.button20.Location = new System.Drawing.Point(133, 25);
            this.button20.Name = "button20";
            this.button20.Size = new System.Drawing.Size(124, 43);
            this.button20.TabIndex = 3;
            this.button20.Text = "实例测试键";
            this.button20.UseVisualStyleBackColor = true;
            this.button20.Click += new System.EventHandler(this.button20_Click);
            // 
            // button19
            // 
            this.button19.Location = new System.Drawing.Point(8, 172);
            this.button19.Name = "button19";
            this.button19.Size = new System.Drawing.Size(124, 43);
            this.button19.TabIndex = 2;
            this.button19.Text = "装备管理";
            this.button19.UseVisualStyleBackColor = true;
            this.button19.Click += new System.EventHandler(this.button19_Click);
            // 
            // button18
            // 
            this.button18.Location = new System.Drawing.Point(258, 123);
            this.button18.Name = "button18";
            this.button18.Size = new System.Drawing.Size(124, 43);
            this.button18.TabIndex = 1;
            this.button18.Text = "管理宠物合成路线";
            this.button18.UseVisualStyleBackColor = true;
            this.button18.Click += new System.EventHandler(this.button18_Click_1);
            // 
            // button25
            // 
            this.button25.Location = new System.Drawing.Point(258, 74);
            this.button25.Name = "button25";
            this.button25.Size = new System.Drawing.Size(124, 43);
            this.button25.TabIndex = 0;
            this.button25.Text = "成就管理";
            this.button25.UseVisualStyleBackColor = true;
            this.button25.Click += new System.EventHandler(this.button25_Click);
            // 
            // button24
            // 
            this.button24.Location = new System.Drawing.Point(258, 25);
            this.button24.Name = "button24";
            this.button24.Size = new System.Drawing.Size(124, 43);
            this.button24.TabIndex = 0;
            this.button24.Text = "成就管理";
            this.button24.UseVisualStyleBackColor = true;
            this.button24.Click += new System.EventHandler(this.button24_Click);
            // 
            // button23
            // 
            this.button23.Location = new System.Drawing.Point(133, 123);
            this.button23.Name = "button23";
            this.button23.Size = new System.Drawing.Size(124, 43);
            this.button23.TabIndex = 0;
            this.button23.Text = "编辑道具JSON";
            this.button23.UseVisualStyleBackColor = true;
            this.button23.Click += new System.EventHandler(this.button23_Click);
            // 
            // button16
            // 
            this.button16.Location = new System.Drawing.Point(8, 123);
            this.button16.Name = "button16";
            this.button16.Size = new System.Drawing.Size(124, 43);
            this.button16.TabIndex = 0;
            this.button16.Text = "增加套装";
            this.button16.UseVisualStyleBackColor = true;
            this.button16.Click += new System.EventHandler(this.button16_Click);
            // 
            // button15
            // 
            this.button15.Location = new System.Drawing.Point(8, 74);
            this.button15.Name = "button15";
            this.button15.Size = new System.Drawing.Size(124, 43);
            this.button15.TabIndex = 0;
            this.button15.Text = "管理任务";
            this.button15.UseVisualStyleBackColor = true;
            this.button15.Click += new System.EventHandler(this.button15_Click);
            // 
            // button14
            // 
            this.button14.Location = new System.Drawing.Point(8, 25);
            this.button14.Name = "button14";
            this.button14.Size = new System.Drawing.Size(124, 43);
            this.button14.TabIndex = 0;
            this.button14.Text = "管理宠物进化路线";
            this.button14.UseVisualStyleBackColor = true;
            this.button14.Click += new System.EventHandler(this.button14_Click);
            // 
            // 信息处理
            // 
            this.信息处理.Controls.Add(this.宠物列表_300);
            this.信息处理.Controls.Add(this.宠物列表_280);
            this.信息处理.Controls.Add(this.宠物列表_270);
            this.信息处理.Location = new System.Drawing.Point(4, 22);
            this.信息处理.Name = "信息处理";
            this.信息处理.Padding = new System.Windows.Forms.Padding(3);
            this.信息处理.Size = new System.Drawing.Size(389, 448);
            this.信息处理.TabIndex = 13;
            this.信息处理.Text = "信息处理";
            this.信息处理.UseVisualStyleBackColor = true;
            // 
            // 宠物列表_300
            // 
            this.宠物列表_300.Location = new System.Drawing.Point(258, 21);
            this.宠物列表_300.Name = "宠物列表_300";
            this.宠物列表_300.Size = new System.Drawing.Size(120, 33);
            this.宠物列表_300.TabIndex = 2;
            this.宠物列表_300.Text = "复制300宠物列表";
            this.宠物列表_300.UseVisualStyleBackColor = true;
            this.宠物列表_300.Click += new System.EventHandler(this.宠物列表_300_Click);
            // 
            // 宠物列表_280
            // 
            this.宠物列表_280.Location = new System.Drawing.Point(132, 21);
            this.宠物列表_280.Name = "宠物列表_280";
            this.宠物列表_280.Size = new System.Drawing.Size(120, 33);
            this.宠物列表_280.TabIndex = 1;
            this.宠物列表_280.Text = "复制280宠物列表";
            this.宠物列表_280.UseVisualStyleBackColor = true;
            this.宠物列表_280.Click += new System.EventHandler(this.宠物列表_280_Click);
            // 
            // 宠物列表_270
            // 
            this.宠物列表_270.Location = new System.Drawing.Point(6, 21);
            this.宠物列表_270.Name = "宠物列表_270";
            this.宠物列表_270.Size = new System.Drawing.Size(120, 33);
            this.宠物列表_270.TabIndex = 0;
            this.宠物列表_270.Text = "复制270宠物列表";
            this.宠物列表_270.UseVisualStyleBackColor = true;
            this.宠物列表_270.Click += new System.EventHandler(this.宠物列表_270_Click);
            // 
            // 道具配置
            // 
            this.道具配置.Controls.Add(this.道具分类);
            this.道具配置.Controls.Add(this.linkLabel14);
            this.道具配置.Controls.Add(this.label65);
            this.道具配置.Controls.Add(this.linkLabel13);
            this.道具配置.Controls.Add(this.数量上限);
            this.道具配置.Controls.Add(this.label26);
            this.道具配置.Controls.Add(this.出售价格);
            this.道具配置.Controls.Add(this.label66);
            this.道具配置.Controls.Add(this.linkLabel11);
            this.道具配置.Controls.Add(this.linkLabel10);
            this.道具配置.Controls.Add(this.linkLabel2);
            this.道具配置.Controls.Add(this.linkLabel1);
            this.道具配置.Controls.Add(this.label6);
            this.道具配置.Controls.Add(this.道具说明);
            this.道具配置.Controls.Add(this.button2);
            this.道具配置.Controls.Add(this.label5);
            this.道具配置.Controls.Add(this.道具脚本);
            this.道具配置.Controls.Add(this.label4);
            this.道具配置.Controls.Add(this.道具序号);
            this.道具配置.Controls.Add(this.label3);
            this.道具配置.Controls.Add(this.道具图标);
            this.道具配置.Controls.Add(this.label2);
            this.道具配置.Controls.Add(this.道具名字);
            this.道具配置.Location = new System.Drawing.Point(4, 22);
            this.道具配置.Name = "道具配置";
            this.道具配置.Size = new System.Drawing.Size(389, 448);
            this.道具配置.TabIndex = 0;
            this.道具配置.Text = "道具配置";
            this.道具配置.UseVisualStyleBackColor = true;
            this.道具配置.Click += new System.EventHandler(this.道具配置_Click);
            // 
            // 道具分类
            // 
            this.道具分类.FormattingEnabled = true;
            this.道具分类.Items.AddRange(new object[] {
            "其他",
            "礼包",
            "消耗",
            "特殊",
            "收集",
            "地图钥匙",
            "进化材料",
            "技能书",
            "战斗捕捉",
            "宠物合成",
            "魔法卡牌",
            "皮肤",
            "宠物卵",
            "图纸",
            "活动道具",
            "涅槃类材料",
            "强化辅助",
            "岁月蚀刻"});
            this.道具分类.Location = new System.Drawing.Point(278, 64);
            this.道具分类.Name = "道具分类";
            this.道具分类.Size = new System.Drawing.Size(87, 20);
            this.道具分类.TabIndex = 52;
            // 
            // linkLabel14
            // 
            this.linkLabel14.AutoSize = true;
            this.linkLabel14.Location = new System.Drawing.Point(38, 256);
            this.linkLabel14.Name = "linkLabel14";
            this.linkLabel14.Size = new System.Drawing.Size(53, 12);
            this.linkLabel14.TabIndex = 44;
            this.linkLabel14.TabStop = true;
            this.linkLabel14.Text = "选择装备";
            this.linkLabel14.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.linkLabel14_LinkClicked);
            // 
            // label65
            // 
            this.label65.AutoSize = true;
            this.label65.Location = new System.Drawing.Point(208, 92);
            this.label65.Name = "label65";
            this.label65.Size = new System.Drawing.Size(59, 12);
            this.label65.TabIndex = 48;
            this.label65.Text = "数量上限:";
            // 
            // linkLabel13
            // 
            this.linkLabel13.AutoSize = true;
            this.linkLabel13.Location = new System.Drawing.Point(38, 171);
            this.linkLabel13.Name = "linkLabel13";
            this.linkLabel13.Size = new System.Drawing.Size(53, 12);
            this.linkLabel13.TabIndex = 43;
            this.linkLabel13.TabStop = true;
            this.linkLabel13.Text = "选择装备";
            this.linkLabel13.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.linkLabel13_LinkClicked);
            // 
            // 数量上限
            // 
            this.数量上限.Location = new System.Drawing.Point(278, 87);
            this.数量上限.Name = "数量上限";
            this.数量上限.Size = new System.Drawing.Size(87, 21);
            this.数量上限.TabIndex = 47;
            // 
            // label26
            // 
            this.label26.AutoSize = true;
            this.label26.Location = new System.Drawing.Point(17, 92);
            this.label26.Name = "label26";
            this.label26.Size = new System.Drawing.Size(59, 12);
            this.label26.TabIndex = 42;
            this.label26.Text = "出售价格:";
            // 
            // 出售价格
            // 
            this.出售价格.Location = new System.Drawing.Point(87, 87);
            this.出售价格.Name = "出售价格";
            this.出售价格.Size = new System.Drawing.Size(108, 21);
            this.出售价格.TabIndex = 41;
            // 
            // label66
            // 
            this.label66.AutoSize = true;
            this.label66.Location = new System.Drawing.Point(208, 67);
            this.label66.Name = "label66";
            this.label66.Size = new System.Drawing.Size(59, 12);
            this.label66.TabIndex = 46;
            this.label66.Text = "道具分类:";
            // 
            // linkLabel11
            // 
            this.linkLabel11.AutoSize = true;
            this.linkLabel11.Location = new System.Drawing.Point(38, 239);
            this.linkLabel11.Name = "linkLabel11";
            this.linkLabel11.Size = new System.Drawing.Size(53, 12);
            this.linkLabel11.TabIndex = 40;
            this.linkLabel11.TabStop = true;
            this.linkLabel11.Text = "选择宠物";
            this.linkLabel11.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.linkLabel11_LinkClicked);
            // 
            // linkLabel10
            // 
            this.linkLabel10.AutoSize = true;
            this.linkLabel10.Location = new System.Drawing.Point(38, 154);
            this.linkLabel10.Name = "linkLabel10";
            this.linkLabel10.Size = new System.Drawing.Size(53, 12);
            this.linkLabel10.TabIndex = 39;
            this.linkLabel10.TabStop = true;
            this.linkLabel10.Text = "选择宠物";
            this.linkLabel10.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.linkLabel10_LinkClicked);
            // 
            // linkLabel2
            // 
            this.linkLabel2.AutoSize = true;
            this.linkLabel2.Location = new System.Drawing.Point(38, 220);
            this.linkLabel2.Name = "linkLabel2";
            this.linkLabel2.Size = new System.Drawing.Size(53, 12);
            this.linkLabel2.TabIndex = 12;
            this.linkLabel2.TabStop = true;
            this.linkLabel2.Text = "选择道具";
            this.linkLabel2.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.linkLabel2_LinkClicked);
            // 
            // linkLabel1
            // 
            this.linkLabel1.AutoSize = true;
            this.linkLabel1.Location = new System.Drawing.Point(38, 135);
            this.linkLabel1.Name = "linkLabel1";
            this.linkLabel1.Size = new System.Drawing.Size(53, 12);
            this.linkLabel1.TabIndex = 11;
            this.linkLabel1.TabStop = true;
            this.linkLabel1.Text = "选择道具";
            this.linkLabel1.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.linkLabel1_LinkClicked);
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(38, 197);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(59, 12);
            this.label6.TabIndex = 10;
            this.label6.Text = "道具说明:";
            // 
            // 道具说明
            // 
            this.道具说明.Location = new System.Drawing.Point(108, 196);
            this.道具说明.Multiline = true;
            this.道具说明.Name = "道具说明";
            this.道具说明.Size = new System.Drawing.Size(271, 204);
            this.道具说明.TabIndex = 9;
            // 
            // button2
            // 
            this.button2.Location = new System.Drawing.Point(128, 406);
            this.button2.Name = "button2";
            this.button2.Size = new System.Drawing.Size(194, 35);
            this.button2.TabIndex = 8;
            this.button2.Text = "添加道具";
            this.button2.UseVisualStyleBackColor = true;
            this.button2.Click += new System.EventHandler(this.button2_Click);
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(38, 117);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(59, 12);
            this.label5.TabIndex = 7;
            this.label5.Text = "道具脚本:";
            // 
            // 道具脚本
            // 
            this.道具脚本.Location = new System.Drawing.Point(108, 116);
            this.道具脚本.Multiline = true;
            this.道具脚本.Name = "道具脚本";
            this.道具脚本.Size = new System.Drawing.Size(271, 73);
            this.道具脚本.TabIndex = 6;
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(17, 69);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(59, 12);
            this.label4.TabIndex = 5;
            this.label4.Text = "道具序号:";
            // 
            // 道具序号
            // 
            this.道具序号.Location = new System.Drawing.Point(87, 64);
            this.道具序号.Name = "道具序号";
            this.道具序号.Size = new System.Drawing.Size(108, 21);
            this.道具序号.TabIndex = 4;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(17, 44);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(59, 12);
            this.label3.TabIndex = 3;
            this.label3.Text = "道具图标:";
            // 
            // 道具图标
            // 
            this.道具图标.Location = new System.Drawing.Point(87, 39);
            this.道具图标.Name = "道具图标";
            this.道具图标.Size = new System.Drawing.Size(127, 21);
            this.道具图标.TabIndex = 2;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(17, 17);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(59, 12);
            this.label2.TabIndex = 1;
            this.label2.Text = "道具名字:";
            // 
            // 道具名字
            // 
            this.道具名字.Location = new System.Drawing.Point(87, 12);
            this.道具名字.Name = "道具名字";
            this.道具名字.Size = new System.Drawing.Size(127, 21);
            this.道具名字.TabIndex = 0;
            // 
            // 魔法卡配置
            // 
            this.魔法卡配置.Controls.Add(this.添加魔法卡);
            this.魔法卡配置.Controls.Add(this.pictureBox2);
            this.魔法卡配置.Controls.Add(this.卡片属性);
            this.魔法卡配置.Controls.Add(this.label49);
            this.魔法卡配置.Controls.Add(this.卡片图片);
            this.魔法卡配置.Controls.Add(this.label50);
            this.魔法卡配置.Controls.Add(this.卡片属性数值);
            this.魔法卡配置.Controls.Add(this.label51);
            this.魔法卡配置.Controls.Add(this.label52);
            this.魔法卡配置.Controls.Add(this.卡片名字);
            this.魔法卡配置.Location = new System.Drawing.Point(4, 22);
            this.魔法卡配置.Name = "魔法卡配置";
            this.魔法卡配置.Padding = new System.Windows.Forms.Padding(3);
            this.魔法卡配置.Size = new System.Drawing.Size(389, 448);
            this.魔法卡配置.TabIndex = 11;
            this.魔法卡配置.Text = "魔法卡配置";
            this.魔法卡配置.UseVisualStyleBackColor = true;
            // 
            // 添加魔法卡
            // 
            this.添加魔法卡.Location = new System.Drawing.Point(252, 72);
            this.添加魔法卡.Name = "添加魔法卡";
            this.添加魔法卡.Size = new System.Drawing.Size(103, 36);
            this.添加魔法卡.TabIndex = 53;
            this.添加魔法卡.Text = "添加魔法卡";
            this.添加魔法卡.UseVisualStyleBackColor = true;
            this.添加魔法卡.Click += new System.EventHandler(this.添加魔法卡_Click);
            // 
            // pictureBox2
            // 
            this.pictureBox2.Location = new System.Drawing.Point(95, 133);
            this.pictureBox2.Name = "pictureBox2";
            this.pictureBox2.Size = new System.Drawing.Size(134, 190);
            this.pictureBox2.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom;
            this.pictureBox2.TabIndex = 52;
            this.pictureBox2.TabStop = false;
            // 
            // 卡片属性
            // 
            this.卡片属性.FormattingEnabled = true;
            this.卡片属性.Items.AddRange(new object[] {
            "攻击",
            "命中",
            "防御",
            "生命",
            "闪避",
            "速度",
            "魔法",
            "金币上限",
            "元宝上限",
            "水晶上限",
            "赫拉神殿小怪额外伤害",
            "赫拉神殿BOSS额外伤害",
            "必遇BOSS次数减少",
            "转换战斗胜利经验",
            "批量开包",
            "追龙任务",
            "自动涅槃间隔减短"});
            this.卡片属性.Location = new System.Drawing.Point(95, 39);
            this.卡片属性.Name = "卡片属性";
            this.卡片属性.Size = new System.Drawing.Size(134, 20);
            this.卡片属性.TabIndex = 51;
            // 
            // label49
            // 
            this.label49.AutoSize = true;
            this.label49.Location = new System.Drawing.Point(25, 96);
            this.label49.Name = "label49";
            this.label49.Size = new System.Drawing.Size(59, 12);
            this.label49.TabIndex = 50;
            this.label49.Text = "卡片图片:";
            // 
            // 卡片图片
            // 
            this.卡片图片.Location = new System.Drawing.Point(95, 92);
            this.卡片图片.Name = "卡片图片";
            this.卡片图片.Size = new System.Drawing.Size(134, 21);
            this.卡片图片.TabIndex = 49;
            this.卡片图片.Leave += new System.EventHandler(this.卡片图片_Leave);
            // 
            // label50
            // 
            this.label50.AutoSize = true;
            this.label50.Location = new System.Drawing.Point(25, 69);
            this.label50.Name = "label50";
            this.label50.Size = new System.Drawing.Size(59, 12);
            this.label50.TabIndex = 48;
            this.label50.Text = "属性数值:";
            // 
            // 卡片属性数值
            // 
            this.卡片属性数值.Location = new System.Drawing.Point(95, 64);
            this.卡片属性数值.Name = "卡片属性数值";
            this.卡片属性数值.Size = new System.Drawing.Size(134, 21);
            this.卡片属性数值.TabIndex = 47;
            // 
            // label51
            // 
            this.label51.AutoSize = true;
            this.label51.Location = new System.Drawing.Point(25, 44);
            this.label51.Name = "label51";
            this.label51.Size = new System.Drawing.Size(59, 12);
            this.label51.TabIndex = 46;
            this.label51.Text = "卡片属性:";
            // 
            // label52
            // 
            this.label52.AutoSize = true;
            this.label52.Location = new System.Drawing.Point(25, 17);
            this.label52.Name = "label52";
            this.label52.Size = new System.Drawing.Size(59, 12);
            this.label52.TabIndex = 44;
            this.label52.Text = "卡片名字:";
            // 
            // 卡片名字
            // 
            this.卡片名字.Location = new System.Drawing.Point(95, 12);
            this.卡片名字.Name = "卡片名字";
            this.卡片名字.Size = new System.Drawing.Size(134, 21);
            this.卡片名字.TabIndex = 43;
            // 
            // 皮肤配置
            // 
            this.皮肤配置.Controls.Add(this.dataGridView_pifu);
            this.皮肤配置.Location = new System.Drawing.Point(4, 22);
            this.皮肤配置.Name = "皮肤配置";
            this.皮肤配置.Padding = new System.Windows.Forms.Padding(3);
            this.皮肤配置.Size = new System.Drawing.Size(389, 448);
            this.皮肤配置.TabIndex = 14;
            this.皮肤配置.Text = "皮肤配置";
            this.皮肤配置.UseVisualStyleBackColor = true;
            // 
            // dataGridView_pifu
            // 
            this.dataGridView_pifu.AllowUserToAddRows = false;
            this.dataGridView_pifu.AllowUserToDeleteRows = false;
            this.dataGridView_pifu.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridView_pifu.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.皮肤名,
            this.形象ID});
            this.dataGridView_pifu.ContextMenuStrip = this.contextMenu_bs;
            this.dataGridView_pifu.Location = new System.Drawing.Point(6, 6);
            this.dataGridView_pifu.MultiSelect = false;
            this.dataGridView_pifu.Name = "dataGridView_pifu";
            this.dataGridView_pifu.ReadOnly = true;
            this.dataGridView_pifu.RowHeadersVisible = false;
            this.dataGridView_pifu.RowHeadersWidth = 82;
            this.dataGridView_pifu.RowTemplate.Height = 23;
            this.dataGridView_pifu.ShowRowErrors = false;
            this.dataGridView_pifu.Size = new System.Drawing.Size(377, 147);
            this.dataGridView_pifu.TabIndex = 20;
            // 
            // 皮肤名
            // 
            this.皮肤名.HeaderText = "皮肤名";
            this.皮肤名.MinimumWidth = 10;
            this.皮肤名.Name = "皮肤名";
            this.皮肤名.ReadOnly = true;
            this.皮肤名.Width = 170;
            // 
            // 形象ID
            // 
            this.形象ID.HeaderText = "形象ID";
            this.形象ID.Name = "形象ID";
            this.形象ID.ReadOnly = true;
            // 
            // contextMenu_bs
            // 
            this.contextMenu_bs.ImageScalingSize = new System.Drawing.Size(32, 32);
            this.contextMenu_bs.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.读取ToolStripMenuItem});
            this.contextMenu_bs.Name = "contextMenu_bs";
            this.contextMenu_bs.Size = new System.Drawing.Size(101, 26);
            // 
            // 读取ToolStripMenuItem
            // 
            this.读取ToolStripMenuItem.Name = "读取ToolStripMenuItem";
            this.读取ToolStripMenuItem.Size = new System.Drawing.Size(100, 22);
            this.读取ToolStripMenuItem.Text = "读取";
            this.读取ToolStripMenuItem.Click += new System.EventHandler(this.读取ToolStripMenuItem_Click);
            // 
            // 宝石管理
            // 
            this.宝石管理.Controls.Add(this.bs_eType_str);
            this.宝石管理.Controls.Add(this.bs_add);
            this.宝石管理.Controls.Add(this.bs_dataList);
            this.宝石管理.Controls.Add(this.bs_eType);
            this.宝石管理.Controls.Add(this.bs_typeClass);
            this.宝石管理.Controls.Add(this.bs_upType);
            this.宝石管理.Controls.Add(this.bs_wxz);
            this.宝石管理.Controls.Add(this.bs_color);
            this.宝石管理.Controls.Add(this.bs_LV);
            this.宝石管理.Controls.Add(this.bs_upNum);
            this.宝石管理.Controls.Add(this.bs_typeName);
            this.宝石管理.Controls.Add(this.bs_pid);
            this.宝石管理.Controls.Add(this.label64);
            this.宝石管理.Controls.Add(this.label63);
            this.宝石管理.Controls.Add(this.label62);
            this.宝石管理.Controls.Add(this.label61);
            this.宝石管理.Controls.Add(this.label60);
            this.宝石管理.Controls.Add(this.label59);
            this.宝石管理.Controls.Add(this.label58);
            this.宝石管理.Controls.Add(this.label57);
            this.宝石管理.Controls.Add(this.label56);
            this.宝石管理.Location = new System.Drawing.Point(4, 22);
            this.宝石管理.Name = "宝石管理";
            this.宝石管理.Padding = new System.Windows.Forms.Padding(3);
            this.宝石管理.Size = new System.Drawing.Size(389, 448);
            this.宝石管理.TabIndex = 12;
            this.宝石管理.Text = "宝石管理";
            this.宝石管理.UseVisualStyleBackColor = true;
            // 
            // bs_eType_str
            // 
            this.bs_eType_str.Location = new System.Drawing.Point(68, 250);
            this.bs_eType_str.Name = "bs_eType_str";
            this.bs_eType_str.Size = new System.Drawing.Size(100, 21);
            this.bs_eType_str.TabIndex = 21;
            // 
            // bs_add
            // 
            this.bs_add.Location = new System.Drawing.Point(68, 321);
            this.bs_add.Margin = new System.Windows.Forms.Padding(2);
            this.bs_add.Name = "bs_add";
            this.bs_add.Size = new System.Drawing.Size(98, 36);
            this.bs_add.TabIndex = 20;
            this.bs_add.Text = "添加";
            this.bs_add.UseVisualStyleBackColor = true;
            this.bs_add.Click += new System.EventHandler(this.bs_add_Click);
            // 
            // bs_dataList
            // 
            this.bs_dataList.AllowUserToAddRows = false;
            this.bs_dataList.AllowUserToDeleteRows = false;
            this.bs_dataList.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.bs_dataList.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.typeName});
            this.bs_dataList.ContextMenuStrip = this.contextMenu_bs;
            this.bs_dataList.Location = new System.Drawing.Point(183, 26);
            this.bs_dataList.MultiSelect = false;
            this.bs_dataList.Name = "bs_dataList";
            this.bs_dataList.ReadOnly = true;
            this.bs_dataList.RowHeadersVisible = false;
            this.bs_dataList.RowHeadersWidth = 82;
            this.bs_dataList.RowTemplate.Height = 23;
            this.bs_dataList.ShowRowErrors = false;
            this.bs_dataList.Size = new System.Drawing.Size(186, 247);
            this.bs_dataList.TabIndex = 19;
            // 
            // typeName
            // 
            this.typeName.HeaderText = "宝石名称";
            this.typeName.MinimumWidth = 10;
            this.typeName.Name = "typeName";
            this.typeName.ReadOnly = true;
            this.typeName.Width = 170;
            // 
            // bs_eType
            // 
            this.bs_eType.FormattingEnabled = true;
            this.bs_eType.Items.AddRange(new object[] {
            "身体",
            "头部",
            "项链",
            "武器",
            "手镯",
            "脚部",
            "戒指",
            "翅膀",
            "宝石",
            "道具",
            "卡牌左",
            "卡牌右",
            "法宝",
            "灵饰"});
            this.bs_eType.Location = new System.Drawing.Point(68, 222);
            this.bs_eType.Name = "bs_eType";
            this.bs_eType.Size = new System.Drawing.Size(100, 20);
            this.bs_eType.TabIndex = 18;
            this.bs_eType.SelectedIndexChanged += new System.EventHandler(this.bs_eType_SelectedIndexChanged);
            // 
            // bs_typeClass
            // 
            this.bs_typeClass.FormattingEnabled = true;
            this.bs_typeClass.Location = new System.Drawing.Point(68, 195);
            this.bs_typeClass.Name = "bs_typeClass";
            this.bs_typeClass.Size = new System.Drawing.Size(100, 20);
            this.bs_typeClass.TabIndex = 17;
            // 
            // bs_upType
            // 
            this.bs_upType.FormattingEnabled = true;
            this.bs_upType.Items.AddRange(new object[] {
            "攻击",
            "防御",
            "速度",
            "闪避",
            "生命",
            "魔法",
            "加深",
            "抵消",
            "吸血",
            "吸墨",
            "命中"});
            this.bs_upType.Location = new System.Drawing.Point(68, 82);
            this.bs_upType.Name = "bs_upType";
            this.bs_upType.Size = new System.Drawing.Size(100, 20);
            this.bs_upType.TabIndex = 16;
            // 
            // bs_wxz
            // 
            this.bs_wxz.Location = new System.Drawing.Point(68, 283);
            this.bs_wxz.Name = "bs_wxz";
            this.bs_wxz.Size = new System.Drawing.Size(100, 21);
            this.bs_wxz.TabIndex = 15;
            // 
            // bs_color
            // 
            this.bs_color.Location = new System.Drawing.Point(68, 164);
            this.bs_color.Name = "bs_color";
            this.bs_color.Size = new System.Drawing.Size(100, 21);
            this.bs_color.TabIndex = 14;
            // 
            // bs_LV
            // 
            this.bs_LV.Location = new System.Drawing.Point(68, 137);
            this.bs_LV.Name = "bs_LV";
            this.bs_LV.Size = new System.Drawing.Size(100, 21);
            this.bs_LV.TabIndex = 13;
            // 
            // bs_upNum
            // 
            this.bs_upNum.Location = new System.Drawing.Point(68, 110);
            this.bs_upNum.Name = "bs_upNum";
            this.bs_upNum.Size = new System.Drawing.Size(100, 21);
            this.bs_upNum.TabIndex = 12;
            // 
            // bs_typeName
            // 
            this.bs_typeName.Location = new System.Drawing.Point(68, 54);
            this.bs_typeName.Name = "bs_typeName";
            this.bs_typeName.Size = new System.Drawing.Size(100, 21);
            this.bs_typeName.TabIndex = 10;
            // 
            // bs_pid
            // 
            this.bs_pid.Location = new System.Drawing.Point(68, 26);
            this.bs_pid.Name = "bs_pid";
            this.bs_pid.Size = new System.Drawing.Size(100, 21);
            this.bs_pid.TabIndex = 9;
            // 
            // label64
            // 
            this.label64.AutoSize = true;
            this.label64.Location = new System.Drawing.Point(9, 286);
            this.label64.Name = "label64";
            this.label64.Size = new System.Drawing.Size(53, 12);
            this.label64.TabIndex = 8;
            this.label64.Text = "打孔位置";
            // 
            // label63
            // 
            this.label63.AutoSize = true;
            this.label63.Location = new System.Drawing.Point(9, 225);
            this.label63.Name = "label63";
            this.label63.Size = new System.Drawing.Size(53, 12);
            this.label63.TabIndex = 7;
            this.label63.Text = "镶嵌部位";
            // 
            // label62
            // 
            this.label62.AutoSize = true;
            this.label62.Cursor = System.Windows.Forms.Cursors.Hand;
            this.label62.ForeColor = System.Drawing.Color.DeepSkyBlue;
            this.label62.Location = new System.Drawing.Point(9, 169);
            this.label62.Name = "label62";
            this.label62.Size = new System.Drawing.Size(53, 12);
            this.label62.TabIndex = 6;
            this.label62.Text = "宝石颜色";
            this.label62.Click += new System.EventHandler(this.label62_Click);
            // 
            // label61
            // 
            this.label61.AutoSize = true;
            this.label61.Location = new System.Drawing.Point(9, 198);
            this.label61.Name = "label61";
            this.label61.Size = new System.Drawing.Size(53, 12);
            this.label61.TabIndex = 5;
            this.label61.Text = "宝石分类";
            // 
            // label60
            // 
            this.label60.AutoSize = true;
            this.label60.Location = new System.Drawing.Point(9, 113);
            this.label60.Name = "label60";
            this.label60.Size = new System.Drawing.Size(53, 12);
            this.label60.TabIndex = 4;
            this.label60.Text = "提升数值";
            // 
            // label59
            // 
            this.label59.AutoSize = true;
            this.label59.Location = new System.Drawing.Point(21, 29);
            this.label59.Name = "label59";
            this.label59.Size = new System.Drawing.Size(41, 12);
            this.label59.TabIndex = 3;
            this.label59.Text = "道具ID";
            // 
            // label58
            // 
            this.label58.AutoSize = true;
            this.label58.Location = new System.Drawing.Point(9, 141);
            this.label58.Name = "label58";
            this.label58.Size = new System.Drawing.Size(53, 12);
            this.label58.TabIndex = 2;
            this.label58.Text = "宝石等级";
            // 
            // label57
            // 
            this.label57.AutoSize = true;
            this.label57.Location = new System.Drawing.Point(9, 85);
            this.label57.Name = "label57";
            this.label57.Size = new System.Drawing.Size(53, 12);
            this.label57.TabIndex = 1;
            this.label57.Text = "属性类型";
            // 
            // label56
            // 
            this.label56.AutoSize = true;
            this.label56.Location = new System.Drawing.Point(9, 57);
            this.label56.Name = "label56";
            this.label56.Size = new System.Drawing.Size(53, 12);
            this.label56.TabIndex = 0;
            this.label56.Text = "宝石名称";
            // 
            // 道具存档
            // 
            this.道具存档.Controls.Add(this.button3);
            this.道具存档.Controls.Add(this.linkLabel3);
            this.道具存档.Controls.Add(this.label8);
            this.道具存档.Controls.Add(this.添加道具数量);
            this.道具存档.Controls.Add(this.label7);
            this.道具存档.Controls.Add(this.道具序号2);
            this.道具存档.Location = new System.Drawing.Point(4, 22);
            this.道具存档.Name = "道具存档";
            this.道具存档.Size = new System.Drawing.Size(389, 448);
            this.道具存档.TabIndex = 2;
            this.道具存档.Text = "道具存档";
            this.道具存档.UseVisualStyleBackColor = true;
            // 
            // button3
            // 
            this.button3.Location = new System.Drawing.Point(44, 107);
            this.button3.Name = "button3";
            this.button3.Size = new System.Drawing.Size(194, 35);
            this.button3.TabIndex = 13;
            this.button3.Text = "添加道具";
            this.button3.UseVisualStyleBackColor = true;
            this.button3.Click += new System.EventHandler(this.button3_Click);
            // 
            // linkLabel3
            // 
            this.linkLabel3.AutoSize = true;
            this.linkLabel3.Location = new System.Drawing.Point(223, 28);
            this.linkLabel3.Name = "linkLabel3";
            this.linkLabel3.Size = new System.Drawing.Size(53, 12);
            this.linkLabel3.TabIndex = 12;
            this.linkLabel3.TabStop = true;
            this.linkLabel3.Text = "选择道具";
            this.linkLabel3.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.linkLabel3_LinkClicked);
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(12, 64);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(59, 12);
            this.label8.TabIndex = 9;
            this.label8.Text = "添加数量:";
            // 
            // 添加道具数量
            // 
            this.添加道具数量.Location = new System.Drawing.Point(81, 59);
            this.添加道具数量.Name = "添加道具数量";
            this.添加道具数量.Size = new System.Drawing.Size(134, 21);
            this.添加道具数量.TabIndex = 8;
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(12, 28);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(59, 12);
            this.label7.TabIndex = 7;
            this.label7.Text = "道具序号:";
            this.label7.Click += new System.EventHandler(this.label7_Click);
            // 
            // 道具序号2
            // 
            this.道具序号2.Location = new System.Drawing.Point(81, 23);
            this.道具序号2.Name = "道具序号2";
            this.道具序号2.Size = new System.Drawing.Size(134, 21);
            this.道具序号2.TabIndex = 6;
            // 
            // 宠物存档
            // 
            this.宠物存档.Controls.Add(this.宠物等级);
            this.宠物存档.Controls.Add(this.label54);
            this.宠物存档.Controls.Add(this.pictureBox1);
            this.宠物存档.Controls.Add(this.button4);
            this.宠物存档.Controls.Add(this.linkLabel4);
            this.宠物存档.Controls.Add(this.label9);
            this.宠物存档.Controls.Add(this.宠物成长);
            this.宠物存档.Controls.Add(this.label10);
            this.宠物存档.Controls.Add(this.宠物序号);
            this.宠物存档.Location = new System.Drawing.Point(4, 22);
            this.宠物存档.Name = "宠物存档";
            this.宠物存档.Size = new System.Drawing.Size(389, 448);
            this.宠物存档.TabIndex = 3;
            this.宠物存档.Text = "宠物存档";
            this.宠物存档.UseVisualStyleBackColor = true;
            // 
            // 宠物等级
            // 
            this.宠物等级.Location = new System.Drawing.Point(81, 102);
            this.宠物等级.Margin = new System.Windows.Forms.Padding(2);
            this.宠物等级.Maximum = new decimal(new int[] {
            130,
            0,
            0,
            0});
            this.宠物等级.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.宠物等级.Name = "宠物等级";
            this.宠物等级.Size = new System.Drawing.Size(80, 21);
            this.宠物等级.TabIndex = 23;
            this.宠物等级.Value = new decimal(new int[] {
            130,
            0,
            0,
            0});
            // 
            // label54
            // 
            this.label54.AutoSize = true;
            this.label54.Location = new System.Drawing.Point(12, 107);
            this.label54.Name = "label54";
            this.label54.Size = new System.Drawing.Size(59, 12);
            this.label54.TabIndex = 22;
            this.label54.Text = "宠物等级:";
            // 
            // pictureBox1
            // 
            this.pictureBox1.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom;
            this.pictureBox1.InitialImage = ((System.Drawing.Image)(resources.GetObject("pictureBox1.InitialImage")));
            this.pictureBox1.Location = new System.Drawing.Point(25, 181);
            this.pictureBox1.Name = "pictureBox1";
            this.pictureBox1.Size = new System.Drawing.Size(270, 201);
            this.pictureBox1.TabIndex = 20;
            this.pictureBox1.TabStop = false;
            // 
            // button4
            // 
            this.button4.Location = new System.Drawing.Point(47, 142);
            this.button4.Name = "button4";
            this.button4.Size = new System.Drawing.Size(194, 35);
            this.button4.TabIndex = 19;
            this.button4.Text = "添加宠物";
            this.button4.UseVisualStyleBackColor = true;
            this.button4.Click += new System.EventHandler(this.button4_Click);
            // 
            // linkLabel4
            // 
            this.linkLabel4.AutoSize = true;
            this.linkLabel4.Location = new System.Drawing.Point(223, 39);
            this.linkLabel4.Name = "linkLabel4";
            this.linkLabel4.Size = new System.Drawing.Size(53, 12);
            this.linkLabel4.TabIndex = 18;
            this.linkLabel4.TabStop = true;
            this.linkLabel4.Text = "选择宠物";
            this.linkLabel4.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.linkLabel4_LinkClicked);
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(12, 75);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(59, 12);
            this.label9.TabIndex = 17;
            this.label9.Text = "宠物成长:";
            // 
            // 宠物成长
            // 
            this.宠物成长.Location = new System.Drawing.Point(81, 70);
            this.宠物成长.Name = "宠物成长";
            this.宠物成长.Size = new System.Drawing.Size(134, 21);
            this.宠物成长.TabIndex = 16;
            this.宠物成长.TextChanged += new System.EventHandler(this.宠物成长_TextChanged);
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(12, 39);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(59, 12);
            this.label10.TabIndex = 15;
            this.label10.Text = "宠物序号:";
            // 
            // 宠物序号
            // 
            this.宠物序号.Location = new System.Drawing.Point(81, 34);
            this.宠物序号.Name = "宠物序号";
            this.宠物序号.Size = new System.Drawing.Size(134, 21);
            this.宠物序号.TabIndex = 14;
            this.宠物序号.Leave += new System.EventHandler(this.宠物序号_Leave);
            // 
            // 地图配置
            // 
            this.地图配置.Controls.Add(this.tabControl2);
            this.地图配置.Location = new System.Drawing.Point(4, 22);
            this.地图配置.Name = "地图配置";
            this.地图配置.Size = new System.Drawing.Size(389, 448);
            this.地图配置.TabIndex = 4;
            this.地图配置.Text = "地图配置";
            this.地图配置.UseVisualStyleBackColor = true;
            // 
            // tabControl2
            // 
            this.tabControl2.Controls.Add(this.tabPage2);
            this.tabControl2.Controls.Add(this.tabPage3);
            this.tabControl2.Location = new System.Drawing.Point(3, 3);
            this.tabControl2.Name = "tabControl2";
            this.tabControl2.SelectedIndex = 0;
            this.tabControl2.Size = new System.Drawing.Size(281, 312);
            this.tabControl2.TabIndex = 0;
            // 
            // tabPage2
            // 
            this.tabPage2.Controls.Add(this.textBox6);
            this.tabPage2.Controls.Add(this.label45);
            this.tabPage2.Controls.Add(this.checkBox2);
            this.tabPage2.Controls.Add(this.label44);
            this.tabPage2.Controls.Add(this.textBox5);
            this.tabPage2.Controls.Add(this.linkLabel5);
            this.tabPage2.Controls.Add(this.button5);
            this.tabPage2.Controls.Add(this.label17);
            this.tabPage2.Controls.Add(this.地图掉落最大数量);
            this.tabPage2.Controls.Add(this.label18);
            this.tabPage2.Controls.Add(this.地图掉落最小数量);
            this.tabPage2.Controls.Add(this.地图掉落列表);
            this.tabPage2.Controls.Add(this.label16);
            this.tabPage2.Controls.Add(this.label13);
            this.tabPage2.Controls.Add(this.地图最大元宝);
            this.tabPage2.Controls.Add(this.label15);
            this.tabPage2.Controls.Add(this.地图最小元宝);
            this.tabPage2.Controls.Add(this.label14);
            this.tabPage2.Controls.Add(this.地图最大金币);
            this.tabPage2.Controls.Add(this.label12);
            this.tabPage2.Controls.Add(this.地图最小金币);
            this.tabPage2.Controls.Add(this.label11);
            this.tabPage2.Controls.Add(this.地图序号);
            this.tabPage2.Location = new System.Drawing.Point(4, 22);
            this.tabPage2.Name = "tabPage2";
            this.tabPage2.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage2.Size = new System.Drawing.Size(273, 286);
            this.tabPage2.TabIndex = 0;
            this.tabPage2.Text = "增加地图";
            this.tabPage2.UseVisualStyleBackColor = true;
            this.tabPage2.Click += new System.EventHandler(this.tabPage2_Click);
            // 
            // textBox6
            // 
            this.textBox6.Location = new System.Drawing.Point(209, 17);
            this.textBox6.Name = "textBox6";
            this.textBox6.Size = new System.Drawing.Size(41, 21);
            this.textBox6.TabIndex = 41;
            this.textBox6.Text = "600";
            // 
            // label45
            // 
            this.label45.AutoSize = true;
            this.label45.Location = new System.Drawing.Point(155, 20);
            this.label45.Name = "label45";
            this.label45.Size = new System.Drawing.Size(59, 12);
            this.label45.TabIndex = 42;
            this.label45.Text = "开启费用:";
            // 
            // checkBox2
            // 
            this.checkBox2.AutoSize = true;
            this.checkBox2.Location = new System.Drawing.Point(183, 158);
            this.checkBox2.Name = "checkBox2";
            this.checkBox2.Size = new System.Drawing.Size(48, 16);
            this.checkBox2.TabIndex = 40;
            this.checkBox2.Text = "副本";
            this.checkBox2.UseVisualStyleBackColor = true;
            // 
            // label44
            // 
            this.label44.AutoSize = true;
            this.label44.Location = new System.Drawing.Point(25, 161);
            this.label44.Name = "label44";
            this.label44.Size = new System.Drawing.Size(59, 12);
            this.label44.TabIndex = 39;
            this.label44.Text = "地图背景:";
            // 
            // textBox5
            // 
            this.textBox5.Location = new System.Drawing.Point(94, 156);
            this.textBox5.Name = "textBox5";
            this.textBox5.Size = new System.Drawing.Size(58, 21);
            this.textBox5.TabIndex = 38;
            this.textBox5.Text = "1.gif";
            // 
            // linkLabel5
            // 
            this.linkLabel5.AutoSize = true;
            this.linkLabel5.Location = new System.Drawing.Point(23, 124);
            this.linkLabel5.Name = "linkLabel5";
            this.linkLabel5.Size = new System.Drawing.Size(53, 12);
            this.linkLabel5.TabIndex = 37;
            this.linkLabel5.TabStop = true;
            this.linkLabel5.Text = "选择道具";
            this.linkLabel5.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.linkLabel5_LinkClicked);
            // 
            // button5
            // 
            this.button5.Location = new System.Drawing.Point(23, 218);
            this.button5.Name = "button5";
            this.button5.Size = new System.Drawing.Size(227, 63);
            this.button5.TabIndex = 36;
            this.button5.Text = "增加地图配置";
            this.button5.UseVisualStyleBackColor = true;
            this.button5.Click += new System.EventHandler(this.button5_Click);
            // 
            // label17
            // 
            this.label17.AutoSize = true;
            this.label17.Location = new System.Drawing.Point(158, 188);
            this.label17.Name = "label17";
            this.label17.Size = new System.Drawing.Size(17, 12);
            this.label17.TabIndex = 35;
            this.label17.Text = "到";
            // 
            // 地图掉落最大数量
            // 
            this.地图掉落最大数量.Location = new System.Drawing.Point(182, 184);
            this.地图掉落最大数量.Name = "地图掉落最大数量";
            this.地图掉落最大数量.Size = new System.Drawing.Size(58, 21);
            this.地图掉落最大数量.TabIndex = 34;
            // 
            // label18
            // 
            this.label18.AutoSize = true;
            this.label18.Location = new System.Drawing.Point(25, 188);
            this.label18.Name = "label18";
            this.label18.Size = new System.Drawing.Size(59, 12);
            this.label18.TabIndex = 33;
            this.label18.Text = "掉落数量:";
            // 
            // 地图掉落最小数量
            // 
            this.地图掉落最小数量.Location = new System.Drawing.Point(94, 184);
            this.地图掉落最小数量.Name = "地图掉落最小数量";
            this.地图掉落最小数量.Size = new System.Drawing.Size(58, 21);
            this.地图掉落最小数量.TabIndex = 32;
            // 
            // 地图掉落列表
            // 
            this.地图掉落列表.Location = new System.Drawing.Point(93, 99);
            this.地图掉落列表.Multiline = true;
            this.地图掉落列表.Name = "地图掉落列表";
            this.地图掉落列表.Size = new System.Drawing.Size(147, 51);
            this.地图掉落列表.TabIndex = 31;
            // 
            // label16
            // 
            this.label16.AutoSize = true;
            this.label16.Location = new System.Drawing.Point(23, 102);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(59, 12);
            this.label16.TabIndex = 30;
            this.label16.Text = "掉落道具:";
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(158, 76);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(17, 12);
            this.label13.TabIndex = 29;
            this.label13.Text = "到";
            // 
            // 地图最大元宝
            // 
            this.地图最大元宝.Location = new System.Drawing.Point(182, 72);
            this.地图最大元宝.Name = "地图最大元宝";
            this.地图最大元宝.Size = new System.Drawing.Size(58, 21);
            this.地图最大元宝.TabIndex = 28;
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(49, 77);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(35, 12);
            this.label15.TabIndex = 27;
            this.label15.Text = "元宝:";
            // 
            // 地图最小元宝
            // 
            this.地图最小元宝.Location = new System.Drawing.Point(94, 72);
            this.地图最小元宝.Name = "地图最小元宝";
            this.地图最小元宝.Size = new System.Drawing.Size(58, 21);
            this.地图最小元宝.TabIndex = 26;
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(158, 48);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(17, 12);
            this.label14.TabIndex = 25;
            this.label14.Text = "到";
            // 
            // 地图最大金币
            // 
            this.地图最大金币.Location = new System.Drawing.Point(182, 44);
            this.地图最大金币.Name = "地图最大金币";
            this.地图最大金币.Size = new System.Drawing.Size(58, 21);
            this.地图最大金币.TabIndex = 24;
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(49, 49);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(35, 12);
            this.label12.TabIndex = 21;
            this.label12.Text = "金币:";
            // 
            // 地图最小金币
            // 
            this.地图最小金币.Location = new System.Drawing.Point(94, 44);
            this.地图最小金币.Name = "地图最小金币";
            this.地图最小金币.Size = new System.Drawing.Size(58, 21);
            this.地图最小金币.TabIndex = 20;
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(24, 22);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(59, 12);
            this.label11.TabIndex = 19;
            this.label11.Text = "地图序号:";
            // 
            // 地图序号
            // 
            this.地图序号.Location = new System.Drawing.Point(93, 17);
            this.地图序号.Name = "地图序号";
            this.地图序号.Size = new System.Drawing.Size(58, 21);
            this.地图序号.TabIndex = 18;
            // 
            // tabPage3
            // 
            this.tabPage3.Controls.Add(this.button6);
            this.tabPage3.Controls.Add(this.linkLabel9);
            this.tabPage3.Controls.Add(this.怪物经验);
            this.tabPage3.Controls.Add(this.label25);
            this.tabPage3.Controls.Add(this.button8);
            this.tabPage3.Controls.Add(this.linkLabel7);
            this.tabPage3.Controls.Add(this.linkLabel8);
            this.tabPage3.Controls.Add(this.button7);
            this.tabPage3.Controls.Add(this.怪物掉落);
            this.tabPage3.Controls.Add(this.label24);
            this.tabPage3.Controls.Add(this.怪物成长);
            this.tabPage3.Controls.Add(this.怪物最小等级);
            this.tabPage3.Controls.Add(this.怪物序号);
            this.tabPage3.Controls.Add(this.怪物最大掉落);
            this.tabPage3.Controls.Add(this.label23);
            this.tabPage3.Controls.Add(this.linkLabel6);
            this.tabPage3.Controls.Add(this.label22);
            this.tabPage3.Controls.Add(this.label20);
            this.tabPage3.Controls.Add(this.怪物最大等级);
            this.tabPage3.Controls.Add(this.label21);
            this.tabPage3.Controls.Add(this.label19);
            this.tabPage3.Location = new System.Drawing.Point(4, 22);
            this.tabPage3.Name = "tabPage3";
            this.tabPage3.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage3.Size = new System.Drawing.Size(273, 286);
            this.tabPage3.TabIndex = 1;
            this.tabPage3.Text = "增加怪物";
            this.tabPage3.UseVisualStyleBackColor = true;
            this.tabPage3.Click += new System.EventHandler(this.tabPage3_Click);
            // 
            // button6
            // 
            this.button6.Location = new System.Drawing.Point(2, 0);
            this.button6.Name = "button6";
            this.button6.Size = new System.Drawing.Size(272, 283);
            this.button6.TabIndex = 51;
            this.button6.Text = "创建";
            this.button6.UseVisualStyleBackColor = true;
            this.button6.Click += new System.EventHandler(this.button6_Click);
            // 
            // linkLabel9
            // 
            this.linkLabel9.AutoSize = true;
            this.linkLabel9.Location = new System.Drawing.Point(7, 109);
            this.linkLabel9.Name = "linkLabel9";
            this.linkLabel9.Size = new System.Drawing.Size(53, 12);
            this.linkLabel9.TabIndex = 50;
            this.linkLabel9.TabStop = true;
            this.linkLabel9.Text = "选择道具";
            this.linkLabel9.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.linkLabel9_LinkClicked);
            // 
            // 怪物经验
            // 
            this.怪物经验.Location = new System.Drawing.Point(44, 63);
            this.怪物经验.Name = "怪物经验";
            this.怪物经验.Size = new System.Drawing.Size(58, 21);
            this.怪物经验.TabIndex = 48;
            // 
            // label25
            // 
            this.label25.AutoSize = true;
            this.label25.Location = new System.Drawing.Point(9, 67);
            this.label25.Name = "label25";
            this.label25.Size = new System.Drawing.Size(35, 12);
            this.label25.TabIndex = 49;
            this.label25.Text = "经验:";
            // 
            // button8
            // 
            this.button8.Location = new System.Drawing.Point(54, 178);
            this.button8.Name = "button8";
            this.button8.Size = new System.Drawing.Size(148, 40);
            this.button8.TabIndex = 47;
            this.button8.Text = "重置";
            this.button8.UseVisualStyleBackColor = true;
            this.button8.Click += new System.EventHandler(this.button8_Click);
            // 
            // linkLabel7
            // 
            this.linkLabel7.AutoSize = true;
            this.linkLabel7.Location = new System.Drawing.Point(31, 149);
            this.linkLabel7.Name = "linkLabel7";
            this.linkLabel7.Size = new System.Drawing.Size(53, 12);
            this.linkLabel7.TabIndex = 46;
            this.linkLabel7.TabStop = true;
            this.linkLabel7.Text = "加入列表";
            this.linkLabel7.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.linkLabel7_LinkClicked);
            // 
            // linkLabel8
            // 
            this.linkLabel8.AutoSize = true;
            this.linkLabel8.Location = new System.Drawing.Point(146, 149);
            this.linkLabel8.Name = "linkLabel8";
            this.linkLabel8.Size = new System.Drawing.Size(101, 12);
            this.linkLabel8.TabIndex = 46;
            this.linkLabel8.TabStop = true;
            this.linkLabel8.Text = "查看已加入的怪物";
            this.linkLabel8.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.linkLabel8_LinkClicked);
            // 
            // button7
            // 
            this.button7.Location = new System.Drawing.Point(22, 224);
            this.button7.Name = "button7";
            this.button7.Size = new System.Drawing.Size(233, 49);
            this.button7.TabIndex = 44;
            this.button7.Text = "保存配置";
            this.button7.UseVisualStyleBackColor = true;
            this.button7.Click += new System.EventHandler(this.button7_Click);
            // 
            // 怪物掉落
            // 
            this.怪物掉落.Location = new System.Drawing.Point(68, 90);
            this.怪物掉落.Multiline = true;
            this.怪物掉落.Name = "怪物掉落";
            this.怪物掉落.Size = new System.Drawing.Size(189, 47);
            this.怪物掉落.TabIndex = 42;
            // 
            // label24
            // 
            this.label24.AutoSize = true;
            this.label24.Location = new System.Drawing.Point(9, 90);
            this.label24.Name = "label24";
            this.label24.Size = new System.Drawing.Size(59, 12);
            this.label24.TabIndex = 41;
            this.label24.Text = "掉落道具:";
            // 
            // 怪物成长
            // 
            this.怪物成长.Location = new System.Drawing.Point(199, 10);
            this.怪物成长.Name = "怪物成长";
            this.怪物成长.Size = new System.Drawing.Size(58, 21);
            this.怪物成长.TabIndex = 30;
            // 
            // 怪物最小等级
            // 
            this.怪物最小等级.Location = new System.Drawing.Point(45, 37);
            this.怪物最小等级.Name = "怪物最小等级";
            this.怪物最小等级.Size = new System.Drawing.Size(32, 21);
            this.怪物最小等级.TabIndex = 26;
            // 
            // 怪物序号
            // 
            this.怪物序号.Location = new System.Drawing.Point(45, 10);
            this.怪物序号.Name = "怪物序号";
            this.怪物序号.Size = new System.Drawing.Size(58, 21);
            this.怪物序号.TabIndex = 20;
            this.怪物序号.TextChanged += new System.EventHandler(this.怪物序号_TextChanged);
            // 
            // 怪物最大掉落
            // 
            this.怪物最大掉落.Location = new System.Drawing.Point(199, 37);
            this.怪物最大掉落.Name = "怪物最大掉落";
            this.怪物最大掉落.Size = new System.Drawing.Size(58, 21);
            this.怪物最大掉落.TabIndex = 39;
            this.怪物最大掉落.Text = "0";
            // 
            // label23
            // 
            this.label23.AutoSize = true;
            this.label23.Location = new System.Drawing.Point(138, 41);
            this.label23.Name = "label23";
            this.label23.Size = new System.Drawing.Size(59, 12);
            this.label23.TabIndex = 40;
            this.label23.Text = "最大掉落:";
            // 
            // linkLabel6
            // 
            this.linkLabel6.AutoSize = true;
            this.linkLabel6.Location = new System.Drawing.Point(115, 14);
            this.linkLabel6.Name = "linkLabel6";
            this.linkLabel6.Size = new System.Drawing.Size(29, 12);
            this.linkLabel6.TabIndex = 38;
            this.linkLabel6.TabStop = true;
            this.linkLabel6.Text = "选择";
            this.linkLabel6.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.linkLabel6_LinkClicked);
            // 
            // label22
            // 
            this.label22.AutoSize = true;
            this.label22.Location = new System.Drawing.Point(164, 14);
            this.label22.Name = "label22";
            this.label22.Size = new System.Drawing.Size(35, 12);
            this.label22.TabIndex = 31;
            this.label22.Text = "成长:";
            // 
            // label20
            // 
            this.label20.AutoSize = true;
            this.label20.Location = new System.Drawing.Point(79, 41);
            this.label20.Name = "label20";
            this.label20.Size = new System.Drawing.Size(17, 12);
            this.label20.TabIndex = 29;
            this.label20.Text = "到";
            // 
            // 怪物最大等级
            // 
            this.怪物最大等级.Location = new System.Drawing.Point(100, 37);
            this.怪物最大等级.Name = "怪物最大等级";
            this.怪物最大等级.Size = new System.Drawing.Size(32, 21);
            this.怪物最大等级.TabIndex = 28;
            // 
            // label21
            // 
            this.label21.AutoSize = true;
            this.label21.Location = new System.Drawing.Point(9, 42);
            this.label21.Name = "label21";
            this.label21.Size = new System.Drawing.Size(35, 12);
            this.label21.TabIndex = 27;
            this.label21.Text = "等级:";
            // 
            // label19
            // 
            this.label19.AutoSize = true;
            this.label19.Location = new System.Drawing.Point(10, 14);
            this.label19.Name = "label19";
            this.label19.Size = new System.Drawing.Size(35, 12);
            this.label19.TabIndex = 21;
            this.label19.Text = "序号:";
            // 
            // 哈希验证
            // 
            this.哈希验证.Controls.Add(this.button33);
            this.哈希验证.Controls.Add(this.textBox9);
            this.哈希验证.Controls.Add(this.label55);
            this.哈希验证.Controls.Add(this.textBox3);
            this.哈希验证.Controls.Add(this.label47);
            this.哈希验证.Controls.Add(this.button28);
            this.哈希验证.Controls.Add(this.button11);
            this.哈希验证.Controls.Add(this.textBox2);
            this.哈希验证.Controls.Add(this.button10);
            this.哈希验证.Location = new System.Drawing.Point(4, 22);
            this.哈希验证.Margin = new System.Windows.Forms.Padding(2);
            this.哈希验证.Name = "哈希验证";
            this.哈希验证.Padding = new System.Windows.Forms.Padding(2);
            this.哈希验证.Size = new System.Drawing.Size(389, 448);
            this.哈希验证.TabIndex = 5;
            this.哈希验证.Text = "哈希验证";
            this.哈希验证.UseVisualStyleBackColor = true;
            // 
            // button33
            // 
            this.button33.Location = new System.Drawing.Point(279, 316);
            this.button33.Margin = new System.Windows.Forms.Padding(2);
            this.button33.Name = "button33";
            this.button33.Size = new System.Drawing.Size(93, 35);
            this.button33.TabIndex = 15;
            this.button33.Text = "单取";
            this.button33.UseVisualStyleBackColor = true;
            this.button33.Click += new System.EventHandler(this.button33_Click);
            // 
            // textBox9
            // 
            this.textBox9.Location = new System.Drawing.Point(60, 321);
            this.textBox9.Name = "textBox9";
            this.textBox9.Size = new System.Drawing.Size(214, 21);
            this.textBox9.TabIndex = 14;
            // 
            // label55
            // 
            this.label55.AutoSize = true;
            this.label55.Location = new System.Drawing.Point(15, 325);
            this.label55.Name = "label55";
            this.label55.Size = new System.Drawing.Size(41, 12);
            this.label55.TabIndex = 13;
            this.label55.Text = "HASH：";
            // 
            // textBox3
            // 
            this.textBox3.Location = new System.Drawing.Point(73, 62);
            this.textBox3.Name = "textBox3";
            this.textBox3.Size = new System.Drawing.Size(83, 21);
            this.textBox3.TabIndex = 12;
            // 
            // label47
            // 
            this.label47.AutoSize = true;
            this.label47.Location = new System.Drawing.Point(28, 66);
            this.label47.Name = "label47";
            this.label47.Size = new System.Drawing.Size(53, 12);
            this.label47.TabIndex = 11;
            this.label47.Text = "版本号：";
            // 
            // button28
            // 
            this.button28.Location = new System.Drawing.Point(240, 23);
            this.button28.Margin = new System.Windows.Forms.Padding(2);
            this.button28.Name = "button28";
            this.button28.Size = new System.Drawing.Size(90, 35);
            this.button28.TabIndex = 10;
            this.button28.Text = "生成下载哈希";
            this.button28.UseVisualStyleBackColor = true;
            this.button28.Click += new System.EventHandler(this.button28_Click);
            // 
            // button11
            // 
            this.button11.Location = new System.Drawing.Point(134, 23);
            this.button11.Margin = new System.Windows.Forms.Padding(2);
            this.button11.Name = "button11";
            this.button11.Size = new System.Drawing.Size(90, 35);
            this.button11.TabIndex = 10;
            this.button11.Text = "生成宠物哈希";
            this.button11.UseVisualStyleBackColor = true;
            this.button11.Click += new System.EventHandler(this.button11_Click);
            // 
            // textBox2
            // 
            this.textBox2.AllowDrop = true;
            this.textBox2.Location = new System.Drawing.Point(15, 89);
            this.textBox2.MaxLength = 99999999;
            this.textBox2.Multiline = true;
            this.textBox2.Name = "textBox2";
            this.textBox2.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.textBox2.Size = new System.Drawing.Size(259, 226);
            this.textBox2.TabIndex = 8;
            // 
            // button10
            // 
            this.button10.Location = new System.Drawing.Point(15, 23);
            this.button10.Margin = new System.Windows.Forms.Padding(2);
            this.button10.Name = "button10";
            this.button10.Size = new System.Drawing.Size(93, 35);
            this.button10.TabIndex = 0;
            this.button10.Text = "生成哈希配置";
            this.button10.UseVisualStyleBackColor = true;
            this.button10.Click += new System.EventHandler(this.button10_Click);
            // 
            // 装备管理
            // 
            this.装备管理.Controls.Add(this.五行限制);
            this.装备管理.Controls.Add(this.label53);
            this.装备管理.Controls.Add(this.textBox4);
            this.装备管理.Controls.Add(this.label42);
            this.装备管理.Controls.Add(this.button12);
            this.装备管理.Controls.Add(this.主属性);
            this.装备管理.Controls.Add(this.label46);
            this.装备管理.Controls.Add(this.装备类型);
            this.装备管理.Controls.Add(this.label40);
            this.装备管理.Controls.Add(this.装备命中);
            this.装备管理.Controls.Add(this.label43);
            this.装备管理.Controls.Add(this.装备图标);
            this.装备管理.Controls.Add(this.label39);
            this.装备管理.Controls.Add(this.装备吸血);
            this.装备管理.Controls.Add(this.label38);
            this.装备管理.Controls.Add(this.装备加深);
            this.装备管理.Controls.Add(this.装备吸魔);
            this.装备管理.Controls.Add(this.label35);
            this.装备管理.Controls.Add(this.label37);
            this.装备管理.Controls.Add(this.装备抵消);
            this.装备管理.Controls.Add(this.label36);
            this.装备管理.Controls.Add(this.装备魔法);
            this.装备管理.Controls.Add(this.label34);
            this.装备管理.Controls.Add(this.装备生命);
            this.装备管理.Controls.Add(this.label33);
            this.装备管理.Controls.Add(this.装备闪避);
            this.装备管理.Controls.Add(this.label32);
            this.装备管理.Controls.Add(this.装备速度);
            this.装备管理.Controls.Add(this.label31);
            this.装备管理.Controls.Add(this.装备防御);
            this.装备管理.Controls.Add(this.label30);
            this.装备管理.Controls.Add(this.装备攻击);
            this.装备管理.Controls.Add(this.label29);
            this.装备管理.Controls.Add(this.装备名字);
            this.装备管理.Controls.Add(this.label28);
            this.装备管理.Controls.Add(this.装备_ID);
            this.装备管理.Controls.Add(this.label27);
            this.装备管理.Location = new System.Drawing.Point(4, 22);
            this.装备管理.Name = "装备管理";
            this.装备管理.Size = new System.Drawing.Size(389, 448);
            this.装备管理.TabIndex = 6;
            this.装备管理.Text = "装备管理";
            this.装备管理.UseVisualStyleBackColor = true;
            // 
            // 五行限制
            // 
            this.五行限制.FormattingEnabled = true;
            this.五行限制.Items.AddRange(new object[] {
            "金",
            "木",
            "水",
            "火",
            "土",
            "神",
            "神圣",
            "聖",
            "佛",
            "巫"});
            this.五行限制.Location = new System.Drawing.Point(73, 229);
            this.五行限制.Name = "五行限制";
            this.五行限制.Size = new System.Drawing.Size(58, 20);
            this.五行限制.TabIndex = 10;
            // 
            // label53
            // 
            this.label53.AutoSize = true;
            this.label53.Location = new System.Drawing.Point(20, 232);
            this.label53.Name = "label53";
            this.label53.Size = new System.Drawing.Size(35, 12);
            this.label53.TabIndex = 9;
            this.label53.Text = "五行:";
            // 
            // textBox4
            // 
            this.textBox4.Location = new System.Drawing.Point(73, 261);
            this.textBox4.Multiline = true;
            this.textBox4.Name = "textBox4";
            this.textBox4.Size = new System.Drawing.Size(227, 41);
            this.textBox4.TabIndex = 8;
            // 
            // label42
            // 
            this.label42.AutoSize = true;
            this.label42.Location = new System.Drawing.Point(20, 263);
            this.label42.Name = "label42";
            this.label42.Size = new System.Drawing.Size(35, 12);
            this.label42.TabIndex = 7;
            this.label42.Text = "说明:";
            // 
            // button12
            // 
            this.button12.Location = new System.Drawing.Point(27, 317);
            this.button12.Name = "button12";
            this.button12.Size = new System.Drawing.Size(216, 42);
            this.button12.TabIndex = 6;
            this.button12.Text = "保存";
            this.button12.UseVisualStyleBackColor = true;
            this.button12.Click += new System.EventHandler(this.button12_Click);
            // 
            // 主属性
            // 
            this.主属性.FormattingEnabled = true;
            this.主属性.Items.AddRange(new object[] {
            "攻击",
            "防御",
            "速度",
            "闪避",
            "生命",
            "魔法",
            "命中"});
            this.主属性.Location = new System.Drawing.Point(228, 204);
            this.主属性.Name = "主属性";
            this.主属性.Size = new System.Drawing.Size(72, 20);
            this.主属性.TabIndex = 5;
            // 
            // label46
            // 
            this.label46.AutoSize = true;
            this.label46.Location = new System.Drawing.Point(175, 207);
            this.label46.Name = "label46";
            this.label46.Size = new System.Drawing.Size(47, 12);
            this.label46.TabIndex = 4;
            this.label46.Text = "主属性:";
            // 
            // 装备类型
            // 
            this.装备类型.FormattingEnabled = true;
            this.装备类型.Items.AddRange(new object[] {
            "身体",
            "头部",
            "项链",
            "武器",
            "手镯",
            "脚部",
            "戒指",
            "翅膀",
            "宝石",
            "道具",
            "卡牌左",
            "卡牌右",
            "左神兽",
            "背饰"});
            this.装备类型.Location = new System.Drawing.Point(228, 179);
            this.装备类型.Name = "装备类型";
            this.装备类型.Size = new System.Drawing.Size(72, 20);
            this.装备类型.TabIndex = 5;
            // 
            // label40
            // 
            this.label40.AutoSize = true;
            this.label40.Location = new System.Drawing.Point(175, 179);
            this.label40.Name = "label40";
            this.label40.Size = new System.Drawing.Size(35, 12);
            this.label40.TabIndex = 4;
            this.label40.Text = "类型:";
            // 
            // 装备命中
            // 
            this.装备命中.Location = new System.Drawing.Point(73, 203);
            this.装备命中.Name = "装备命中";
            this.装备命中.Size = new System.Drawing.Size(58, 21);
            this.装备命中.TabIndex = 3;
            // 
            // label43
            // 
            this.label43.AutoSize = true;
            this.label43.Location = new System.Drawing.Point(20, 206);
            this.label43.Name = "label43";
            this.label43.Size = new System.Drawing.Size(35, 12);
            this.label43.TabIndex = 2;
            this.label43.Text = "命中:";
            // 
            // 装备图标
            // 
            this.装备图标.Location = new System.Drawing.Point(73, 176);
            this.装备图标.Name = "装备图标";
            this.装备图标.Size = new System.Drawing.Size(96, 21);
            this.装备图标.TabIndex = 3;
            // 
            // label39
            // 
            this.label39.AutoSize = true;
            this.label39.Location = new System.Drawing.Point(20, 179);
            this.label39.Name = "label39";
            this.label39.Size = new System.Drawing.Size(35, 12);
            this.label39.TabIndex = 2;
            this.label39.Text = "图标:";
            // 
            // 装备吸血
            // 
            this.装备吸血.Location = new System.Drawing.Point(73, 149);
            this.装备吸血.Name = "装备吸血";
            this.装备吸血.Size = new System.Drawing.Size(58, 21);
            this.装备吸血.TabIndex = 1;
            // 
            // label38
            // 
            this.label38.AutoSize = true;
            this.label38.Location = new System.Drawing.Point(20, 152);
            this.label38.Name = "label38";
            this.label38.Size = new System.Drawing.Size(35, 12);
            this.label38.TabIndex = 0;
            this.label38.Text = "吸血:";
            // 
            // 装备加深
            // 
            this.装备加深.Location = new System.Drawing.Point(73, 122);
            this.装备加深.Name = "装备加深";
            this.装备加深.Size = new System.Drawing.Size(58, 21);
            this.装备加深.TabIndex = 1;
            // 
            // 装备吸魔
            // 
            this.装备吸魔.Location = new System.Drawing.Point(228, 149);
            this.装备吸魔.Name = "装备吸魔";
            this.装备吸魔.Size = new System.Drawing.Size(58, 21);
            this.装备吸魔.TabIndex = 1;
            // 
            // label35
            // 
            this.label35.AutoSize = true;
            this.label35.Location = new System.Drawing.Point(20, 125);
            this.label35.Name = "label35";
            this.label35.Size = new System.Drawing.Size(35, 12);
            this.label35.TabIndex = 0;
            this.label35.Text = "加深:";
            // 
            // label37
            // 
            this.label37.AutoSize = true;
            this.label37.Location = new System.Drawing.Point(175, 152);
            this.label37.Name = "label37";
            this.label37.Size = new System.Drawing.Size(35, 12);
            this.label37.TabIndex = 0;
            this.label37.Text = "吸魔:";
            // 
            // 装备抵消
            // 
            this.装备抵消.Location = new System.Drawing.Point(228, 122);
            this.装备抵消.Name = "装备抵消";
            this.装备抵消.Size = new System.Drawing.Size(58, 21);
            this.装备抵消.TabIndex = 1;
            // 
            // label36
            // 
            this.label36.AutoSize = true;
            this.label36.Location = new System.Drawing.Point(175, 125);
            this.label36.Name = "label36";
            this.label36.Size = new System.Drawing.Size(35, 12);
            this.label36.TabIndex = 0;
            this.label36.Text = "抵消:";
            // 
            // 装备魔法
            // 
            this.装备魔法.Location = new System.Drawing.Point(228, 95);
            this.装备魔法.Name = "装备魔法";
            this.装备魔法.Size = new System.Drawing.Size(58, 21);
            this.装备魔法.TabIndex = 1;
            // 
            // label34
            // 
            this.label34.AutoSize = true;
            this.label34.Location = new System.Drawing.Point(175, 98);
            this.label34.Name = "label34";
            this.label34.Size = new System.Drawing.Size(35, 12);
            this.label34.TabIndex = 0;
            this.label34.Text = "魔法:";
            // 
            // 装备生命
            // 
            this.装备生命.Location = new System.Drawing.Point(73, 95);
            this.装备生命.Name = "装备生命";
            this.装备生命.Size = new System.Drawing.Size(58, 21);
            this.装备生命.TabIndex = 1;
            // 
            // label33
            // 
            this.label33.AutoSize = true;
            this.label33.Location = new System.Drawing.Point(20, 98);
            this.label33.Name = "label33";
            this.label33.Size = new System.Drawing.Size(35, 12);
            this.label33.TabIndex = 0;
            this.label33.Text = "生命:";
            // 
            // 装备闪避
            // 
            this.装备闪避.Location = new System.Drawing.Point(228, 68);
            this.装备闪避.Name = "装备闪避";
            this.装备闪避.Size = new System.Drawing.Size(58, 21);
            this.装备闪避.TabIndex = 1;
            // 
            // label32
            // 
            this.label32.AutoSize = true;
            this.label32.Location = new System.Drawing.Point(175, 71);
            this.label32.Name = "label32";
            this.label32.Size = new System.Drawing.Size(35, 12);
            this.label32.TabIndex = 0;
            this.label32.Text = "闪避:";
            // 
            // 装备速度
            // 
            this.装备速度.Location = new System.Drawing.Point(73, 68);
            this.装备速度.Name = "装备速度";
            this.装备速度.Size = new System.Drawing.Size(58, 21);
            this.装备速度.TabIndex = 1;
            // 
            // label31
            // 
            this.label31.AutoSize = true;
            this.label31.Location = new System.Drawing.Point(20, 71);
            this.label31.Name = "label31";
            this.label31.Size = new System.Drawing.Size(35, 12);
            this.label31.TabIndex = 0;
            this.label31.Text = "速度:";
            // 
            // 装备防御
            // 
            this.装备防御.Location = new System.Drawing.Point(228, 41);
            this.装备防御.Name = "装备防御";
            this.装备防御.Size = new System.Drawing.Size(58, 21);
            this.装备防御.TabIndex = 1;
            // 
            // label30
            // 
            this.label30.AutoSize = true;
            this.label30.Location = new System.Drawing.Point(175, 44);
            this.label30.Name = "label30";
            this.label30.Size = new System.Drawing.Size(35, 12);
            this.label30.TabIndex = 0;
            this.label30.Text = "防御:";
            // 
            // 装备攻击
            // 
            this.装备攻击.Location = new System.Drawing.Point(73, 41);
            this.装备攻击.Name = "装备攻击";
            this.装备攻击.Size = new System.Drawing.Size(58, 21);
            this.装备攻击.TabIndex = 1;
            // 
            // label29
            // 
            this.label29.AutoSize = true;
            this.label29.Location = new System.Drawing.Point(20, 44);
            this.label29.Name = "label29";
            this.label29.Size = new System.Drawing.Size(35, 12);
            this.label29.TabIndex = 0;
            this.label29.Text = "攻击:";
            // 
            // 装备名字
            // 
            this.装备名字.Location = new System.Drawing.Point(228, 14);
            this.装备名字.Name = "装备名字";
            this.装备名字.Size = new System.Drawing.Size(104, 21);
            this.装备名字.TabIndex = 1;
            // 
            // label28
            // 
            this.label28.AutoSize = true;
            this.label28.Location = new System.Drawing.Point(175, 17);
            this.label28.Name = "label28";
            this.label28.Size = new System.Drawing.Size(35, 12);
            this.label28.TabIndex = 0;
            this.label28.Text = "名字:";
            // 
            // 装备_ID
            // 
            this.装备_ID.Location = new System.Drawing.Point(73, 14);
            this.装备_ID.Name = "装备_ID";
            this.装备_ID.Size = new System.Drawing.Size(96, 21);
            this.装备_ID.TabIndex = 1;
            // 
            // label27
            // 
            this.label27.AutoSize = true;
            this.label27.Location = new System.Drawing.Point(20, 17);
            this.label27.Name = "label27";
            this.label27.Size = new System.Drawing.Size(47, 12);
            this.label27.TabIndex = 0;
            this.label27.Text = "类型ID:";
            // 
            // 装备存档
            // 
            this.装备存档.Controls.Add(this.存档装备等级);
            this.装备存档.Controls.Add(this.label48);
            this.装备存档.Controls.Add(this.button13);
            this.装备存档.Controls.Add(this.linkLabel12);
            this.装备存档.Controls.Add(this.label41);
            this.装备存档.Controls.Add(this.存档装备ID);
            this.装备存档.Location = new System.Drawing.Point(4, 22);
            this.装备存档.Name = "装备存档";
            this.装备存档.Size = new System.Drawing.Size(389, 448);
            this.装备存档.TabIndex = 7;
            this.装备存档.Text = "装备存档";
            this.装备存档.UseVisualStyleBackColor = true;
            this.装备存档.Click += new System.EventHandler(this.tabPage6_Click);
            // 
            // 存档装备等级
            // 
            this.存档装备等级.Location = new System.Drawing.Point(81, 73);
            this.存档装备等级.Name = "存档装备等级";
            this.存档装备等级.Size = new System.Drawing.Size(72, 21);
            this.存档装备等级.TabIndex = 19;
            this.存档装备等级.Text = "0";
            // 
            // label48
            // 
            this.label48.AutoSize = true;
            this.label48.Location = new System.Drawing.Point(12, 78);
            this.label48.Name = "label48";
            this.label48.Size = new System.Drawing.Size(59, 12);
            this.label48.TabIndex = 18;
            this.label48.Text = "强化等级:";
            // 
            // button13
            // 
            this.button13.Location = new System.Drawing.Point(44, 115);
            this.button13.Name = "button13";
            this.button13.Size = new System.Drawing.Size(194, 35);
            this.button13.TabIndex = 17;
            this.button13.Text = "添加道具";
            this.button13.UseVisualStyleBackColor = true;
            this.button13.Click += new System.EventHandler(this.button13_Click);
            // 
            // linkLabel12
            // 
            this.linkLabel12.AutoSize = true;
            this.linkLabel12.Location = new System.Drawing.Point(223, 36);
            this.linkLabel12.Name = "linkLabel12";
            this.linkLabel12.Size = new System.Drawing.Size(53, 12);
            this.linkLabel12.TabIndex = 16;
            this.linkLabel12.TabStop = true;
            this.linkLabel12.Text = "选择道具";
            this.linkLabel12.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.linkLabel12_LinkClicked);
            // 
            // label41
            // 
            this.label41.AutoSize = true;
            this.label41.Location = new System.Drawing.Point(12, 36);
            this.label41.Name = "label41";
            this.label41.Size = new System.Drawing.Size(59, 12);
            this.label41.TabIndex = 15;
            this.label41.Text = "装备序号:";
            this.label41.Click += new System.EventHandler(this.label41_Click);
            // 
            // 存档装备ID
            // 
            this.存档装备ID.Location = new System.Drawing.Point(81, 31);
            this.存档装备ID.Name = "存档装备ID";
            this.存档装备ID.Size = new System.Drawing.Size(134, 21);
            this.存档装备ID.TabIndex = 14;
            this.存档装备ID.TextChanged += new System.EventHandler(this.存档装备ID_TextChanged);
            // 
            // 生成操作
            // 
            this.生成操作.Controls.Add(this.button17);
            this.生成操作.Location = new System.Drawing.Point(4, 22);
            this.生成操作.Name = "生成操作";
            this.生成操作.Padding = new System.Windows.Forms.Padding(3);
            this.生成操作.Size = new System.Drawing.Size(389, 448);
            this.生成操作.TabIndex = 9;
            this.生成操作.Text = "生成操作";
            this.生成操作.UseVisualStyleBackColor = true;
            // 
            // button17
            // 
            this.button17.Location = new System.Drawing.Point(21, 20);
            this.button17.Name = "button17";
            this.button17.Size = new System.Drawing.Size(127, 45);
            this.button17.TabIndex = 0;
            this.button17.Text = "生成图纸预览界面";
            this.button17.UseVisualStyleBackColor = true;
            this.button17.Click += new System.EventHandler(this.button17_Click);
            // 
            // tabPage9
            // 
            this.tabPage9.Controls.Add(this.button22);
            this.tabPage9.Controls.Add(this.textBox8);
            this.tabPage9.Location = new System.Drawing.Point(4, 22);
            this.tabPage9.Name = "tabPage9";
            this.tabPage9.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage9.Size = new System.Drawing.Size(389, 448);
            this.tabPage9.TabIndex = 10;
            this.tabPage9.Text = "tabPage9";
            this.tabPage9.UseVisualStyleBackColor = true;
            // 
            // button22
            // 
            this.button22.Location = new System.Drawing.Point(68, 13);
            this.button22.Name = "button22";
            this.button22.Size = new System.Drawing.Size(122, 48);
            this.button22.TabIndex = 12;
            this.button22.Text = "获取";
            this.button22.UseVisualStyleBackColor = true;
            this.button22.Click += new System.EventHandler(this.button22_Click);
            // 
            // textBox8
            // 
            this.textBox8.AllowDrop = true;
            this.textBox8.Location = new System.Drawing.Point(6, 67);
            this.textBox8.MaxLength = 99999999;
            this.textBox8.Multiline = true;
            this.textBox8.Name = "textBox8";
            this.textBox8.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.textBox8.Size = new System.Drawing.Size(262, 229);
            this.textBox8.TabIndex = 11;
            // 
            // button9
            // 
            this.button9.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.button9.Location = new System.Drawing.Point(287, 463);
            this.button9.Name = "button9";
            this.button9.Size = new System.Drawing.Size(89, 49);
            this.button9.TabIndex = 7;
            this.button9.Text = "转换为RC4";
            this.button9.UseVisualStyleBackColor = true;
            this.button9.Click += new System.EventHandler(this.button9_Click);
            // 
            // textBox7
            // 
            this.textBox7.Location = new System.Drawing.Point(207, 14);
            this.textBox7.Name = "textBox7";
            this.textBox7.Size = new System.Drawing.Size(108, 21);
            this.textBox7.TabIndex = 8;
            // 
            // search
            // 
            this.search.Location = new System.Drawing.Point(321, 11);
            this.search.Name = "search";
            this.search.Size = new System.Drawing.Size(65, 24);
            this.search.TabIndex = 9;
            this.search.Text = "搜索道具";
            this.search.UseVisualStyleBackColor = true;
            this.search.Click += new System.EventHandler(this.button18_Click);
            // 
            // editor
            // 
            this.editor.Location = new System.Drawing.Point(392, 11);
            this.editor.Name = "editor";
            this.editor.Size = new System.Drawing.Size(65, 24);
            this.editor.TabIndex = 10;
            this.editor.Text = "编辑窗口";
            this.editor.UseVisualStyleBackColor = true;
            this.editor.Click += new System.EventHandler(this.editor_Click);
            // 
            // button26
            // 
            this.button26.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.button26.Location = new System.Drawing.Point(872, 501);
            this.button26.Name = "button26";
            this.button26.Size = new System.Drawing.Size(20, 23);
            this.button26.TabIndex = 11;
            this.button26.Text = "m";
            this.button26.UseVisualStyleBackColor = true;
            this.button26.Click += new System.EventHandler(this.button26_Click);
            // 
            // button27
            // 
            this.button27.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.button27.Location = new System.Drawing.Point(192, 463);
            this.button27.Name = "button27";
            this.button27.Size = new System.Drawing.Size(89, 49);
            this.button27.TabIndex = 12;
            this.button27.Text = "邮件RC4";
            this.button27.UseVisualStyleBackColor = true;
            this.button27.Click += new System.EventHandler(this.button27_Click);
            // 
            // button29
            // 
            this.button29.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.button29.Location = new System.Drawing.Point(382, 463);
            this.button29.Name = "button29";
            this.button29.Size = new System.Drawing.Size(89, 49);
            this.button29.TabIndex = 7;
            this.button29.Text = "解密RC4";
            this.button29.UseVisualStyleBackColor = true;
            this.button29.Click += new System.EventHandler(this.button29_Click);
            // 
            // button35
            // 
            this.button35.Location = new System.Drawing.Point(8, 221);
            this.button35.Name = "button35";
            this.button35.Size = new System.Drawing.Size(124, 43);
            this.button35.TabIndex = 8;
            this.button35.Text = "道具配置管理";
            this.button35.UseVisualStyleBackColor = true;
            this.button35.Click += new System.EventHandler(this.button35_Click);
            // 
            // 管理
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(96F, 96F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Dpi;
            this.ClientSize = new System.Drawing.Size(892, 524);
            this.Controls.Add(this.button27);
            this.Controls.Add(this.button26);
            this.Controls.Add(this.textBox1);
            this.Controls.Add(this.editor);
            this.Controls.Add(this.search);
            this.Controls.Add(this.textBox7);
            this.Controls.Add(this.button29);
            this.Controls.Add(this.button9);
            this.Controls.Add(this.tabControl1);
            this.Controls.Add(this.文件);
            this.Controls.Add(this.button1);
            this.Controls.Add(this.地图ID);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.comboBox1);
            this.Name = "管理";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "配置编辑器";
            this.Load += new System.EventHandler(this.Form1_Load);
            this.tabControl1.ResumeLayout(false);
            this.tabPage7.ResumeLayout(false);
            this.信息处理.ResumeLayout(false);
            this.道具配置.ResumeLayout(false);
            this.道具配置.PerformLayout();
            this.魔法卡配置.ResumeLayout(false);
            this.魔法卡配置.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox2)).EndInit();
            this.皮肤配置.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView_pifu)).EndInit();
            this.contextMenu_bs.ResumeLayout(false);
            this.宝石管理.ResumeLayout(false);
            this.宝石管理.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.bs_dataList)).EndInit();
            this.道具存档.ResumeLayout(false);
            this.道具存档.PerformLayout();
            this.宠物存档.ResumeLayout(false);
            this.宠物存档.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.宠物等级)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).EndInit();
            this.地图配置.ResumeLayout(false);
            this.tabControl2.ResumeLayout(false);
            this.tabPage2.ResumeLayout(false);
            this.tabPage2.PerformLayout();
            this.tabPage3.ResumeLayout(false);
            this.tabPage3.PerformLayout();
            this.哈希验证.ResumeLayout(false);
            this.哈希验证.PerformLayout();
            this.装备管理.ResumeLayout(false);
            this.装备管理.PerformLayout();
            this.装备存档.ResumeLayout(false);
            this.装备存档.PerformLayout();
            this.生成操作.ResumeLayout(false);
            this.tabPage9.ResumeLayout(false);
            this.tabPage9.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.ComboBox comboBox1;
        private System.Windows.Forms.TextBox textBox1;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.TextBox 地图ID;
        private System.Windows.Forms.Button button1;
        private System.Windows.Forms.TextBox 文件;
        private System.Windows.Forms.TabControl tabControl1;
        private System.Windows.Forms.TabPage 道具配置;
        private System.Windows.Forms.TabPage 道具存档;
        private System.Windows.Forms.TabPage 宠物存档;
        private System.Windows.Forms.TextBox 道具名字;
        private System.Windows.Forms.Button button2;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.TextBox 道具序号;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.TextBox 道具图标;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.TextBox 道具说明;
        private System.Windows.Forms.LinkLabel linkLabel1;
        public System.Windows.Forms.TextBox 道具脚本;
        private System.Windows.Forms.LinkLabel linkLabel2;
        private System.Windows.Forms.Button button3;
        private System.Windows.Forms.LinkLabel linkLabel3;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.TextBox 添加道具数量;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.TextBox 道具序号2;
        private System.Windows.Forms.Button button4;
        private System.Windows.Forms.LinkLabel linkLabel4;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.TextBox 宠物成长;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.TextBox 宠物序号;
        private System.Windows.Forms.PictureBox pictureBox1;
        private System.Windows.Forms.TabPage 地图配置;
        private System.Windows.Forms.TabControl tabControl2;
        private System.Windows.Forms.TabPage tabPage2;
        private System.Windows.Forms.TabPage tabPage3;
        private System.Windows.Forms.Button button5;
        private System.Windows.Forms.Label label17;
        private System.Windows.Forms.TextBox 地图掉落最大数量;
        private System.Windows.Forms.Label label18;
        private System.Windows.Forms.TextBox 地图掉落最小数量;
        private System.Windows.Forms.TextBox 地图掉落列表;
        private System.Windows.Forms.Label label16;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.TextBox 地图最大元宝;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.TextBox 地图最小元宝;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.TextBox 地图最大金币;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.TextBox 地图最小金币;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.TextBox 地图序号;
        private System.Windows.Forms.LinkLabel linkLabel5;
        private System.Windows.Forms.LinkLabel linkLabel7;
        private System.Windows.Forms.LinkLabel linkLabel8;
        private System.Windows.Forms.Button button7;
        private System.Windows.Forms.TextBox 怪物掉落;
        private System.Windows.Forms.Label label24;
        private System.Windows.Forms.TextBox 怪物成长;
        private System.Windows.Forms.TextBox 怪物最小等级;
        private System.Windows.Forms.TextBox 怪物序号;
        private System.Windows.Forms.TextBox 怪物最大掉落;
        private System.Windows.Forms.Label label23;
        private System.Windows.Forms.LinkLabel linkLabel6;
        private System.Windows.Forms.Label label22;
        private System.Windows.Forms.Label label20;
        private System.Windows.Forms.TextBox 怪物最大等级;
        private System.Windows.Forms.Label label21;
        private System.Windows.Forms.Label label19;
        private System.Windows.Forms.Button button8;
        private System.Windows.Forms.TextBox 怪物经验;
        private System.Windows.Forms.Label label25;
        private System.Windows.Forms.LinkLabel linkLabel9;
        private System.Windows.Forms.Button button6;
        private System.Windows.Forms.LinkLabel linkLabel11;
        private System.Windows.Forms.LinkLabel linkLabel10;
        private System.Windows.Forms.Label label26;
        private System.Windows.Forms.TextBox 出售价格;
        private System.Windows.Forms.Button button9;
        private System.Windows.Forms.TabPage 哈希验证;
        private System.Windows.Forms.TextBox textBox2;
        private System.Windows.Forms.Button button10;
        private System.Windows.Forms.Button button11;
        private System.Windows.Forms.TabPage 装备管理;
        private System.Windows.Forms.ComboBox 装备类型;
        private System.Windows.Forms.Label label40;
        private System.Windows.Forms.TextBox 装备图标;
        private System.Windows.Forms.Label label39;
        private System.Windows.Forms.TextBox 装备吸血;
        private System.Windows.Forms.Label label38;
        private System.Windows.Forms.TextBox 装备加深;
        private System.Windows.Forms.TextBox 装备吸魔;
        private System.Windows.Forms.Label label35;
        private System.Windows.Forms.Label label37;
        private System.Windows.Forms.TextBox 装备抵消;
        private System.Windows.Forms.Label label36;
        private System.Windows.Forms.TextBox 装备魔法;
        private System.Windows.Forms.Label label34;
        private System.Windows.Forms.TextBox 装备生命;
        private System.Windows.Forms.Label label33;
        private System.Windows.Forms.TextBox 装备闪避;
        private System.Windows.Forms.Label label32;
        private System.Windows.Forms.TextBox 装备速度;
        private System.Windows.Forms.Label label31;
        private System.Windows.Forms.TextBox 装备防御;
        private System.Windows.Forms.Label label30;
        private System.Windows.Forms.TextBox 装备攻击;
        private System.Windows.Forms.Label label29;
        private System.Windows.Forms.TextBox 装备名字;
        private System.Windows.Forms.Label label28;
        private System.Windows.Forms.TextBox 装备_ID;
        private System.Windows.Forms.Label label27;
        private System.Windows.Forms.Button button12;
        private System.Windows.Forms.TabPage 装备存档;
        private System.Windows.Forms.Button button13;
        private System.Windows.Forms.LinkLabel linkLabel12;
        private System.Windows.Forms.Label label41;
        private System.Windows.Forms.TextBox 存档装备ID;
        private System.Windows.Forms.TextBox textBox4;
        private System.Windows.Forms.Label label42;
        private System.Windows.Forms.TextBox 装备命中;
        private System.Windows.Forms.Label label43;
        private System.Windows.Forms.LinkLabel linkLabel14;
        private System.Windows.Forms.LinkLabel linkLabel13;
        private System.Windows.Forms.Label label44;
        private System.Windows.Forms.TextBox textBox5;
        private System.Windows.Forms.TextBox textBox6;
        private System.Windows.Forms.Label label45;
        private System.Windows.Forms.CheckBox checkBox2;
        private System.Windows.Forms.TabPage tabPage7;
        private System.Windows.Forms.Button button14;
        private System.Windows.Forms.Button button15;
        private System.Windows.Forms.ComboBox 主属性;
        private System.Windows.Forms.Label label46;
        private System.Windows.Forms.Button button16;
        private System.Windows.Forms.TabPage 生成操作;
        private System.Windows.Forms.Button button17;
        private System.Windows.Forms.TextBox textBox7;
        private System.Windows.Forms.Button search;
        private System.Windows.Forms.Button editor;
        private System.Windows.Forms.Button button18;
        private System.Windows.Forms.Button button19;
        private System.Windows.Forms.Button button20;
        private System.Windows.Forms.Button button21;
        private System.Windows.Forms.TabPage tabPage9;
        private System.Windows.Forms.Button button22;
        private System.Windows.Forms.TextBox textBox8;
        private System.Windows.Forms.Button button23;
        private System.Windows.Forms.Button button24;
        private System.Windows.Forms.Button button25;
        private System.Windows.Forms.Button button26;
        private System.Windows.Forms.Button button27;
        private System.Windows.Forms.Button button28;
        private System.Windows.Forms.Button button29;
        private System.Windows.Forms.TextBox textBox3;
        private System.Windows.Forms.Label label47;
        private System.Windows.Forms.TextBox 存档装备等级;
        private System.Windows.Forms.Label label48;
        private System.Windows.Forms.Button button30;
        private System.Windows.Forms.TabPage 魔法卡配置;
        private System.Windows.Forms.ComboBox 卡片属性;
        private System.Windows.Forms.Label label49;
        private System.Windows.Forms.TextBox 卡片图片;
        private System.Windows.Forms.Label label50;
        private System.Windows.Forms.TextBox 卡片属性数值;
        private System.Windows.Forms.Label label51;
        private System.Windows.Forms.Label label52;
        private System.Windows.Forms.TextBox 卡片名字;
        private System.Windows.Forms.PictureBox pictureBox2;
        private System.Windows.Forms.Button 添加魔法卡;
        private System.Windows.Forms.ComboBox 五行限制;
        private System.Windows.Forms.Label label53;
        private System.Windows.Forms.Label label54;
        private System.Windows.Forms.NumericUpDown 宠物等级;
        private System.Windows.Forms.Button button31;
        private System.Windows.Forms.TextBox textBox9;
        private System.Windows.Forms.Label label55;
        private System.Windows.Forms.Button button32;
        private System.Windows.Forms.Button button33;
        private System.Windows.Forms.TabPage 宝石管理;
        private System.Windows.Forms.Label label60;
        private System.Windows.Forms.Label label59;
        private System.Windows.Forms.Label label58;
        private System.Windows.Forms.Label label57;
        private System.Windows.Forms.Label label56;
        private System.Windows.Forms.Label label62;
        private System.Windows.Forms.Label label61;
        private System.Windows.Forms.Label label64;
        private System.Windows.Forms.Label label63;
        private System.Windows.Forms.ComboBox bs_eType;
        private System.Windows.Forms.ComboBox bs_typeClass;
        private System.Windows.Forms.ComboBox bs_upType;
        private System.Windows.Forms.TextBox bs_wxz;
        private System.Windows.Forms.TextBox bs_color;
        private System.Windows.Forms.TextBox bs_LV;
        private System.Windows.Forms.TextBox bs_upNum;
        private System.Windows.Forms.TextBox bs_typeName;
        private System.Windows.Forms.TextBox bs_pid;
        private System.Windows.Forms.DataGridView bs_dataList;
        private System.Windows.Forms.ContextMenuStrip contextMenu_bs;
        private System.Windows.Forms.ToolStripMenuItem 读取ToolStripMenuItem;
        private System.Windows.Forms.DataGridViewTextBoxColumn typeName;
        private System.Windows.Forms.Button bs_add;
        private System.Windows.Forms.TextBox bs_eType_str;
        private System.Windows.Forms.ColorDialog colorDialog1;
        private System.Windows.Forms.Button button34;
        private System.Windows.Forms.TabPage 信息处理;
        private System.Windows.Forms.Button 宠物列表_270;
        private System.Windows.Forms.Button 宠物列表_300;
        private System.Windows.Forms.Button 宠物列表_280;
        private System.Windows.Forms.TabPage 皮肤配置;
        private System.Windows.Forms.DataGridView dataGridView_pifu;
        private System.Windows.Forms.DataGridViewTextBoxColumn 皮肤名;
        private System.Windows.Forms.DataGridViewTextBoxColumn 形象ID;
        private System.Windows.Forms.Label label65;
        private System.Windows.Forms.TextBox 数量上限;
        private System.Windows.Forms.Label label66;
        private System.Windows.Forms.ComboBox 道具分类;
        private System.Windows.Forms.Button button35;
    }
}

