﻿using System;
using System.Collections.Generic;
using System.Windows.Forms;
using 怪物信息 = Shikong.Pokemon2.PCG.MonsterInfo;
using 用户信息 = Shikong.Pokemon2.PCG.UserInfo;
using 宠物信息 = Shikong.Pokemon2.PCG.PetInfo;
using 宠物类型 = Shikong.Pokemon2.PCG.PetConfig;
using 道具信息 = Shikong.Pokemon2.PCG.PropInfo;
using 地图信息 = Shikong.Pokemon2.PCG.MapInfo;
using 装备类型 = Shikong.Pokemon2.PCG.EquipmentType;
using 装备信息 = Shikong.Pokemon2.PCG.EquipmentInfo;
using 道具类型 = Shikong.Pokemon2.PCG.PropType;
using 道具具体信息 = Shikong.Pokemon2.PCG.PropConfig;
using 数据处理 = Shikong.Pokemon2.PCG.DataProcess;
using Shikong.Pokemon2.PCG;
namespace Admin
{
    public partial class 宠物进化路线 : Form
    {
        List<宠物类型> 宠物列表 = new 数据处理().ReadPetTypeList();
        public 宠物进化路线()
        {
            InitializeComponent();
        }

        private void 宠物进化路线_Load(object sender, EventArgs e)
        {
            dataGridView1.DataSource = 宠物列表;
            dataGridView2.AutoGenerateColumns = false;
        }

        private void button1_Click(object sender, EventArgs e)
        {
           
            if (this.Width == 1053) {
                this.Width = 334;
                return;
            }
            //dataGridView2.DataSource = new 数据处理().ReadPetTypeList();
            dataGridView2.DataSource = new 数据处理().GetAllEW();
            this.Width = 1053;
        }

        private void AID_TextChanged(object sender, EventArgs e)
        {
            if (AID.Text.Length == 0) {
                return;
            }
            宠物类型 宠物= new 数据处理().GetAppointedPetType(AID.Text);
            ANAME.Text = 宠物.宠物名字;
            APIC.ImageLocation = Environment.CurrentDirectory.ToString() + "\\PageMain\\Content\\PetPhoto\\t" + AID.Text + ".gif";
        }

        private void APROP_TextChanged(object sender, EventArgs e)
        {
            if (APROP.Text != "")
            {

                label7.Text = new 数据处理().GetPropName(APROP.Text);
            }
        }

        private void APROP_MouseClick(object sender, MouseEventArgs e)
        {
            APROP.Text = "";
            道具列表 列表 = new 道具列表();
            列表.TEXT = APROP;
            列表.ShowDialog();
        }

        private void BPROP_TextChanged(object sender, EventArgs e)
        {
           
        }

        private void groupBox2_Enter(object sender, EventArgs e)
        {

        }

        private void BPROP_TextChanged_1(object sender, EventArgs e)
        {
            if (BPROP.Text != "")
            {
                label3.Text = new 数据处理().GetPropName(BPROP.Text);
            }
        }

        private void BID_TextChanged(object sender, EventArgs e)
        {
            if (BID.Text.Length == 0)
            {
                return;
            }
            宠物类型 宠物 = new 数据处理().GetAppointedPetType(BID.Text);
            BNAME.Text = 宠物.宠物名字;
            BPIC.ImageLocation = Environment.CurrentDirectory.ToString() + "\\PageMain\\Content\\PetPhoto\\t" +BID.Text + ".gif";
        }

        private void BPROP_Click(object sender, EventArgs e)
        {
            BPROP.Text = "";
            道具列表 列表 = new 道具列表();
            列表.TEXT = BPROP;
            列表.ShowDialog();
        }

        private void textBox1_TextChanged(object sender, EventArgs e)
        {
            List<宠物类型> 新列表 = new List<宠物类型>();
            for (int i = 0; i < 宠物列表.Count; i++) {
                if (宠物列表[i].宠物名字.IndexOf(textBox1.Text)!=-1 || textBox1.Text.Length == 0) {
                    新列表.Add(宠物列表[i]);
                }
            }
            dataGridView1.DataSource = 新列表;
        }

        private void AID_MouseClick(object sender, MouseEventArgs e)
        {
            宠物列表 列表 = new 宠物列表();
            列表.类型 = "宠物序号";
            列表.TEXT = AID;
            列表.ShowDialog();
        }

        private void BID_Click(object sender, EventArgs e)
        {
            宠物列表 列表 = new 宠物列表();
            列表.类型 = "宠物序号";
            列表.TEXT = BID;
            列表.ShowDialog();
        }
        String 选择ID = null;
        private void dataGridView1_CellContentDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
          
        }

        private void dataGridView1_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {

        }

        private void dataGridView1_CellMouseDoubleClick(object sender, DataGridViewCellMouseEventArgs e)
        {
            try
            {
                if (e.RowIndex > -1)
                {
                    string n = dataGridView1.Rows[e.RowIndex].Cells[0].Value.ToString();
                    string id = dataGridView1.Rows[e.RowIndex].Cells[1].Value.ToString();
                    选择ID = id;
                    选中的宠物路线.Text = "新增(宠物名:" + n + ",宠物ID:" + id + ")";
                    EvolutionWay 路线 = new DataProcess().GetAppointedEW(id);
                    if (路线 != null)
                    {
                        APROP.Text = 路线.AP;
                        BPROP.Text = 路线.BP;
                        AID.Text = 路线.AI;
                        BID.Text = 路线.BI;
                        ALV.Text = 路线.ALV;
                        BLV.Text = 路线.BLV;
                        选中的宠物路线.Text = "修改(宠物名:" + n + ",宠物ID:" + id + ")";
                    }
                    else
                    {
                        APROP.ResetText();
                        BPROP.ResetText();
                        AID.ResetText();
                        BID.ResetText();
                        ALV.ResetText();
                        BLV.ResetText();
                    }
                    选中的宠物路线.Enabled = true;
                }
            }
            catch (Exception ex)
            {

            }
        }

        private void button2_Click(object sender, EventArgs e)
        {
            EvolutionWay 路线 = new EvolutionWay();
            路线.AP = APROP.Text;
            路线.BP = BPROP.Text;
            路线.AI = AID.Text;//A进化宠物
            路线.BI = BID.Text;//B进化宠物
            路线.ALV = ALV.Text;
            路线.BLV = BLV.Text;
            路线.petID = 选择ID;
            new DataProcess().AddEW(路线);
            MessageBox.Show("保存成功!");
        }
    }
}
