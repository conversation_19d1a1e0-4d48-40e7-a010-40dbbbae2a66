﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shikong.Pokemon2.PCG
{
    public class MallsRestrict//限制购买-终身购买上限
    {
        public string 限购货币类型 { get; set; }
        public string 道具ID { get; set; }
        public int 购买上限 { get; set; }//如果需要增加购买次数修改上限就好了
        /*
         * 如果要每次活动都重置,思路一：每次活动更新时重置玩家限购配置
         */
    }
}
