﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using static download.tools;

namespace DLL
{
    public interface IClass
    {
        string DecryptRC4(string str, string key);
        string EncryptRC4(string str, string key);
        string GetStringHash(string str);
    }

    [ClassInterface(ClassInterfaceType.None)]
    public class tools : IClass
    {
        public string DecryptRC4(string str,string key) {
            return SkCryptography.DES.DecryptRC4(str, key);
        }
        public string EncryptRC4(string str, string key)
        {
            return SkCryptography.DES.EncryptRC4(str, key);
        }
        public string GetStringHash(string str)
        {
            try
            {
                byte[] data = new MD5CryptoServiceProvider().ComputeHash(Encoding.UTF8.GetBytes(str));

                // Create a new Stringbuilder to collect the bytes
                // and create a string.
                StringBuilder sBuilder = new StringBuilder();

                // Loop through each byte of the hashed data 
                // and format each one as a hexadecimal string.
                foreach (byte t in data)
                {
                    sBuilder.Append(t.ToString("x2"));
                }

                // Return the hexadecimal string.
                return sBuilder.ToString();
            }
            catch (Exception)
            {
                return "-1";
            }
        }
    }

}
