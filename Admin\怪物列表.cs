﻿using System;
using System.Collections.Generic;
using System.Windows.Forms;
using Shikong.Pokemon2.PCG;

namespace Admin
{
    public partial class 怪物列表 : Form
    {
        List<MonsterType> 道具 = new DataProcess().Get_MTList();
        public string 类型 = "怪物序号";
        public TextBox TEXT;
        private void 道具列表_Load(object sender, EventArgs e)
        {

            dataGridView1.DataSource = 道具;
        }
           public 怪物列表()
        {
            InitializeComponent();
        }

        private void dataGridView1_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {


        }

        private void dataGridView1_Click(object sender, EventArgs e)
        {

        }

        private void dataGridView1_CellClick(object sender, DataGridViewCellEventArgs e)
        {

        }

        private void dataGridView1_CellContentDoubleClick(object sender, DataGridViewCellEventArgs e)
        {

        }

        private void dataGridView1_CellMouseDoubleClick(object sender, DataGridViewCellMouseEventArgs e)
        {
            try
            {
                if (
                    e.RowIndex > -1)
                {
                    if (类型.Equals("怪物序号"))
                    {
                        TEXT.Text = 道具[e.RowIndex].怪物序号;
                    }
                    else
                    {
                        TEXT.Text = 道具[e.RowIndex].怪物名字;
                    }
                    Close();
                }
            }
            catch (Exception ex)
            {

            }
        }

        private void button1_Click(object sender, EventArgs e)
        {
            try
            {
                if (dataGridView1.SelectedRows.Count > 0)
                {
                    string 序号 = "";
                    foreach (DataGridViewRow r in dataGridView1.SelectedRows)
                    {
                        if (类型.Equals("怪物序号"))
                        {
                            序号 = 序号 + r.Cells[类型].Value.ToString() + "|";
                        }
                        else
                        {
                            序号 = 序号 + r.Cells[类型].Value.ToString() + "、";
                        }
                    }
                    序号 = 序号 + "|";
                    序号 = 序号.Replace("||", "");
                    序号 = 序号.Replace("、|", "");
                    TEXT.Text += "|" + 序号;
                    Close();
                }
            }
            catch (Exception ex)
            {

            }
        }
    }
}
