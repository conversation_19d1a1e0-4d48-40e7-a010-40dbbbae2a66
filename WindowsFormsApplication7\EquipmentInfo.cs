﻿using Shikong.Pokemon2.PCG.装备宝石;
using System;
using System.Collections.Generic;

namespace Shikong.Pokemon2.PCG
{
    public class EquipmentInfo
    {
        public string ID { get; set; }
        public string 类ID { get; set; }
        public string 强化 { get; set; }
        /// <summary>
        /// 宠物id
        /// </summary>
        public string cID { get; set; }
        public string Name
        {
            get
            {
                if (Tp == null) Tp = new DataProcess().GetAET(类ID);
                if (Tp != null) return Tp.名字;
                return "加载失败";
            }
            set { }
        }
        public string ICO
        {
            get {
                if (Tp == null) Tp = new DataProcess().GetAET(类ID);
                if (Tp != null) return Tp.ICO;
                return "加载失败";
            }
            set { }
        }
        public string 类型
        {
            get
            {
                if (Tp == null) Tp = new DataProcess().GetAET(类ID);
                if (Tp != null) return Tp.类型;
                return "加载失败";
            }
            set { }
        }
        public string WX { get; set; }

        public string LSSX { get; set; }
        public List<String> 宝石列表 { get; set; }
        public int 扩展槽位 { get; set; }
        public EquipmentType Tp;
        //public String Gem  { get; set; }
    }
}
