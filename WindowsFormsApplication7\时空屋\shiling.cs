﻿using Newtonsoft.Json;
using Shikong.Pokemon2.PCG;
using Shikong.Pokemon2.PCG.时空屋;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ShikongPlus.Pokemon2.PCG.时空屋
{
    public class shiling
    {
        public string Name { get; set; }
        public int 帧数 { get; set; }
        public int 播放延迟 { get; set; }
        public int 注灵最高等级 { get; set; }
        public int 赐福最高等级 { get; set; }
        public int 羁绊最高等级 { get; set; }
        public Dictionary<int, Dictionary<String, double>> 等级区间配置 { get; set; }
        public Dictionary<int, string> 赐福礼包配置 { get; set; }//等级|礼包ID
        public Dictionary<int, 羁绊属性> 羁绊配置 { get; set; }

        /// <summary>
        /// 获取注灵等级
        /// </summary>
        /// <returns></returns>
        public static int getZhuLingLevel(ShiLingInfo l)
        {
            var zl = GetShiLingCfg().FirstOrDefault(n => n.Name.Equals(l.Name));
            if (zl == null) return -1;//没有配置
            //根据等级配置每一级是100
            if (l.注灵经验 >= zl.注灵最高等级*100) return zl.注灵最高等级;
            else if (l.注灵经验 >= 100) return l.注灵经验 / 100;
            else return 0;
        }
        /// <summary>
        /// 获取羁绊等级
        /// </summary>
        /// <param name="l"></param>
        /// <returns></returns>
        public static int getJiBanLevel(ShiLingInfo l)
        {
            var jb = GetShiLingCfg().FirstOrDefault(n => n.Name.Equals(l.Name));
            if (jb == null) return -1;//没有配置
            //一级是500，每级递增200
            if (l.羁绊经验 >= 500 + (jb.羁绊最高等级 - 1) * 200) return jb.羁绊最高等级;
            else if(l.羁绊经验 >= 500)
            {
                //(经验-500)/200+1就是等级了
                return (l.羁绊经验 - 500) / 200 + 1;
            }
            else return 0;
        }
        /// <summary>
        /// 获取赐福等级
        /// </summary>
        /// <param name="l"></param>
        /// <returns></returns>
        public static int getCiFuLevel(ShiLingInfo l)
        {
            var cf = GetShiLingCfg().FirstOrDefault(n => n.Name.Equals(l.Name));
            if (cf == null) return -1;//没有配置
            //根据等级配置每一级是100
            if (l.赐福经验 >= 300 +( cf.赐福最高等级-1) * 100) return cf.赐福最高等级;
            else if (l.赐福经验 >= 100) return (l.赐福经验-300) / 100 + 1;
            else return 0;

        }
        public const string ZL_Path = @"PageMain\equip_5.qingshan"; //注灵配置
        /// <summary>
        /// 获取注灵配置
        /// </summary>
        public static List<shiling> GetShiLingCfg()
        {
            string cfg = new DataProcess().ReadFile(DataProcess.pf + ZL_Path);

            if (cfg == null || cfg.Length <= 0)
            {
                return new List<shiling>();
            }
            cfg = SkRC4.DES.DecryptRC4(cfg, new DataProcess().GetKey(1));
            var zl = JsonConvert.DeserializeObject<List<shiling>>(cfg);
            return zl;

        }
        /// <summary>
        /// 获取对应等级的赐福道具ID
        /// </summary>
        /// <param name="l"></param>
        /// <returns>不符合返回-1，否则返回道具ID</returns>
        public static string getCiFuGift_PropID(ShiLingInfo l)
        {
            int lv = getCiFuLevel(l);
            var cfGift = GetShiLingCfg().FirstOrDefault(n => n.Name.Equals(l.Name));
            if (lv == -1 || cfGift == null) return null;
            string pid = "0";
            foreach (var item in cfGift.赐福礼包配置)
            {
                if (lv >= item.Key) pid = item.Value;//一直遍历到最后就是最高一级的礼包
            }
            return pid;
        }

        /// <summary>
        /// 获取对应等级的赐福道具ID
        /// </summary>
        /// <param name="l"></param>
        /// <returns>不符合返回-1，否则返回道具ID</returns>
        public static string getCiFuGift_PropID_(ShiLingInfo l)
        {
            int lv = getCiFuLevel(l);
            var cfGift = GetShiLingCfg().FirstOrDefault(n => n.Name.Equals(l.Name));
            if (lv == -1 || cfGift == null) return null;
            string pid = "0";
            List<String> pids = new List<String>();
            foreach (var item in cfGift.赐福礼包配置)
            {
                if (lv >= item.Key) pids.Add(item.Value);
            }
            //这是正常随机的情况，但是可能会被回档
            //int num = DataProcess.RandomGenerator.Next(0, pids.Count);

            //如果只有一种可能性的情况下就不生成随机数
            if (pids.Count>1 && (l.礼盒随机值 == null || l.礼盒随机值.Count == 0 || l.赐福领取次数>=l.礼盒随机值.Count)) {
                updateCiFuGiftRandomNum(l);
            }

            //按照天数去获取已经生成好的随机数
            //这样就算玩家发现了规律，因为无法知道未来十五天的情况，回档的收益降低，也就没人去回档了
            if (pids.Count > 1) pid = pids[l.礼盒随机值[l.赐福领取次数]];
            else pid = pids[0];

            if(pids.Count > 1) l.赐福领取次数++;

            return pid;
        }

        /// <summary>
        /// 刷新随机数组
        /// </summary>
        /// <param name="l"></param>
        /// <returns>执行成功情况</returns>
        public static bool updateCiFuGiftRandomNum(ShiLingInfo l)
        {
            int lv = getCiFuLevel(l);
            var cfGift = GetShiLingCfg().FirstOrDefault(n => n.Name.Equals(l.Name));
            if (lv == -1 || cfGift == null) return false;
            string pid = "0";
            List<String> pids = new List<String>();
            foreach (var item in cfGift.赐福礼包配置)
            {
                if (lv >= item.Key) pids.Add(item.Value);
            }
            l.礼盒随机值 = new List<int>();
            //第一个随机数必定是最好的礼盒，这样就没动机在第一个就回档了
            //其实也就相当于每15天就必然有一个好的
            l.礼盒随机值.Add(pids.Count-1);

            //生成接下来十四天的随机数
            for (int i = 0; i < 14; i++)
            {
                int randomNumber = DataProcess.RandomGenerator.Next(0, pids.Count);
                l.礼盒随机值.Add(randomNumber);
            }
            l.赐福领取次数 = 0;

            return true;

        }

        /// <summary>
        /// 增加经验值，1注灵2赐福3羁绊
        /// </summary>
        /// <param name="name"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        public static string AddExp(string name,int type,int num)
        {
            if (num < 1) return "阁下难道想零元购？";
            var user = new DataProcess().ReadUserInfo();
            var ling = user.侍灵信息.FirstOrDefault(n => n.Name.Equals(name));
            if (ling == null) return "侍灵不存在！";
            var slCfg = GetShiLingCfg().FirstOrDefault(n => n.Name.Equals(ling.Name));
            if (slCfg == null) return "侍灵配置不存在，请联系青衫！";
            //注灵 2024030807  赐福  2024030808  羁绊  2024030809  
            string zl_p = "2024030807", cf_p = "2024030808", jb_p = "2024030809";
            //处理注灵
            if (type == 1)
            {
                var propType = new DataProcess().GetAPType(zl_p);
                if (propType == null)
                {
                    return "所需道具不存在于当前版本，请联系管理员检查！";
                }
                if (getZhuLingLevel(ling) >= slCfg.注灵最高等级) return "注灵已经是最高等级啦！";
                var prop = new DataProcess().GetAP_ID(zl_p);
                if (prop == null || Convert.ToInt32(prop.道具数量) < num)
                {
                    return "强化注灵所需道具不足！";
                }
                
                string[] script = new DataProcess().ReadPropScript(propType.道具序号).道具脚本.Split('|');
                int addExp = 0;
                for (int i =0; i< num; i++)
                {
                    addExp += DataProcess.RandomGenerator.Next(Convert.ToInt32(script[1]), Convert.ToInt32(script[2]) + 1);
                }
                new DataProcess().ReviseOrDeletePP(prop, num);//扣掉道具
                ling.注灵经验 += addExp;
                new DataProcess().SaveUserDataFile(user); 
                return "强化注灵成功，增加经验值：" + addExp;
            }
            //处理赐福
            if (type == 2)
            {

                //记录使用道具之前的等级获取到的最好的道具
                var thisLvProp = getCiFuGift_PropID(ling);
                var propType = new DataProcess().GetAPType(cf_p);
                if (propType == null)
                {
                    return "所需道具不存在于当前版本，请联系管理员检查！";
                }
                if (getCiFuLevel(ling) >= slCfg.赐福最高等级) return "赐福已经是最高等级啦！";
                var prop = new DataProcess().GetAP_ID(cf_p);
                if (prop == null || Convert.ToInt32(prop.道具数量) < num)
                {
                    return "强化赐福所需道具不足！";
                }
                string[] script = new DataProcess().ReadPropScript(propType.道具序号).道具脚本.Split('|');
                int addExp = 0;
                for (int i = 0; i < num; i++)
                {
                    addExp += DataProcess.RandomGenerator.Next(Convert.ToInt32(script[1]), Convert.ToInt32(script[2]) + 1);
                }
              
                new DataProcess().ReviseOrDeletePP(prop, num);//扣掉道具
                ling.赐福经验 += addExp;

                //获取使用道具之后的等级
                var lvProp = getCiFuGift_PropID(ling);

                //如果升级了可能会影响到升级后获取到的最好道具，因此要重新获取随机数
                if (!thisLvProp.Equals("0") && lvProp != thisLvProp)
                {
                    updateCiFuGiftRandomNum(ling);

                }
                new DataProcess().SaveUserDataFile(user);
              
                return "强化赐福成功，增加经验值：" + addExp;
            }
            //羁绊赐福
            if (type == 3)
            {
                var propType = new DataProcess().GetAPType(jb_p);
                if (propType == null)
                {
                    return "所需道具不存在于当前版本，请联系管理员检查！";
                }
                if (getJiBanLevel(ling) >= slCfg.羁绊最高等级) return "羁绊已经是最高等级啦！";
                var prop = new DataProcess().GetAP_ID(jb_p);
                if (prop == null || Convert.ToInt32(prop.道具数量) < num)
                {
                    return "强化羁绊所需道具不足！";
                }
                string[] script = new DataProcess().ReadPropScript(propType.道具序号).道具脚本.Split('|');
                int addExp = 0;
                for (int i = 0; i < num; i++)
                {
                    addExp += DataProcess.RandomGenerator.Next(Convert.ToInt32(script[1]), Convert.ToInt32(script[2]) + 1);
                }
                new DataProcess().ReviseOrDeletePP(prop, num);//扣掉道具
                ling.羁绊经验 += addExp;
                new DataProcess().SaveUserDataFile(user);
                return "强化羁绊成功，增加经验值：" + addExp;
            }
            return "该操作无法处理。";
        }
        /// <summary>
        /// 计算所有属性
        /// </summary>
        /// <returns>返回一个词典，代表每个属性的值</returns>
        public static Dictionary<String, double> calcALL()
        {
            //计算玩家所有灵的属性
            var user = new DataProcess().ReadUserInfo();
            var zl = GetShiLingCfg();

            var calcResult = new Dictionary<String, double>();
            calcResult.Add("攻击", 0);
            calcResult.Add("命中", 0);
            calcResult.Add("防御", 0);
            calcResult.Add("速度", 0);
            calcResult.Add("闪避", 0);
            calcResult.Add("生命", 0);
            calcResult.Add("魔法", 0);
            calcResult.Add("加深", 0);
            calcResult.Add("抵消", 0);
            calcResult.Add("吸血", 0);
            calcResult.Add("吸魔", 0);
            if(zl == null || zl.Count == 0) return calcResult;//没有配置信息
            foreach (var l in user.侍灵信息)
            {
                //这里拿到对应灵的配置信息
                var l_cfg = zl.FirstOrDefault(n => n.Name.Equals(l.Name));
                //计算注灵属性
                int lv = getZhuLingLevel(l);
                if (lv > 0)
                {
                   
                    if (l_cfg == null) continue;
                    //计算注灵等级属性
                    int LastLv = 0;
                    foreach (var k in l_cfg.等级区间配置)
                    {
                        //判断等级与区间等级，计算差后计算属性
                        if (lv > LastLv)
                        {
                            //如果等级超过该区间，直接算满该区间的属性
                            if(lv > k.Key)
                            {
                                foreach (var item in k.Value)
                                {
                                    calcResult[item.Key] += item.Value * (k.Key- LastLv);//属性加成*(本次区间等级上限-上次等级上限)
                                }
                            }
                            //如果没有超过就计算差
                            else
                            {
                                //int sub_lv = lv - LastLv;//加成等级
                                foreach (var item in k.Value)
                                {
                                    calcResult[item.Key] += item.Value * (lv - LastLv);//属性加成*(等级-上次区间等级上限)
                                }
                            }
                            
                        }
                        LastLv = k.Key;//更新上次区间等级上限
                    }
                }
                int jbLv = getJiBanLevel(l);
                if(jbLv > 0)
                {
                    //计算羁绊属性
                    foreach (var k in l_cfg.羁绊配置)
                    {
                        if(jbLv >= k.Key)
                        {
                            foreach (var item in k.Value.属性)
                            {
                                calcResult[item.Key] += item.Value;//计算属性
                            }
                        }
                    }
                }
            }
            return calcResult;
        }

        /// <summary>
        /// 地图属性削弱
        /// </summary>
        /// <returns></returns>
        public static Dictionary<String, double> Map_Attributes_Dec(int mapID)
        {
            //计算玩家所有灵的属性
            var user = new DataProcess().ReadUserInfo();
            var zl = GetShiLingCfg();

            var calcResult = new Dictionary<String, double>();
            calcResult.Add("攻击", 0);
            calcResult.Add("命中", 0);
            calcResult.Add("防御", 0);
            calcResult.Add("速度", 0);
            calcResult.Add("闪避", 0);
            calcResult.Add("生命", 0);
            calcResult.Add("魔法", 0);
            calcResult.Add("加深", 0);
            calcResult.Add("抵消", 0);
            calcResult.Add("吸血", 0);
            calcResult.Add("吸魔", 0);
            foreach (var l in user.侍灵信息)
            {
                //这里拿到对应灵的配置信息
                var l_cfg = zl.FirstOrDefault(n => n.Name.Equals(l.Name));
                int jbLv = getJiBanLevel(l);
                if (jbLv > 0)
                {
                    //计算羁绊属性
                    foreach (var k in l_cfg.羁绊配置)
                    {
                        //大于就代表激活了，然后判断有没有地图属性削弱的属性
                        if (jbLv > k.Key && !string.IsNullOrEmpty(k.Value.地图属性削弱))
                        {
                            //这里处理两种情况，有全属性和没全属性
                            if (k.Value.地图属性削弱.Contains("全属性"))
                            {
                                //地图ID|全属性|降低的属性百分比
                                string[] att = k.Value.地图属性削弱.Split('|');
                                //这里必定等于3，其他情况都返回
                                if (att.Length != 3) return calcResult;
                                
                                foreach (var sx in calcResult)
                                {
                                    //削弱地图对的上才行或者配置全图
                                    if (Convert.ToInt32(att[0]) == mapID || att[0].Equals("全图"))
                                    {
                                        calcResult[sx.Key] += Convert.ToDouble(att[2]);//配置百分比
                                    }
                                }
                            }
                            //非全属性的情况，这里使用,分割需要削弱的属性。（生命,攻击,防御等）
                            else
                            {
                                //地图ID|属性,属性,属性,属性...|降低的属性百分比
                                string[] att = k.Value.地图属性削弱.Split('|');
                                //这里必定等于3，其他情况都返回
                                if (att.Length != 3) return calcResult;
                                //削弱地图对的上才行或者配置全图
                                if (Convert.ToInt32(att[0]) == mapID || att[0].Equals("全图"))
                                {
                                    //这里对属性分割
                                    string[] att_ = att[1].Split(',');
                                    foreach (var sx in att_)//对指定削弱的属性遍历
                                    {
                                        calcResult[sx] += Convert.ToDouble(att[2]);//配置百分比
                                    }
                                    
                                }
                                
                            }
                        }
                        
                    }
                }
            }
            return calcResult;
        }

        
        public class 羁绊属性
        {
            public Dictionary<string,double> 属性 { get; set; }
            public string 图标 { get; set; }
            public string 地图属性削弱 { get; set; }//如果不包含全属性则是属性的json
            public string 自定义说明 { get; set; }

        }

    }
}
