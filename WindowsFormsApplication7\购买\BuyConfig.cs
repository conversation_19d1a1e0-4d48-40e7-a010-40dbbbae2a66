﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ShikongPlus.Pokemon2.PCG.购买
{
    /// <summary>
    /// 购买系统配置主类
    /// 包含所有购买相关的配置信息，支持从服务器动态加载
    /// 用于替代Form1.cs中buy函数的硬编码逻辑
    /// </summary>
    public class BuyConfig
    {
        /// <summary>
        /// 商店类型配置
        /// 定义各种商店的类型ID和名称映射
        /// 包括元宝商店、水晶商店、结晶商店等
        /// </summary>
        public ShopTypeConfig ShopTypes { get; set; }

        /// <summary>
        /// 特殊道具映射配置列表
        /// 定义特殊商店中商品与所需道具的映射关系
        /// 例如：神龙宝匣需要神龙宝藏钥匙才能购买
        /// </summary>
        public List<SpecialItemMapping> SpecialItems { get; set; }

        /// <summary>
        /// 消息配置
        /// 定义购买过程中各种提示消息的文本内容
        /// 支持动态修改错误提示、成功提示等
        /// </summary>
        public MessageConfig Messages { get; set; }

        /// <summary>
        /// 购买限制配置
        /// 定义各种购买数量限制和约束条件
        /// 包括普通商店和特殊商店的购买上限
        /// </summary>
        public PurchaseLimitConfig Limits { get; set; }

        /// <summary>
        /// 特殊事件配置
        /// 定义特殊时期的购买逻辑，如愚人节活动
        /// 支持自定义事件的动态开关
        /// </summary>
        public SpecialEventConfig SpecialEvents { get; set; }
    }
}
