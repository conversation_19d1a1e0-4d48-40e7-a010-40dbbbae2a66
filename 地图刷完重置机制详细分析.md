# 宠物时空游戏地图刷完重置机制详细分析

## 1. 系统概述

宠物时空游戏包含多种地图类型的自动重置机制，主要包括：
- **普通副本地图** (FBMap)
- **地狱之门** (Hell)  
- **通天塔** (TT)

每种地图类型都有独特的进度跟踪和自动重置逻辑。

## 2. 核心数据结构

### 2.1 FBROP 类 (副本进度)
```csharp
public class FBROP
{
    public string id { get; set; }    // 地图ID
    public string num { get; set; }   // 当前进度/层数
}
```

### 2.2 AutoMapInfo 类 (自动地图配置)
```csharp
public class AutoMapInfo
{
    public int mapID { get; set; }      // 地图ID
    public string mapName { get; set; } // 地图名称
    public int maxNum { get; set; }     // 最大层数
    public string propID { get; set; }  // 重置所需道具ID
}
```

### 2.3 关键静态变量
```csharp
internal static bool FBMap = false;     // 是否为副本地图
internal static bool AutoMap = false;   // 是否开启自动副本
internal static int MapFloor;           // 副本目标层数
internal static bool AutoHell = false;  // 是否开启自动地狱
internal static int HellFloor;          // 地狱目标层数
internal static bool AutoTT = false;    // 是否开启自动通天
internal static int TTFloor;            // 通天目标层数
```

## 3. 副本地图重置机制

### 3.1 副本进度跟踪

<augment_code_snippet path="WindowsFormsApplication7/Fight.cs" mode="EXCERPT">
````csharp
if (FBMap) {
    var info = new DataProcess().GetFBROP(地图);
    int num = info == null || info.num == null ? 0 : Convert.ToInt32(info.num);
    long tmp = num + 1;
    
    if (AutoMap && tmp >= MapFloor)  // 达到目标层数
    {
        var r = DataProcess.AutoMap.FirstOrDefault(C => C.mapID.ToString() == 地图);
        if (r == null)
        {
            DataProcess.GameForm.发送红色公告("当前副本不能自动，请联系管理员增加本地图的自动支持。");
        }
        else {
            PropInfo ttkey = new DataProcess().GetAP_ID(r.propID);
            
            if (ttkey == null || Convert.ToInt32(ttkey.道具数量) < 1)
            {
                DataProcess.GameForm.发送红色公告("钥匙不足，自动副本已结束。");
                AutoMap = false;
                结果.Auto = 2;
            }
            else
            {
                new DataProcess().ReviseOrDeletePP(ttkey, 1);  // 消耗重置道具
                DataProcess.GameForm.发送红色公告("自动副本功能已帮您自动重置一次副本。");
                new DataProcess().ChangeROP(地图, "-1");  // 重置副本进度
            }
        }
    }
}
````
</augment_code_snippet>

### 3.2 副本进度更新逻辑

<augment_code_snippet path="WindowsFormsApplication7/Fight.cs" mode="EXCERPT">
````csharp
private static void ChangeFbProgress()  // 触发地狱卡住
{
    if (FBMap && 地图 != "地狱之门" && 地图 != "通天塔")
    {
        FBROP 当前层数 = new DataProcess().GetFBROP(地图);
        
        // 检查是否达到地图怪物总数
        if (当前层数 != null && new DataProcess().GetAMML(地图).Count <= Convert.ToInt32(当前层数.num) + 1)
        {
            new DataProcess().ChangeROP(地图, "-10");  // 标记副本完成
        }
        else
        {
            new DataProcess().PromoteROP(地图);  // 推进副本进度
        }
    }
}
````
</augment_code_snippet>

### 3.3 副本重置条件

1. **自动模式开启**: `AutoMap = true`
2. **达到目标层数**: `当前进度 >= MapFloor`
3. **地图支持自动**: 在`DataProcess.AutoMap`配置中存在
4. **拥有重置道具**: 指定的重置道具数量 >= 1

### 3.4 副本状态值含义

- **正常进度**: 0, 1, 2, 3... (表示当前战斗的怪物序号)
- **副本完成**: -10 (表示副本已刷完，等待重置)
- **重置状态**: -1 (重置副本到初始状态)

## 4. 地狱之门重置机制

### 4.1 地狱层数管理

<augment_code_snippet path="WindowsFormsApplication7/Fight.cs" mode="EXCERPT">
````csharp
if (Hell)
{
    if (String.IsNullOrEmpty(user.地狱层数))
    {
        user.地狱层数 = "2";  // 初始化为第2层
    }
    else
    {
        long tmp = Convert.ToInt64(user.地狱层数) + 1;
        
        // 更新历史最高层数
        if (tmp >= Convert.ToInt64(user.历史地狱层数))
        {
            user.历史地狱层数 = tmp.ToString();
        }
        
        if (AutoHell && tmp > HellFloor)  // 超过目标层数
        {
            PropInfo dykey = new DataProcess().GetAP_ID("2016101705");  // 地狱钥匙
            if (dykey == null || Convert.ToInt32(dykey.道具数量) < 1)
            {
                DataProcess.GameForm.发送红色公告("钥匙不足，自动地狱已结束。");
                AutoHell = false;
                user.地狱层数 = tmp.ToString();
                结果.Auto = 2;
            }
            else
            {
                new DataProcess().ReviseOrDeletePP(dykey, 1);  // 消耗地狱钥匙
                DataProcess.GameForm.发送红色公告("自动地狱功能已帮您自动重置一次地狱之门。");
                user.地狱层数 = "1";  // 重置到第1层
            }
        }
        else
        {
            user.地狱层数 = tmp.ToString();  // 正常推进层数
        }
    }
}
````
</augment_code_snippet>

### 4.2 地狱重置条件

1. **自动模式开启**: `AutoHell = true`
2. **超过目标层数**: `当前层数 > HellFloor`
3. **拥有地狱钥匙**: 道具ID "2016101705" 数量 >= 1

## 5. 通天塔重置机制

### 5.1 通天塔层数管理

<augment_code_snippet path="WindowsFormsApplication7/Fight.cs" mode="EXCERPT">
````csharp
if (TT)
{
    if (String.IsNullOrEmpty(user.TTT))
    {
        user.TTT = "2";  // 初始化为第2层
    }
    else
    {
        long tmp = Convert.ToInt64(user.TTT) + 1;
        
        if (AutoTT && tmp > TTFloor)  // 超过目标层数
        {
            PropInfo ttkey = new DataProcess().GetAP_ID("2018021701");  // 通天钥匙
            if (ttkey == null || Convert.ToInt32(ttkey.道具数量) < 1)
            {
                DataProcess.GameForm.发送红色公告("钥匙不足，自动通天已结束。");
                AutoTT = false;
                user.TTT = tmp.ToString();
                结果.Auto = 2;
            }
            else
            {
                new DataProcess().ReviseOrDeletePP(ttkey, 1);  // 消耗通天钥匙
                DataProcess.GameForm.发送红色公告("自动通天功能已帮您自动重置一次通天塔。");
                user.TTT = "1";  // 重置到第1层
            }
        }
        else
        {
            user.TTT = tmp.ToString();  // 正常推进层数
        }
    }
}
````
</augment_code_snippet>

### 5.2 通天塔重置条件

1. **自动模式开启**: `AutoTT = true`
2. **超过目标层数**: `当前层数 > TTFloor`
3. **拥有通天钥匙**: 道具ID "2018021701" 数量 >= 1

## 6. 数据存储机制

### 6.1 副本进度存储

副本进度存储在用户存档的第5个分段中：

```csharp
internal FBROP GetFBROP(string mapId)
{
    string 存档 = GetStr();
    string[] 存档组 = 存档.Split(new[] { "O4F89" }, StringSplitOptions.None);
    存档 = 存档组[5];  // 第5个分段存储副本进度
    
    存档 = SkRC4.DES.DecryptRC4(存档, new DataProcess().GetKey(1, true) + GetKey1(5));
    var 进度 = JsonConvert.DeserializeObject<List<FBROP>>(存档);
    
    return 进度.FirstOrDefault(副本 => 副本.id == mapId);
}
```

### 6.2 存档结构

用户存档使用"O4F89"分隔，副本进度位于第5个位置：
```
基础信息O4F89宠物信息O4F89道具信息O4F89存档版本O4F89装备信息O4F89副本进度O4F89任务信息
```

### 6.3 进度操作方法

```csharp
// 修改副本进度
internal bool ChangeROP(string mapId, string floor)
{
    FBROP fbInfo = GetFBROP(mapId);
    List<FBROP> rop = GetFBROPList();
    
    if (fbInfo == null)
    {
        fbInfo = new FBROP() { id = mapId, num = floor };
        rop.Add(fbInfo);
    }
    else
    {
        foreach (FBROP t in rop)
        {
            if (t.id != null && t.id.Equals(mapId))
            {
                t.num = floor;  // 设置新的进度值
            }
        }
    }
    
    return SaveFBROPList(rop);
}

// 推进副本进度
internal bool PromoteROP(string mapId)
{
    FBROP fbInfo = GetFBROP(mapId);
    List<FBROP> rop = GetFBROPList();
    int Floor = 1;
    
    if (fbInfo == null)
    {
        fbInfo = new FBROP() { id = mapId, num = Floor.ToString() };
        rop.Add(fbInfo);
    }
    else
    {
        foreach (FBROP t in rop)
        {
            if (t.id != null && t.id.Equals(mapId))
            {
                t.num = (Convert.ToInt32(t.num) + Floor).ToString();  // 进度+1
            }
        }
    }
    
    return SaveFBROPList(rop);
}
```

## 7. 自动重置配置

### 7.1 AutoMap配置

`DataProcess.AutoMap`存储了支持自动重置的副本配置：

```csharp
public static List<AutoMapInfo> AutoMap = new List<AutoMapInfo>();
```

每个配置包含：
- `mapID`: 地图ID
- `mapName`: 地图名称  
- `maxNum`: 最大层数
- `propID`: 重置所需的道具ID

### 7.2 重置道具

不同地图类型使用不同的重置道具：
- **地狱之门**: "2016101705" (地狱钥匙)
- **通天塔**: "2018021701" (通天钥匙)
- **普通副本**: 根据AutoMap配置中的propID

## 8. 触发时机和调用链

### 8.1 主要调用链

```
发招(string 技能id)
  ↓ (怪物生命 <= 0)
  ↓ (战斗胜利处理)
  ↓ (地狱/通天塔层数更新)
  ↓ (副本进度处理)
  ↓ (第1244行)
ChangeFbProgress()
  ↓
副本进度推进或完成标记
```

### 8.2 重置触发条件

1. **战斗胜利**: 怪物生命值 <= 0
2. **自动模式开启**: 对应的Auto标志为true
3. **达到目标条件**: 层数或进度达到设定值
4. **拥有重置道具**: 相应道具数量充足

## 9. 错误处理和用户反馈

### 9.1 错误情况处理

1. **道具不足**: 
   - 发送红色公告提示
   - 关闭自动模式
   - 设置结果状态为2

2. **地图不支持自动**:
   - 发送红色公告提示管理员配置
   - 不执行重置操作

3. **数据异常**:
   - 初始化默认值
   - 继续正常流程

### 9.2 用户通知

系统会通过游戏公告通知用户：
- "自动副本功能已帮您自动重置一次副本。"
- "自动地狱功能已帮您自动重置一次地狱之门。"
- "自动通天功能已帮您自动重置一次通天塔。"
- "钥匙不足，自动XXX已结束。"

## 10. 性能和安全考虑

### 10.1 数据完整性

- 所有进度数据都经过RC4加密存储
- 使用JSON序列化确保数据结构完整
- 异常情况下有默认值保护

### 10.2 防作弊机制

- 重置需要消耗实际道具
- 进度推进基于实际战斗结果
- 层数更新有历史记录验证

### 10.3 性能优化

- 进度数据按需加载
- 批量更新减少IO操作
- 缓存机制避免重复计算

---

## 总结

宠物时空的地图重置机制是一个完整的自动化系统，包含：

1. **多类型支持**: 副本、地狱、通天塔三种不同的重置机制
2. **智能判断**: 根据进度和配置自动判断是否需要重置
3. **资源消耗**: 通过道具消耗平衡游戏经济
4. **用户友好**: 详细的状态反馈和错误提示
5. **数据安全**: 加密存储和完整性保护

这个系统设计精良，既提供了便利的自动化功能，又保持了游戏的平衡性和公平性。
