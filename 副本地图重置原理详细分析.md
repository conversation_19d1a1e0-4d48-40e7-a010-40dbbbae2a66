# 副本地图重置原理详细分析

## 1. 核心数据结构 - FBROP类

### 1.1 FBROP类定义

<augment_code_snippet path="WindowsFormsApplication7/FBROP.cs" mode="EXCERPT">
````csharp
public class FBROP
{
    public string id { get; set; }    // 地图ID
    public string num { get; set; }   // 当前进度/层数
}
````
</augment_code_snippet>

**FBROP** = **F**u**B**en **R**eset **O**r **P**rogress (副本重置或进度)

### 1.2 num字段的状态含义

- **0, 1, 2, 3...**: 正常进度，表示当前战斗的怪物序号
- **-10**: 副本已刷完，等待重置
- **-1**: 重置副本到初始状态

## 2. 副本进度判断机制

### 2.1 核心判断逻辑

<augment_code_snippet path="WindowsFormsApplication7/Fight.cs" mode="EXCERPT">
````csharp
private static void ChangeFbProgress()  // 触发地狱卡住
{
    if (FBMap && 地图 != "地狱之门" && 地图 != "通天塔")
    {
        FBROP 当前层数 = new DataProcess().GetFBROP(地图);
        
        // 关键判断：当前进度是否已达到地图怪物总数
        if (当前层数 != null && new DataProcess().GetAMML(地图).Count <= Convert.ToInt32(当前层数.num) + 1)
        {
            new DataProcess().ChangeROP(地图, "-10");  // 标记副本完成
        }
        else
        {
            new DataProcess().PromoteROP(地图);  // 推进副本进度
        }
    }
}
````
</augment_code_snippet>

### 2.2 判断公式解析

```
地图怪物总数 <= 当前进度 + 1
```

**举例说明**:
- 地图有5只怪物 (索引0-4)
- 当前进度为3 (已击败第4只怪物)
- 判断: 5 <= 3 + 1 = 4 (false，继续推进)
- 当前进度为4 (已击败第5只怪物)  
- 判断: 5 <= 4 + 1 = 5 (true，副本完成)

### 2.3 GetAMML方法 - 获取地图怪物列表

<augment_code_snippet path="WindowsFormsApplication7/DataProcess.cs" mode="EXCERPT">
````csharp
public List<MonsterInfo> GetAMML(string mapId, bool admin = false)  // 取指定地图怪物列表
{
    if (mapId == "test")
    {
        return JsonConvert.DeserializeObject<List<MonsterInfo>>(File.ReadAllText("testMonster.txt"));
    }
    
    if (admin)
    {
        return ProcessAMML(mapId);
    }
    else
    {
        if (!AMML.ContainsKey(mapId))  // 缓存机制
        {
            if (AMML.Count >= 10)
            {
                AMML.Clear();  // 防止内存溢出
            }
            List<MonsterInfo> tmp = ProcessAMML(mapId);
            AMML.Add(mapId, tmp);
            return tmp;
        }
        else
        {
            return AMML[mapId];  // 返回缓存数据
        }
    }
}
````
</augment_code_snippet>

## 3. 副本进度操作方法

### 3.1 PromoteROP - 推进副本进度

<augment_code_snippet path="WindowsFormsApplication7/DataProcess.cs" mode="EXCERPT">
````csharp
internal bool PromoteROP(string mapId)
{
    FBROP fbInfo = GetFBROP(mapId);
    List<FBROP> rop = GetFBROPList();
    int Floor = 1;  // 每次推进1层
    
    if (fbInfo == null)
    {
        // 首次进入副本，创建进度记录
        fbInfo = new FBROP() { id = mapId, num = Floor.ToString() };
        rop.Add(fbInfo);
    }
    else
    {
        // 推进现有进度
        foreach (FBROP t in rop)
        {
            if (t.id != null && t.id.Equals(mapId))
            {
                t.num = (Convert.ToInt32(t.num) + Floor).ToString();  // 进度+1
            }
        }
    }
    
    return SaveFBROPList(rop);
}
````
</augment_code_snippet>

### 3.2 ChangeROP - 修改副本进度

<augment_code_snippet path="WindowsFormsApplication7/DataProcess.cs" mode="EXCERPT">
````csharp
internal bool ChangeROP(string mapId, string floor)
{
    FBROP fbInfo = GetFBROP(mapId);
    List<FBROP> rop = GetFBROPList();
    
    if (fbInfo == null)
    {
        // 创建新的进度记录
        fbInfo = new FBROP() { id = mapId, num = floor };
        rop.Add(fbInfo);
    }
    else
    {
        // 修改现有进度
        foreach (FBROP t in rop)
        {
            if (t.id != null && t.id.Equals(mapId))
            {
                t.num = floor;  // 直接设置新值
            }
        }
    }
    
    return SaveFBROPList(rop);
}
````
</augment_code_snippet>

## 4. 自动重置机制

### 4.1 自动重置触发条件

<augment_code_snippet path="WindowsFormsApplication7/Fight.cs" mode="EXCERPT">
````csharp
if (FBMap) {
    var info = new DataProcess().GetFBROP(地图);
    int num = info == null || info.num == null ? 0 : Convert.ToInt32(info.num);
    long tmp = num + 1;
    
    if (AutoMap && tmp >= MapFloor)  // 达到目标层数
    {
        var r = DataProcess.AutoMap.FirstOrDefault(C => C.mapID.ToString() == 地图);
        if (r == null)
        {
            DataProcess.GameForm.发送红色公告("当前副本不能自动，请联系管理员增加本地图的自动支持。");
        }
        else {
            PropInfo ttkey = new DataProcess().GetAP_ID(r.propID);
            
            if (ttkey == null || Convert.ToInt32(ttkey.道具数量) < 1)
            {
                DataProcess.GameForm.发送红色公告("钥匙不足，自动副本已结束。");
                AutoMap = false;
                结果.Auto = 2;
            }
            else
            {
                new DataProcess().ReviseOrDeletePP(ttkey, 1);  // 消耗重置道具
                DataProcess.GameForm.发送红色公告("自动副本功能已帮您自动重置一次副本。");
                new DataProcess().ChangeROP(地图, "-1");  // 重置副本进度
            }
        }
    }
}
````
</augment_code_snippet>

### 4.2 重置条件总结

1. **FBMap = true**: 当前在副本地图中
2. **AutoMap = true**: 开启了自动副本功能
3. **tmp >= MapFloor**: 当前进度达到或超过目标层数
4. **地图支持自动**: 在AutoMap配置中存在该地图
5. **拥有重置道具**: 指定道具数量充足

## 5. 数据存储机制

### 5.1 存档结构

副本进度存储在用户存档的第5个分段：

```
基础信息O4F89宠物信息O4F89道具信息O4F89存档版本O4F89装备信息O4F89副本进度O4F89任务信息
```

### 5.2 GetFBROP - 获取副本进度

<augment_code_snippet path="WindowsFormsApplication7/DataProcess.cs" mode="EXCERPT">
````csharp
internal FBROP GetFBROP(string mapId)
{
    string 存档 = GetStr();
    string[] 存档组 = 存档.Split(new[] { "O4F89" }, StringSplitOptions.None);
    存档 = 存档组[5];  // 第5个分段存储副本进度
    
    存档 = SkRC4.DES.DecryptRC4(存档, new DataProcess().GetKey(1, true) + GetKey1(5));
    
    try
    {
        var 进度 = JsonConvert.DeserializeObject<List<FBROP>>(存档);
        foreach (FBROP 副本 in 进度)
        {
            if (副本.id == mapId)
            {
                return 副本;  // 返回指定地图的进度
            }
        }
    }
    catch
    {
        return null;  // 数据损坏时返回null
    }
    
    return null;  // 未找到该地图的进度记录
}
````
</augment_code_snippet>

### 5.3 SaveFBROPList - 保存进度列表

<augment_code_snippet path="WindowsFormsApplication7/DataProcess.cs" mode="EXCERPT">
````csharp
private bool SaveFBROPList(List<FBROP> 进度)
{
    string 存档 = jsonTo.ListToJson(进度);  // JSON序列化
    
    string 拼接 = GetStr();
    string[] 存档组 = 拼接.Split(new[] { "O4F89" }, StringSplitOptions.None);
    存档组[5] = 存档;  // 更新第5个分段
    
    if (AdminMode == 0)
    {
        JointDataFile(存档组, true);  // 保存到文件
    }
    
    return true;
}
````
</augment_code_snippet>

## 6. 副本类型判断

### 6.1 FBMap标志设置

副本地图通过以下方式识别：

```csharp
if (mapinfo.Type == "1")  // 地图类型为副本
{
    Fight.FBMap = true;
    fbFightID++;  // 副本战斗ID递增
    if (fbFightID >= 怪物列表.Count) fbFightID = 0;  // 循环重置
}
else
{
    Fight.FBMap = false;  // 普通地图
}
```

### 6.2 副本战斗ID机制

- **fbFightID**: 副本内的战斗序号
- **循环机制**: 当ID超过怪物总数时重置为0
- **顺序战斗**: 副本内按固定顺序遇到怪物

## 7. 完整的副本重置流程

### 7.1 流程图

```
战斗胜利
    ↓
ChangeFbProgress()
    ↓
获取当前进度(FBROP.num)
    ↓
获取地图怪物总数(GetAMML().Count)
    ↓
判断: 怪物总数 <= 当前进度 + 1?
    ↓               ↓
   是              否
    ↓               ↓
设置进度为-10      推进进度+1
(副本完成)        (继续战斗)
    ↓
检查自动重置条件
    ↓
消耗重置道具
    ↓
设置进度为-1
(重置副本)
```

### 7.2 状态转换

```
初始状态: null
    ↓
首次进入: 0
    ↓
战斗推进: 1 → 2 → 3 → ... → n
    ↓
副本完成: -10
    ↓
自动重置: -1
    ↓
重新开始: 0
```

## 8. 错误处理和边界情况

### 8.1 数据异常处理

1. **FBROP为null**: 创建新的进度记录，从0开始
2. **num为null**: 默认设置为0
3. **怪物列表为空**: 跳过进度判断
4. **存档损坏**: 返回null，使用默认值

### 8.2 边界情况

1. **单怪物地图**: 击败1只怪物后立即完成
2. **空地图**: 无怪物的地图不会触发进度更新
3. **重复进入**: 已完成的副本(-10状态)不会重复标记

## 9. 性能优化

### 9.1 缓存机制

- **AMML字典**: 缓存地图怪物列表，避免重复加载
- **容量限制**: 最多缓存10个地图，防止内存溢出
- **自动清理**: 超出限制时清空所有缓存

### 9.2 批量操作

- **批量保存**: 所有FBROP修改后统一保存
- **减少IO**: 避免频繁的文件读写操作

---

## 总结

副本地图重置机制的核心原理：

1. **FBROP类**: 存储每个副本的当前进度
2. **进度判断**: 通过比较当前进度与怪物总数判断是否完成
3. **状态管理**: 使用特殊值(-10, -1)标记完成和重置状态
4. **自动重置**: 达到条件时消耗道具自动重置副本
5. **数据持久化**: 加密存储在用户存档中

这个机制既保证了副本的正常推进，又提供了便利的自动重置功能，是一个设计精良的游戏系统。
