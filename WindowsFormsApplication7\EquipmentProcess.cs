﻿using System;
using System.Collections.Generic;
using System.Linq;
using PetShikongTools;

namespace Shikong.Pokemon2.PCG
{
    public static class EquipmentProcess
    {
        internal static string[] 玄龙苍珀 = {"生命", "防御", "速度"};
        internal static string[] 金水菩提 = {"生命", "防御", "闪避"};
        internal static string[] 凤羽流苏 = {"魔法", "攻击", "命中"};
        internal static string[] 九曜光华 = {"魔法", "攻击", "速度"};
        internal static string[] 四魂之玉 = { "魔法", "攻击", "速度" };


        private static Dictionary<string, int> FJBS = new Dictionary<string, int>//分解装备给的道具数量
        {
            {"2017070101", 1000}, //天魔
            {"2017063001", 1000}, //自然
            {"20170612", 800}, //黑白
            {"2016123001", 800}, //盛世
            {"20161230", 50}, //创世
            {"2016123101", 100}, //七宗罪
            {"20170214", 200}, //情人
            {"2017082201", 120}, //EFS
            {"2018032001", 1200}, //神圣
            {"2018060401", 1200}, //神圣卡牌
            {"2018092901", 800}, //刀剑
            {"2019100101", 800}, //魔童
            {"2018102701", 1400}, //沉睡
            {"2018102702", 1300}, //沉睡卡牌
            {"2019020101", 1200}, //佩奇
            {"2019060801", 1600}, //通天卡牌
            {"2019051901", 1600}, //通天
            {"2019061501", 1800}, //次元卡牌
            {"2019061502", 1800}, //次元
            {"2020092801", 2000}, //圣战卡牌
            {"2020090801", 2000}, //圣战卡牌
            {"2020092802", 2000}  //圣战
        };

        internal static Dictionary<string, string> EquimentAttribute = new Dictionary<string, string> //相生属性
            {{"生命", "金"}, {"魔法", "木"}, {"攻击", "火"}, {"防御", "土"}, {"命中", "雷"}, {"闪避", "水"}, {"速度", "风"}};

        internal static Dictionary<string, string> Restraint = new Dictionary<string, string> //相克属性
            {{"生命", "火"}, {"魔法", "金"}, {"攻击", "水"}, {"防御", "木"}, {"命中", "风"}, {"闪避", "土"}, {"速度", "雷"}};

        private static List<string> WXList = new List<string> {"金", "木", "火", "土", "雷", "水", "风"};
        private static double _lastTime;

        //private static string[] 成功率 = { "100", "96", "92", "88", "84", "80", "75", "71", "66", "61", "56", "51", "46", "40", "34", "28", "22", "15", "8", "1" };
        internal static string 强化(EquipmentInfo 装备1 /*, int jc, int bh*/)
        {
            //bool 掉级保护 = false;
            //int 成功率加成 = 0;

            //new AntiCheat().反作弊();
            if (_lastTime != 0)
            {
                if (new Tools.GetTime().GetSystemTS() - _lastTime < 200)
                {
                    return "强化CD未到!";
                }
            }

            if (string.IsNullOrEmpty(装备1.强化))
            {
                装备1.强化 = "0";
            }
            //Limitations

            //Equipment Self
            if (装备1.类型.Equals("卡牌右") || 装备1.类型.Equals("卡牌左") || 装备1.类型.Equals("灵饰") || 装备1.类型.Equals("法宝") || 装备1.类型.Equals("背饰"))
            {
                return "此部位装备无法被强化!";
            }

            if (装备1.强化.Equals("20"))
            {
                return "该装备已强化到最高等级,无法再强化!";
            }
            //取装备信息，判断是不是巫族装备
            var 装备2 = new DataProcess().GetAET(装备1.类ID);
            //Prop
            PropInfo qhs = new DataProcess().GetAP_ID("2017060302");
            short lv = Convert.ToInt16(装备1.强化);
            int qhsnum = 0;
            if (lv >= 0 && lv <= 4)
            {
                qhsnum = Convert.ToInt32(Math.Pow(lv + 1, 2) * 150);
            }
            else if (lv >= 5 && lv <= 19)
            {
                qhsnum = Convert.ToInt32(Math.Pow(lv + 1, 2) * 1050);
            }
            if (装备2.五行限制 == "巫") qhsnum *= 2;
            if (qhs == null || Convert.ToInt32(qhs.道具数量) < qhsnum)
            {
                return "必须拥有足够的强化石才能强化哦!";
            }

            //Money
            UserInfo user = new DataProcess().ReadUserInfo();
            if (Convert.ToInt64(user.金币) < NumEncrypt.二十万() * (lv + 1))
            {
                return "你这点钱我很难办事啊!";
            }

            //Consumption
            user.金币 = (Convert.ToInt64(user.金币) - NumEncrypt.二十万() * (lv + 1)).ToString();
            new DataProcess().ReviseOrDeletePP(qhs, qhsnum);
            装备1.强化 = (Convert.ToInt16(装备1.强化) + 1).ToString();
            DataProcess.GameForm.发送红色公告("恭喜您的" + 装备1.Name + "强化到了" + 装备1.强化 + "级,属性再上一个台阶!");
            new DataProcess().SaveUserDataFile(user);
            new DataProcess().ChangeAppointedEquipment(装备1);
            _lastTime = new Tools.GetTime().GetSystemTS();
            PetCalc.RenewBuffs();
            return "强化成功!";
            /*Random 高度随机数 = new Random(数据处理.获取随机种子());

            int Lucky_number = 高度随机数.Next(0, 100);

            if (Lucky_number >= 0 && Lucky_number < Int32.Parse(成功率[Convert.ToInt16(装备1.强化)]) + 成功率加成)
            {
                
            }
            else
            {   
                if (Convert.ToInt16(装备1.强化) >= 8 && !掉级保护)
                {
                    装备1.强化 = (Convert.ToInt16(装备1.强化) - 1).ToString();
                }
                数据处理._游戏窗口.发送红色公告("很不幸,您本次强化失败了.请不要灰心,下次幸运女神一定会眷顾您的!");
                new 数据处理().保存用户存档(用户);
                LastTime = new 数据处理().取系统时间();
                return "强化失败!";
            }*/
        }

        internal static void TransformWX(EquipmentInfo equipment) // 目前想的是是 最佳五行 主属性 +3% （如果是百分比） +30%(如果是数值）
        {
            EquipmentType info = new DataProcess().GetAET(equipment.类ID);
            if (info.类型.Equals("灵饰") || info.类型.Equals("卡牌左") || info.类型.Equals("卡牌右") || info.类型.Equals("法宝") || info.类型.Equals("背饰"))
            {
                DataProcess.GameForm.发送游戏公告("该装备不可点化五行!");
                return;
            }

            PropInfo dhs = new DataProcess().GetAP_ID("2017092001");
            if (dhs == null || Convert.ToInt64(dhs.道具数量) < 1)
            {
                DataProcess.GameForm.发送游戏公告("必须拥有足够的五行点化石才能点化哦!");
                return;
            }
            if (string.IsNullOrEmpty(equipment.WX) || equipment.WX.Equals("无"))
            {
                List<string> tmp = WXList;
                tmp.Remove(EquimentAttribute[info.主属性]);
                equipment.WX = tmp[DataProcess.RandomGenerator.Next(0, tmp.Count)];
                new DataProcess().ChangeAppointedEquipment(equipment);
                new DataProcess().ReviseOrDeletePP(dhs, 1);
                PetCalc.RenewBuffs();
                DataProcess.GameForm.发送游戏公告("五行点化成功！");
                return;
            }

            if (equipment.WX.Equals(EquimentAttribute[info.主属性]))
            {
                DataProcess.GameForm.发送游戏公告("您的装备五行已经是最佳的了，无需再点化了！");
                return;
            }

            {
                List<string> t = WXList;
                t.Remove(Restraint[info.主属性]);
                t.Remove(EquimentAttribute[info.主属性]);
                List<string> tmp = t.Concat(t).ToList().Concat(t).ToList();
                for (int i = 0; i <= 3; i++)
                {
                    tmp.Add(Restraint[info.主属性]);
                }

                tmp.Add(EquimentAttribute[info.主属性]);
                List<string> final = SkRandomObject.GetRandomList(tmp);
                equipment.WX = tmp[DataProcess.RandomGenerator.Next(0, final.Count)];
                new DataProcess().ChangeAppointedEquipment(equipment);
                new DataProcess().ReviseOrDeletePP(dhs, 1);
                PetCalc.RenewBuffs();
                DataProcess.GameForm.发送游戏公告("五行点化成功！");
            }
        }

        internal static bool Resolve(EquipmentInfo equipment)
        {
            UserInfo user = new DataProcess().ReadUserInfo();
            if (Convert.ToInt64(user.金币) - 200000 < 0)
            {
                return false;
            }

            EquipmentType info = new DataProcess().GetAET(equipment.类ID);
            bool a = new DataProcess().DeleteEquipment(equipment.ID);
            if (a)
            {
                int[] c = new int[2];
                if (info.suitID == null)
                {
                    c = info.类型.Equals("灵饰") || info.类型.Equals("法宝")
                        ? DataProcess.Gdsjsz(20, 1041, 2)
                        : DataProcess.Gdsjsz(10, 1021, 2);

                }
                else
                {
                    if (FJBS.ContainsKey(info.suitID))
                    {
                        c = DataProcess.Gdsjsz(10, 10 * FJBS[info.suitID] + 1, 2);
                    }
                    else
                    {
                        c = DataProcess.Gdsjsz(10, 10 * 1020 + 1, 2); ;
                    }
                    //c = SkRandomObject.Gdsjsz(10, 10 * FJBS[info.suitID] + 1, 2);

                }

                PropInfo 信息 = new PropInfo
                {
                    道具类型ID = 2016092501.ToString(),
                    道具位置 = PropLoaction.背包.ToString(),
                    道具数量 = c[0].ToString()
                };
                new DataProcess().AddPlayerProp(信息);
                PropInfo 信息1 = new PropInfo
                {
                    道具类型ID = 2016092502.ToString(),
                    道具位置 = PropLoaction.背包.ToString(),
                    道具数量 = c[1].ToString()
                };
                new DataProcess().AddPlayerProp(信息1);
                user.金币 = (Convert.ToInt64(user.金币) - 200000).ToString();
                new DataProcess().SaveUserDataFile(user);
                DataProcess.GameForm.updateIndex_Page1(0, "");
                DataProcess.GameForm.发送游戏公告("分解成功，您获得了" + c[0] + "个五行之力和" + c[1] + "个补天石！");
                return true;
            }

            return false;
        }

        //这个功能还没做
        /*internal static void Inlay(装备信息 Equipment)
        {

        }*/
    }
}
