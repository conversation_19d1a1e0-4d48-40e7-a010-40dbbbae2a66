﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Web.WebPages.Deployment</name>
  </assembly>
  <members>
    <member name="T:System.Web.WebPages.Deployment.PreApplicationStartCode">
      <summary>Provides a registration point for pre-application start code for Web Pages deployment.</summary>
    </member>
    <member name="M:System.Web.WebPages.Deployment.PreApplicationStartCode.Start">
      <summary>Registers pre-application start code for Web Pages deployment.</summary>
    </member>
    <member name="T:System.Web.WebPages.Deployment.WebPagesDeployment">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.Provides methods that are used to get deployment information about the Web application.</summary>
    </member>
    <member name="M:System.Web.WebPages.Deployment.WebPagesDeployment.GetAssemblyPath(System.Version)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.Gets the assembly path for the Web Pages deployment.</summary>
      <returns>The assembly path for the Web Pages deployment.</returns>
      <param name="version">The Web Pages version.</param>
    </member>
    <member name="M:System.Web.WebPages.Deployment.WebPagesDeployment.GetExplicitWebPagesVersion(System.String)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.Gets the Web Pages version from the given binary path.</summary>
      <returns>The Web Pages version.</returns>
      <param name="path">The binary path for the Web Pages.</param>
    </member>
    <member name="M:System.Web.WebPages.Deployment.WebPagesDeployment.GetIncompatibleDependencies(System.String)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.Gets the assembly references from the given path regardless of the Web Pages version.</summary>
      <returns>The dictionary containing the assembly references of the Web Pages and its version.</returns>
      <param name="appPath">The path to the Web Pages application.</param>
    </member>
    <member name="M:System.Web.WebPages.Deployment.WebPagesDeployment.GetMaxVersion">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.Gets the maximum version of the Web Pages loaded assemblies.</summary>
      <returns>The maximum version of the Web Pages loaded assemblies.</returns>
    </member>
    <member name="M:System.Web.WebPages.Deployment.WebPagesDeployment.GetVersion(System.String)">
      <summary>Gets the Web Pages version from the given path.</summary>
      <returns>The Web Pages version.</returns>
      <param name="path">The path of the root directory for the application.</param>
    </member>
    <member name="M:System.Web.WebPages.Deployment.WebPagesDeployment.GetVersionWithoutEnabledCheck(System.String)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.Gets the Web Pages version using the configuration settings with the specified path.</summary>
      <returns>The Web Pages version.</returns>
      <param name="path">The path to the application settings.</param>
    </member>
    <member name="M:System.Web.WebPages.Deployment.WebPagesDeployment.GetWebPagesAssemblies">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.Returns the assemblies for this Web Pages deployment.</summary>
      <returns>A list containing the assemblies for this Web Pages deployment.</returns>
    </member>
    <member name="M:System.Web.WebPages.Deployment.WebPagesDeployment.IsEnabled(System.String)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.Indicates whether the Web Pages deployment is enabled.</summary>
      <returns>true if the Web Pages deployment is enabled; otherwise, false.</returns>
      <param name="path">The path to the Web Pages deployment.</param>
    </member>
    <member name="M:System.Web.WebPages.Deployment.WebPagesDeployment.IsExplicitlyDisabled(System.String)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.Indicates whether the Web Pages deployment is explicitly disabled.</summary>
      <returns>true if the Web Pages deployment is explicitly disabled; otherwise, false.</returns>
      <param name="path">The path to the Web Pages deployment.</param>
    </member>
  </members>
</doc>