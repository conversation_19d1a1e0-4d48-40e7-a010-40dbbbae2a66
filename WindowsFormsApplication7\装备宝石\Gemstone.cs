﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Shikong.Pokemon2.PCG.装备宝石
{
    public class Gemstone
    {
        public string typeName { get; set; }
        public string upType { get; set; }
        public double upNum { get; set; }
        public int LV { get; set; }
        public string color { get; set; }
        public string prop { get; set; }
        public string typeClass { get; set; }
        public string[] eType { get; set; }

        public int orderNum { get; set; }
        public int[] wzs { get; set; }
        public const string path = "PageMain/pet_g.dat";
        public static Gemstone getGemstone(String Tname)
        {
            var Glist = getGemstoneList();
            foreach (var g in Glist)
            {
                if (g.typeName.Equals(Tname))
                {
                    return g;
                }
            }
            return null;
        }

        public static List<Gemstone> getGemstoneList()
        {
            string cfg = new DataProcess().ReadFile(path);

            cfg = SkRC4.DES.DecryptRC4(cfg, new DataProcess().GetKey(1));
            return JsonConvert.DeserializeObject<List<Gemstone>>(cfg);
        }
        //宝石-新增孔数在这里加
        private static String[] 二孔宝石 = { "混元", "赤炎", "烛龙" };
        private static String[] 三孔宝石 = { "极阳", "圣光", "虚空" };

        public static String setGemstone(String equipmentID, String propID)
        {
            var prop = new DataProcess().GetAP_XH(propID);
            if (prop == null) return "道具不存在，无法继续。";
            var propScript = new DataProcess().ReadPropScript(prop.道具类型ID).道具脚本;
            var equipment = new DataProcess().GetAppointedEquipment(equipmentID);
            var zbInfo = new DataProcess().GetAET(equipment.类ID);
            if (zbInfo.五行限制 == "巫" && !propScript.Contains("返回消耗|")) return "该类型装备不能镶嵌宝石。";

            if (propScript.Contains("清空宝石|"))
            {
                if (equipment.宝石列表 == null || equipment.宝石列表.Count == 0)
                {
                    return "本装备没有宝石属性，无法清空宝石！";
                }
                foreach (var bs in equipment.宝石列表) {
                    var b = getGemstone(bs);

                    new DataProcess().AddPlayerProp(new PropInfo()
                    {
                        道具位置 = "1",
                        道具类型ID = b.prop,
                        道具数量 = "1"
                    });
                  
                }
                equipment.宝石列表 = null;
                new DataProcess().ReviseOrDeletePP(prop, 1);
                new DataProcess().ChangeAppointedEquipment(equipment);
                return "宝石清空成功！对应的宝石已经拆卸到你的背包。";
            }
            if (propScript.Contains("宝石开孔|"))
            {
                int pNum = 10;
                if (equipment.扩展槽位 >= 1) pNum = 20;
                if (Convert.ToInt32(prop.道具数量) < pNum) {
                    return "需要开孔道具：" + pNum + "个，您的数量不足，无法开孔！";
                }
                if (equipment.扩展槽位 >= 2) {
                    return "镶嵌宝石失败，装备只能开三个槽位！";
                }

                equipment.扩展槽位 += 1;
                new DataProcess().ReviseOrDeletePP(prop, pNum);
                new DataProcess().ChangeAppointedEquipment(equipment);
                return "开槽成功！";
            }
            if (propScript.Contains("返回消耗|"))
            {
                //判断装备等级
                if (equipment.强化 == "0" || equipment.强化 == null) return "该装备未强化过!";
                //判断倍数 - 这里倍数不走变量防止被修改
                if (Regex.IsMatch(propScript.Split('|')[1], @"((([^0][0-9]+|0)\.([0-9]{1,2}))$)|^(([1-9]+)\.([0-9]{1,2})$)"))
                {
                    //这里对倍数判断
                    if(Convert.ToDouble(propScript.Split('|')[1])>1.0 && !new DataProcess().getPower()) return "时间回溯失败!请检查时间之石!";
                    if (Convert.ToDouble(propScript.Split('|')[1]) <= 0.0) return "时间回溯失败!请检查时间之石是否损坏!";
                    string npc = "";
                    //这里定义NPC信息
                    if (Convert.ToDouble(propScript.Split('|')[1]) > 0.0) npc = "实习铁匠崽·黑马";
                    if (Convert.ToDouble(propScript.Split('|')[1]) >= 0.5) npc = "锻造宗师·残酷";
                    if (Convert.ToDouble(propScript.Split('|')[1]) >= 0.7) npc = "传说中的特级锻造师·青·超会打铁·衫";
                    //对装备判断
                    short lv = Convert.ToInt16(equipment.强化);
                    int qhsnum = 0;

                    //判断金币
                    UserInfo user_ = new DataProcess().ReadUserInfo();
                    if (Convert.ToInt64(user_.金币) < NumEncrypt.四十万() * (lv + 1))
                    {
                        return "你这点钱,我很难办事啊!";
                    }
                    if (lv >= 0 && lv <= 4)
                    {
                        for(int i =0;i < Convert.ToInt16(lv); i++)
                        {
                            //Console.WriteLine("第{0}级:{1}", i + 1, Convert.ToInt32(Math.Pow(i + 1, 2) * 150));
                            qhsnum += Convert.ToInt32(Math.Pow(i + 1, 2) * 150)*(zbInfo.五行限制 == "巫" ? 2 :1);
                        }
                        
                    }
                    else if (lv > 4 && lv <= 20)
                    {
                        for (int i = 0; i <= 4; i++)
                        {
                            //Console.WriteLine("第{0}级:{1}", i + 1, Convert.ToInt32(Math.Pow(i + 1, 2) * 150));
                            qhsnum += Convert.ToInt32(Math.Pow(i + 1, 2) * 150) * (zbInfo.五行限制 == "巫" ? 2 : 1);
                        }
                        for (int i = 5; i < Convert.ToInt16(lv); i++)
                        {
                            Console.WriteLine("第{0}级:{1}", i + 1, Convert.ToInt32(Math.Pow(i + 1, 2) * 1050));
                            qhsnum += Convert.ToInt32(Math.Pow(i + 1, 2) * 1050) * (zbInfo.五行限制 == "巫" ? 2 : 1);
                        }
                    }
                    //这里需要扣除道具，把装备强化等级归一，然后添加强化石，保存存档
                    user_.金币 = (Convert.ToInt64(user_.金币) - NumEncrypt.四十万() * (lv + 1)).ToString();
                    new DataProcess().SaveUserDataFile(user_);
                    new DataProcess().ReviseOrDeletePP(prop, 1);
                    PropInfo 信息 = new PropInfo()
                    {
                        道具类型ID = "2017060302",
                        道具数量 = (qhsnum* Convert.ToDouble(propScript.Split('|')[1])).ToString()
                    };
                    
                    if (zbInfo.五行限制 == "巫")
                    {
                        信息.道具数量 = (Convert.ToInt64(信息.道具数量) * 2).ToString();
                    }
                    new DataProcess().AddPlayerProp(信息);
                    equipment.强化 = "0";
                    new DataProcess().ChangeAppointedEquipment(equipment);
                    DataProcess.GameForm.发送红色公告($"恭喜你,在 {npc} 的帮助下,使用时间之石获得强化装备{Convert.ToDouble(propScript.Split('|')[1]) * 100}%的强化石!<br>本次返还强化石:{(qhsnum * Convert.ToDouble(propScript.Split('|')[1]))}");
                    return $"恭喜你,在 {npc} 的帮助下,使用时间之石获得强化装备{Convert.ToDouble(propScript.Split('|')[1]) * 100}%的强化石!<br>本次返还强化石:{(qhsnum * Convert.ToDouble(propScript.Split('|')[1]))}";
                }
                else
                {
                    return "时间回溯失败!";
                }
            }
            var ginfo = getGemstone(propScript.Split('|')[1]);
            //目的是为了检验是否在配置中对该宝石做了额外的定义，如若有则无视部位的强制限制
            bool notEType = (ginfo.eType == null || !ginfo.eType.Contains(equipment.类型));
            //目前所有装备可镶嵌
            if ((equipment.类型.Contains("卡牌") || equipment.类型.Equals("灵饰") || equipment.类型.Equals("背饰")
                || equipment.类型.Equals("法宝")) && notEType)
            {
                return "卡牌、法宝、灵饰、背饰无法镶嵌该宝石！";
            }
            if (ginfo.eType != null && !ginfo.eType.Contains(equipment.类型)) {
                return $"该宝石只能镶嵌在部位：{string.Join("、", ginfo.eType)}的装备上面！";
            }
            if (equipment.宝石列表 != null && equipment.宝石列表.Count >= 1)
            {
                //var EGinfo = getGemstone(equipment.宝石列表[0]);
                //if (EGinfo.typeClass != ginfo.typeClass)
                //{
                //    return "请镶嵌同类型的宝石！如果你想镶嵌其他类型的宝石，请在神秘商店中购买空宝石。";
                //}
                //else if (EGinfo.LV + 1 != ginfo.LV)
                //{
                //    return "当前只能镶嵌" + (EGinfo.LV + 1) + "级" + EGinfo.typeClass + "，不允许跳级或者降级镶嵌，如果要更换宝石类型请使用道具擦除之前的宝石。";
                //}
            }
            //if (equipment.宝石列表 == null)
            //{
            //    if (ginfo.LV > 1)
            //    {
            //        return "只能从1级宝石开始镶嵌！";
            //    }
            //}

            if (equipment.宝石列表 == null) {
                equipment.宝石列表 = new List<String>();
            }
            //这里应该可以同类高级替换低级，但是得判断孔位，默认覆盖等级最低的同类型宝石 - 如果没镶嵌满不覆盖低等级宝石
            if (equipment.扩展槽位 + 1 <= equipment.宝石列表.Count)
            {
                string msg = "";
                bool rs =ReplaceGemstone(equipment,ginfo,prop,out msg);
                return msg;
                // 是否有同类型但等级比 ginfo 低的宝石
                //bool canEmbed = false;
                //int replaceIndex = -1;
                //int minReplaceableLevel = -1;
                //for (int i = 0; i < equipment.宝石列表.Count; i++)
                //{
                //    var existingGem = getGemstone(equipment.宝石列表[i]);

                //    if (!existingGem.typeClass.Equals(ginfo.typeClass))
                //        continue;

                //    // 找到同类型但等级低于 ginfo 的宝石
                //    if (existingGem.LV < ginfo.LV)
                //    {
                //        canEmbed = true;

                //        // 记录当前最适合替换的宝石（等级最低但高于之前的最低）
                //        if(minReplaceableLevel == -1 && existingGem.LV > minReplaceableLevel)
                //        {
                //            minReplaceableLevel = existingGem.LV;
                //            replaceIndex = i;
                //        }
                //        else if (existingGem.LV < minReplaceableLevel)
                //        {
                //            minReplaceableLevel = existingGem.LV;
                //            replaceIndex = i;
                //        }
                //    }
                //}
                //if (canEmbed)
                //{
                //    var user1 = new DataProcess().ReadUserInfo();
                //    int money1 = 1000000000;
                //    if (Convert.ToInt64(user1.金币) < money1)
                //    {
                //        return "金币不足" + money1 + "，无法覆盖同类型宝石。";
                //    }
                //    //替换宝石
                //    equipment.宝石列表[replaceIndex]=ginfo.typeName;
                //    new DataProcess().ReviseOrDeletePP(prop, 1);
                //    user1.金币 = (Convert.ToInt64(user1.金币) - money1).ToString();
                //    new DataProcess().SaveUserDataFile(user1);
                //    new DataProcess().ChangeAppointedEquipment(equipment);
                //    return $"替换{ginfo.typeClass}成功！";
                //}
                //else
                //{
                //    return "槽位不足或当前镶嵌宝石等级低于所有已镶嵌同类型宝石，无法继续镶嵌";
                //}
                    
                
            }

            var 当前槽位 = equipment.宝石列表.Count;
            if (ginfo.wzs == null || ginfo.wzs.Length == 0)
            {
                if (当前槽位 == 0 &&
                        (二孔宝石.FirstOrDefault(C => prop.道具名字.Contains(C)) != null
                        ||
                        三孔宝石.FirstOrDefault(C => prop.道具名字.Contains(C)) != null)
                   )
                {
                    string msg = "";
                    bool rs = ReplaceGemstone(equipment, ginfo, prop, out msg);
                    if (rs) {
                        return msg;
                    }
                    return "该槽位不能打二三孔专门打的宝石，只能镶嵌一孔宝石！";
                }
                if (当前槽位 == 1 && 二孔宝石.FirstOrDefault(C => prop.道具名字.Contains(C)) == null)
                {
                    string msg = "";
                    bool rs = ReplaceGemstone(equipment, ginfo, prop, out msg);
                    if (rs)
                    {
                        return msg;
                    }
                    return "该槽位只能打二孔宝石！三孔宝石和一孔宝石无法进行镶嵌！";
                }
                if (当前槽位 == 2 && 三孔宝石.FirstOrDefault(C => prop.道具名字.Contains(C)) == null)
                {
                    string msg = "";
                    bool rs = ReplaceGemstone(equipment, ginfo, prop, out msg);
                    if (rs)
                    {
                        return msg;
                    }
                    return "该槽位只能打三孔宝石！二孔宝石和一孔宝石无法进行镶嵌！";
                }
            }
            else {
                if (!ginfo.wzs.Contains(当前槽位 + 1)) {
                    string msg = "";
                    bool rs = ReplaceGemstone(equipment, ginfo, prop, out msg);
                    if (rs)
                    {
                        return msg;
                    }
                    return $"该槽位只能打在{string.Join("、", ginfo.wzs)}孔！";
                }
            
            }

            equipment.宝石列表.Add(ginfo.typeName);
          
            var user = new DataProcess().ReadUserInfo();
            int money = 1000000000;//文档一次就是10亿
            if (Convert.ToInt64(user.金币) < money)
            {
                return "金币不足" + money + "，无法镶嵌。";
            }

            new DataProcess().ReviseOrDeletePP(prop, 1);
            user.金币 = (Convert.ToInt64(user.金币) - money).ToString();
            new DataProcess().SaveUserDataFile(user);
            new DataProcess().ChangeAppointedEquipment(equipment);
            return "镶嵌成功！";

        }

        public static bool ReplaceGemstone(EquipmentInfo equipment, Gemstone ginfo, PropInfo prop, out string msg)
        {
            msg = "槽位不足或当前镶嵌宝石等级低于所有已镶嵌同类型宝石，无法继续镶嵌。";
            // 是否有同类型但等级比 ginfo 低的宝石
            bool canEmbed = false;
            int replaceIndex = -1;
            //int minReplaceableLevel = -1;
            int minReplaceableLevel = int.MaxValue;
            for (int i = 0; i < equipment.宝石列表.Count; i++)
            {
                var existingGem = getGemstone(equipment.宝石列表[i]);

                if (!existingGem.typeClass.Equals(ginfo.typeClass))
                    continue;

                // 找到同类型但等级低于 ginfo 的宝石
                if (existingGem.LV < ginfo.LV)
                {
                    canEmbed = true;
                    
                    // 记录当前最适合替换的宝石（等级最低但高于之前的最低）
                    //if (minReplaceableLevel == -1 && existingGem.LV > minReplaceableLevel)
                    //{
                    //    minReplaceableLevel = existingGem.LV;
                    //    replaceIndex = i;
                    //}
                    //else if (existingGem.LV < minReplaceableLevel)
                    //{
                    //    minReplaceableLevel = existingGem.LV;
                    //    replaceIndex = i;
                    //}
                    if (existingGem.LV < minReplaceableLevel)
                    {
                        msg = $"已替换 {existingGem.typeName} => {ginfo.typeName}。";
                        minReplaceableLevel = existingGem.LV;
                        replaceIndex = i;
                    }
                }
            }
            if (canEmbed)
            {
                var user1 = new DataProcess().ReadUserInfo();
                int money1 = 1000000000;
                if (Convert.ToInt64(user1.金币) < money1)
                {
                    msg = "金币不足" + money1 + "，无法覆盖同类型宝石。";
                    return false;
                }
                //替换宝石
                equipment.宝石列表[replaceIndex] = ginfo.typeName;
                new DataProcess().ReviseOrDeletePP(prop, 1);
                user1.金币 = (Convert.ToInt64(user1.金币) - money1).ToString();
                new DataProcess().SaveUserDataFile(user1);
                new DataProcess().ChangeAppointedEquipment(equipment);
                //msg = $"替换{ginfo.typeClass}成功！";
                

                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 获取玩家背包里所有宝石道具
        /// </summary>
        /// <returns></returns>
        public static List<PropInfo> getUserGemstoneList()
        {
            new DataProcess().GetPAP();
            return DataProcess.PP_List.Where(t => t.道具名字.IndexOf("[宝石]", StringComparison.Ordinal) != -1 && t.道具图标 != "12").ToList();

        }
        /// <summary>
        /// 一件拆卸宝石
        /// </summary>
        /// <returns></returns>
        public static String yjcx()
        {
            List<EquipmentInfo> elist = new DataProcess().GetPAE();
            int i = 0;
            foreach (var equipment in elist)
            {
                var zbInfo = new DataProcess().GetAET(equipment.类ID);
                if (zbInfo.五行限制 == "巫" && equipment.宝石列表 != null && equipment.宝石列表.Count > 0)
                {
                    foreach (var bs in equipment.宝石列表)
                    {
                        var b = getGemstone(bs);

                        new DataProcess().AddPlayerProp(new PropInfo()
                        {
                            道具位置 = "1",
                            道具类型ID = b.prop,
                            道具数量 = "1"
                        });
                        i++;
                    }
                    equipment.宝石列表 = null;
                    new DataProcess().ChangeAppointedEquipment(equipment);

                }
            }
            return "一键无代价拆卸成功！成功拆卸数量：" + i;
        }

    }
}
