﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using 怪物信息 = Shikong.Pokemon2.PCG.MonsterInfo;
using 用户信息 = Shikong.Pokemon2.PCG.UserInfo;
using 宠物信息 = Shikong.Pokemon2.PCG.PetInfo;
using 宠物类型 = Shikong.Pokemon2.PCG.PetConfig;
using 道具信息 = Shikong.Pokemon2.PCG.PropInfo;
using 地图信息 = Shikong.Pokemon2.PCG.MapInfo;
using 装备类型 = Shikong.Pokemon2.PCG.EquipmentType;
using 装备信息 = Shikong.Pokemon2.PCG.EquipmentInfo;
using 道具类型 = Shikong.Pokemon2.PCG.PropType;
using 道具具体信息 = Shikong.Pokemon2.PCG.PropConfig;
using 数据处理 = Shikong.Pokemon2.PCG.DataProcess;
using 商店道具信息 = Shikong.Pokemon2.PCG.GoodsInfo;
using Shikong.Pokemon2.PCG;
using PetShikongTools;

namespace Admin
{
    public partial class getPROPJSON : Form
    {
        public getPROPJSON()
        {
            InitializeComponent();
        }

        private void button2_Click(object sender, EventArgs e)
        {
           
            List<商店道具信息> 道具列表 = new List<商店道具信息>();
            if (textBox4.Text != "") {
                道具列表 = JsonConvert.DeserializeObject<List<商店道具信息>>(textBox4.Text);
               
            }
            商店道具信息 道具 = new 商店道具信息();
          
            
            
            道具类型 类型 = T.GetAPType(dataGridView1.Rows[dataGridView1.CurrentRow.Index].Cells["num"].Value.ToString());
            道具.商品序号 = 类型.道具序号;
            道具.道具名字 = 类型.道具名字;
            道具.道具图标 = 类型.道具图标;
            道具.货币类型 = checkBox1.Checked ? "金币" : "元宝";
            道具.商品价格 = textBox1.Text;
        
            道具列表.Add(道具);
            textBox4.Text = JsonConvert.SerializeObject(道具列表, Formatting.Indented);

        }
        List<道具类型> 所有道具 = new List<道具类型>();
        string 道具使用类型;
        道具类型 现编辑道具 = new 道具类型();
        道具具体信息 现编辑道具具体信息 = new 道具具体信息();
        private void button1_Click(object sender, EventArgs e)
        {
            dataGridView1.Rows.Clear();
            所有道具 = new 数据处理().ReadAllPropTypes();
            foreach (道具类型 道具 in 所有道具)
            {
                string[] str = { 道具.道具序号, 道具.道具名字 };
                dataGridView1.Rows.Add(str);
            }
            //label4.Text = "道具数：" + dataGridView1.Rows.Count;
        }

        private void button8_Click(object sender, EventArgs e)
        {
            dataGridView1.Rows.Clear();
            foreach (道具类型 道具 in 所有道具)
            {
                if (道具.道具名字.IndexOf(textBox2.Text) > -1 || 道具.道具序号.IndexOf(textBox2.Text) > -1)
                {
                    string[] str = { 道具.道具序号, 道具.道具名字 };
                    dataGridView1.Rows.Add(str);
                }

            }
        }

        private void dataGridView1_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {
            现编辑道具.道具名字 = dataGridView1.Rows[dataGridView1.CurrentRow.Index].Cells["name"].Value.ToString();
            现编辑道具.道具序号 = dataGridView1.Rows[dataGridView1.CurrentRow.Index].Cells["num"].Value.ToString();
            现编辑道具具体信息 = ReadPropScript(现编辑道具.道具序号);
            textBox6.Text = 现编辑道具具体信息.道具脚本;
            textBox5.Text = 现编辑道具具体信息.道具说明;
        }

        private void button3_Click(object sender, EventArgs e)
        {
            var l = JsonConvert.DeserializeObject<List<商店道具信息>>(textBox4.Text);
            
            l = l.Select(a => new { a, newID = Guid.NewGuid() }).OrderBy(b => b.newID).Select(c => c.a).ToList();
            textBox4.Text = JsonConvert.SerializeObject(l, Formatting.Indented);
        }

        private void getPROPJSON_Load(object sender, EventArgs e)
        {

        }
        private PropConfig ReadPropScript(string propId)
        {
            String path
                = 数据处理.PSC_Path + SkCryptography.GetHash.GetStringHash(SkRC4.DES.EncryptRC4((Convert.ToInt32(propId) + 14).ToString(),
                                     T.GetKey(2))) + ".data";
            string 存档 = new 数据处理().ReadFile(path);
            if (string.IsNullOrEmpty(存档)) return null;
            存档 = SkRC4.DES.DecryptRC4(存档, T.GetKey(1)); //这里调用了RC4解密,存档我们是用的RC4进行加密的
            var 信息 = JsonConvert.DeserializeObject<PropConfig>(存档);
            return 信息;
        }
    }
}
