﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{179D3D5D-D3FD-4D07-84CF-CB17BB26A5FF}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>ShikongPlus.Pokemon2.PCG</RootNamespace>
    <AssemblyName>PetShikongPlus</AssemblyName>
    <TargetFrameworkVersion>v4.6.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <PublishUrl>发布\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>x86</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>TRACE;DEBUG</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>1</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <CodeAnalysisRuleSet>gz.ruleset</CodeAnalysisRuleSet>
    <RunCodeAnalysis>false</RunCodeAnalysis>
    <AllowUnsafeBlocks>false</AllowUnsafeBlocks>
    <CodeAnalysisIgnoreGeneratedCode>true</CodeAnalysisIgnoreGeneratedCode>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>x86</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>
    </DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>1</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup>
    <TargetZone>LocalIntranet</TargetZone>
  </PropertyGroup>
  <PropertyGroup>
    <GenerateManifests>false</GenerateManifests>
  </PropertyGroup>
  <PropertyGroup />
  <PropertyGroup />
  <PropertyGroup>
    <ApplicationIcon>favicon.ico</ApplicationIcon>
  </PropertyGroup>
  <PropertyGroup>
    <SignAssembly>false</SignAssembly>
  </PropertyGroup>
  <PropertyGroup>
    <AssemblyOriginatorKeyFile>PetShikong.pfx</AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>TRACE;DEBUG</DefineConstants>
    <WarningLevel>1</WarningLevel>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>gz.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
    <Optimize>false</Optimize>
    <AllowUnsafeBlocks>false</AllowUnsafeBlocks>
    <CheckForOverflowUnderflow>true</CheckForOverflowUnderflow>
    <RunCodeAnalysis>false</RunCodeAnalysis>
    <LangVersion>7</LangVersion>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <OutputPath>bin\Debug\</OutputPath>
    <Optimize>true</Optimize>
    <WarningLevel>1</WarningLevel>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
    <AllowUnsafeBlocks>false</AllowUnsafeBlocks>
    <CheckForOverflowUnderflow>true</CheckForOverflowUnderflow>
    <LangVersion>7.2</LangVersion>
  </PropertyGroup>
  <PropertyGroup>
    <StartupObject />
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Management" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Runtime.Remoting" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AntiCheat.cs" />
    <Compile Include="AutoMapInfo.cs" />
    <Compile Include="Client.cs" />
    <Compile Include="Email.cs" />
    <Compile Include="emailType.cs" />
    <Compile Include="Formula.cs" />
    <Compile Include="HCPet.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="HCPet.Designer.cs">
      <DependentUpon>HCPet.cs</DependentUpon>
    </Compile>
    <Compile Include="LimitPropsContractResolver.cs" />
    <Compile Include="LogSystem.cs" />
    <Compile Include="MallsRestrict.cs" />
    <Compile Include="mapNetInfo.cs" />
    <Compile Include="NativeMethods.cs" />
    <Compile Include="CompressJson.cs" />
    <Compile Include="DZ.cs" />
    <Compile Include="PetPhoto.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PetPhoto.Designer.cs">
      <DependentUpon>PetPhoto.cs</DependentUpon>
    </Compile>
    <Compile Include="PetRename.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PetRename.Designer.cs">
      <DependentUpon>PetRename.cs</DependentUpon>
    </Compile>
    <Compile Include="PlayerHelper.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PlayerHelper.Designer.cs">
      <DependentUpon>PlayerHelper.cs</DependentUpon>
    </Compile>
    <Compile Include="Res.cs" />
    <Compile Include="Ruler.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Ruler.Designer.cs">
      <DependentUpon>Ruler.cs</DependentUpon>
    </Compile>
    <Compile Include="LoginSK.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LoginSK.Designer.cs">
      <DependentUpon>LoginSK.cs</DependentUpon>
    </Compile>
    <Compile Include="setMonsterJson.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="setMonsterJson.Designer.cs">
      <DependentUpon>setMonsterJson.cs</DependentUpon>
    </Compile>
    <Compile Include="SkRC4.cs" />
    <Compile Include="TalismanConfig.cs" />
    <Compile Include="TalismanInfo.cs" />
    <Compile Include="TalismanProcess.cs" />
    <Compile Include="TestResultFrom.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="TestResultFrom.Designer.cs">
      <DependentUpon>TestResultFrom.cs</DependentUpon>
    </Compile>
    <Compile Include="TJ.cs" />
    <Compile Include="Tools.cs" />
    <Compile Include="Tools\ShikongTools.cs" />
    <Compile Include="Tools\SkCryptography.cs" />
    <Compile Include="Tools\SkFileCheck.cs" />
    <Compile Include="Tools\SkRandomObject.cs" />
    <Compile Include="Tools\SkWeb.cs" />
    <Compile Include="Upgrade.cs" />
    <Compile Include="Fight.cs" />
    <Compile Include="EquipmentProcess.cs" />
    <Compile Include="ConvertJson.cs" />
    <Compile Include="Form1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form1.Designer.cs">
      <DependentUpon>Form1.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="suit.cs" />
    <Compile Include="suits.cs" />
    <Compile Include="task.cs" />
    <Compile Include="TaskInfo.cs" />
    <Compile Include="TaskPanel.cs" />
    <Compile Include="FBROP.cs" />
    <Compile Include="GoodsInfo.cs" />
    <Compile Include="MapInfo.cs" />
    <Compile Include="PetInfo.cs" />
    <Compile Include="PetProcess.cs" />
    <Compile Include="PetCalc.cs" />
    <Compile Include="PetConfig.cs" />
    <Compile Include="MonsterInfo.cs" />
    <Compile Include="MonsterType.cs" />
    <Compile Include="FightResult.cs" />
    <Compile Include="SkillInfo.cs" />
    <Compile Include="SkillConfig.cs" />
    <Compile Include="NumEncrypt.cs" />
    <Compile Include="DataProcess.cs" />
    <Compile Include="Reg.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reg.Designer.cs">
      <DependentUpon>Reg.cs</DependentUpon>
    </Compile>
    <Compile Include="userCardBuff.cs" />
    <Compile Include="UserInfo.cs" />
    <Compile Include="EquipmentInfo.cs" />
    <Compile Include="EquipmentType.cs" />
    <Compile Include="EvolutionWay.cs" />
    <Compile Include="PropInfo.cs" />
    <Compile Include="PropLoaction.cs" />
    <Compile Include="PropType.cs" />
    <Compile Include="PropConfig.cs" />
    <Compile Include="userSet.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="userSet.Designer.cs">
      <DependentUpon>userSet.cs</DependentUpon>
    </Compile>
    <Compile Include="占卜屋\CardInfo.cs" />
    <Compile Include="成长突破\BreakthroughConfig.cs" />
    <Compile Include="成长突破\EnergyGather.cs" />
    <Compile Include="成长突破\GrowthBreakthrough.cs" />
    <Compile Include="时空屋\hunqi.cs" />
    <Compile Include="时空屋\pifu.cs" />
    <Compile Include="时空屋\shenbing.cs" />
    <Compile Include="时空屋\sksd_form.cs" />
    <Compile Include="时空屋\shiling.cs" />
    <Compile Include="装备宝石\Gemstone.cs" />
    <Compile Include="魂宠\soulPet.cs" />
    <Compile Include="龙珠\longzhu.cs" />
    <EmbeddedResource Include="Form1.resx">
      <DependentUpon>Form1.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="HCPet.resx">
      <DependentUpon>HCPet.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="LoginSK.byn.resx">
      <DependentUpon>LoginSK.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="LoginSK.dz-BT.resx">
      <DependentUpon>LoginSK.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="LoginSK.zh-CHS.resx">
      <DependentUpon>LoginSK.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="LoginSK.zh.resx">
      <DependentUpon>LoginSK.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PetPhoto.resx">
      <DependentUpon>PetPhoto.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PetRename.resx">
      <DependentUpon>PetRename.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PlayerHelper.resx">
      <DependentUpon>PlayerHelper.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ruler.resx">
      <DependentUpon>Ruler.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="LoginSK.resx">
      <DependentUpon>LoginSK.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <EmbeddedResource Include="Reg.resx">
      <DependentUpon>Reg.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="setMonsterJson.resx">
      <DependentUpon>setMonsterJson.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="TestResultFrom.resx">
      <DependentUpon>TestResultFrom.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="userSet.resx">
      <DependentUpon>userSet.cs</DependentUpon>
    </EmbeddedResource>
    <None Include="bin\Debug\PetShikongPlus.exe.config" />
    <None Include="bin\Release\单机最新宠物技能列表.xlsx" />
    <None Include="bin\Release\时空单机新手教程.docx" />
    <None Include="packages.config" />
    <None Include="PetShikong.pfx" />
    <None Include="Properties\app.manifest">
      <SubType>Designer</SubType>
    </None>
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <None Include="成长突破\EnergyGatherExp.config" />
    <None Include="成长突破\Form1成长突破集成代码.md" />
    <None Include="成长突破\Form1集成代码.md" />
    <None Include="成长突破\成长突破前端集成说明.md" />
    <None Include="成长突破\成长突破功能总结.md" />
    <None Include="成长突破\成长突破集成说明.md" />
    <None Include="成长突破\测试验证.md" />
    <None Include="成长突破\能量汇聚配置说明.md" />
    <None Include="成长突破\能量汇聚集成说明.md" />
    <None Include="成长突破\道具配置示例.md" />
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config">
      <SubType>Designer</SubType>
    </None>
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.5">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4.5 %28x86 和 x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Client.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1 Client Profile</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <Content Include="bin\Chat - 副本\1.gif" />
    <Content Include="bin\Chat - 副本\10.gif" />
    <Content Include="bin\Chat - 副本\11.gif" />
    <Content Include="bin\Chat - 副本\12.gif" />
    <Content Include="bin\Chat - 副本\13.gif" />
    <Content Include="bin\Chat - 副本\14.gif" />
    <Content Include="bin\Chat - 副本\15.gif" />
    <Content Include="bin\Chat - 副本\16.gif" />
    <Content Include="bin\Chat - 副本\17.gif" />
    <Content Include="bin\Chat - 副本\18.gif" />
    <Content Include="bin\Chat - 副本\19.gif" />
    <Content Include="bin\Chat - 副本\2.gif" />
    <Content Include="bin\Chat - 副本\20.gif" />
    <Content Include="bin\Chat - 副本\21.gif" />
    <Content Include="bin\Chat - 副本\22.gif" />
    <Content Include="bin\Chat - 副本\23.gif" />
    <Content Include="bin\Chat - 副本\24.gif" />
    <Content Include="bin\Chat - 副本\25.gif" />
    <Content Include="bin\Chat - 副本\26.gif" />
    <Content Include="bin\Chat - 副本\27.gif" />
    <Content Include="bin\Chat - 副本\28.gif" />
    <Content Include="bin\Chat - 副本\29.gif" />
    <Content Include="bin\Chat - 副本\3.gif" />
    <Content Include="bin\Chat - 副本\30.gif" />
    <Content Include="bin\Chat - 副本\31.gif" />
    <Content Include="bin\Chat - 副本\32.gif" />
    <Content Include="bin\Chat - 副本\33.gif" />
    <Content Include="bin\Chat - 副本\34.gif" />
    <Content Include="bin\Chat - 副本\35.gif" />
    <Content Include="bin\Chat - 副本\36.gif" />
    <Content Include="bin\Chat - 副本\4.gif" />
    <Content Include="bin\Chat - 副本\5.gif" />
    <Content Include="bin\Chat - 副本\6.gif" />
    <Content Include="bin\Chat - 副本\7.gif" />
    <Content Include="bin\Chat - 副本\8.gif" />
    <Content Include="bin\Chat - 副本\9.gif" />
    <Content Include="bin\Debug\byn\PetShikongPlus.resources.dll" />
    <Content Include="bin\Debug\dz-BT\PetShikongPlus.resources.dll" />
    <Content Include="bin\Debug\PetShikongPlus.exe" />
    <Content Include="bin\Debug\PetShikongPlus.pdb" />
    <Content Include="bin\Debug\zh-CHS\PetShikongPlus.resources.dll" />
    <Content Include="bin\Debug\zh\PetShikongPlus.resources.dll" />
    <Content Include="bin\Release\AxInterop.ShockwaveFlashObjects.dll" />
    <Content Include="bin\Release\Interop.IWshRuntimeLibrary.dll" />
    <Content Include="bin\Release\Interop.ShockwaveFlashObjects.dll" />
    <Content Include="bin\Release\Newtonsoft.Json.dll" />
    <Content Include="bin\Release\PetShikongTools.dll" />
    <Content Include="bin\Release\tools\信息收集工具.exe" />
    <Content Include="bin\Release\关注下我的公众号，以后不定期发单机福利！.PNG" />
    <Content Include="bin\Release\更新日志.txt" />
    <Content Include="favicon.ico" />
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <PropertyGroup>
    <PreBuildEvent>
    </PreBuildEvent>
  </PropertyGroup>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>