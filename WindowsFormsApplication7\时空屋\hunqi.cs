﻿using Newtonsoft.Json;
using PetShikongTools;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shikong.Pokemon2.PCG.时空屋
{
    public class hunqi
    {
        public string 魂器ID { get; set; }
        public string 图标 { get; set; }
        public hunqi_qianghua 强化设定 { get; set; }
        public hunqi_yuansu 元素设定 { get; set; }
        public hunqi_xilian 洗练设定 { get; set; }
        public hunqi_zhuanling 转灵设定 { get; set; }
        public const string HQ_Path = @"PageMain\equip_2.qingshan"; //魂器配置
        public static string[] 品级 = { "普通", "精良", "优秀", "稀有", "史诗", "传说" };
        /// <summary>
        /// 获取定义列表
        /// </summary>
        /// <param name="getname">是否将所需道具的道具ID转换为道具名，一般拿来展示时使用</param>
        /// <returns></returns>
        public static List<hunqi> GetHunqis(bool getname = false)
        {
            string cfg = new DataProcess().ReadFile(DataProcess.pf + HQ_Path);

            if (cfg == null || cfg.Length <= 0)
            {
                return new List<hunqi>();
            }

            cfg = SkRC4.DES.DecryptRC4(cfg, new DataProcess().GetKey(1));
            var way = JsonConvert.DeserializeObject<List<hunqi>>(cfg);
            if (getname)
            {
                for (int i = 0; i < way.Count; i++)
                {
                    var w = way[i];
                    w.强化设定.所需道具 = new DataProcess().GetAPType(w.强化设定.所需道具).道具名字;
                    w.洗练设定.所需道具 = new DataProcess().GetAPType(w.洗练设定.所需道具).道具名字;
                    w.转灵设定.所需道具 = new DataProcess().GetAPType(w.转灵设定.所需道具).道具名字;
                    foreach (var j in w.元素设定.属性规则)
                    {
                        j.Value.强化设定.道具 = new DataProcess().GetAPType(j.Value.强化设定.道具).道具名字;
                    }
                }
            }
            DataProcess.stringLoadSBJSON = cfg;
            return way;
        }
        /// <summary>
        /// 强化魂器
        /// </summary>
        /// <param name="id">魂器ID</param>
        /// <returns>强化结果，如果包含了成功字样就是成功了，否则失败了</returns>
        public static string up(String id)
        {
            var user = new DataProcess().ReadUserInfo();
            if (user.魂器列表 == null) user.魂器列表 = new Dictionary<string, hqinfo>();
            if (user.魂器列表.ContainsKey(id))
            {
                var hunqis = GetHunqis();
                var s = hunqis.FirstOrDefault(C => C.魂器ID == id);
                if (s == null)
                {
                    return "魂器不在规则中存在，请联系管理员检查！";
                }
                var sheding = s.强化设定;
                var propType = new DataProcess().GetAPType(sheding.所需道具);
                if (propType == null)
                {
                    return "所需道具不存在于当前版本，请联系管理员检查！";
                }
                if (user.魂器列表[id].等级 >= sheding.最高等级)
                {
                    return "当前已经满级，无法强化！！";
                }
                var prop = new DataProcess().GetAP_ID(sheding.所需道具);
                var propNum = sheding.所需数量[Convert.ToInt32(user.魂器列表[id].等级) + 1];
                if (prop == null || Convert.ToInt32(prop.道具数量) < propNum)
                {
                    return "强化所需道具不足，无法强化！需要" + propType.道具名字 + "*" + propNum;
                }
                new DataProcess().ReviseOrDeletePP(prop, propNum);
                user.魂器列表[id].等级++;
                new DataProcess().SaveUserDataFile(user);
                return "成功将【" + id + "】的等级强化到：" + user.魂器列表[id].等级;
            }
            else
            {
                return "魂器不存在，无法强化！";
            }
        }
        /// <summary>
        /// 转灵魂器（也就是随机品级）
        /// 品级决定了能洗出哪些词条
        /// 0 普通
        /// 1 精良
        /// 2 优秀
        /// 3 稀有
        /// 4 史诗
        /// 5 传说
        /// </summary>
        /// <param name="id">魂器ID</param>
        /// <returns>转灵结果，如果包含了成功字样就是成功了，否则失败了</returns>
        public static string zhuanling(String id)
        {
            var user = new DataProcess().ReadUserInfo();
            if (user.魂器列表 == null) user.魂器列表 = new Dictionary<string, hqinfo>();
            if (user.魂器列表.ContainsKey(id))
            {
                var lv = user.魂器列表[id].转灵品级;
                if (lv >= 5)
                {
                    return "魂器品级已经最优！";
                }
                var hunqis = GetHunqis();
                var s = hunqis.FirstOrDefault(C => C.魂器ID == id);
                if (s == null)
                {
                    return "魂器不在规则中存在，请联系管理员检查！";
                }
                var sheding = s.转灵设定;
                var propType = new DataProcess().GetAPType(sheding.所需道具);
                if (propType == null)
                {
                    return "所需道具不存在于当前版本，请联系管理员检查！";
                }

                var prop = new DataProcess().GetAP_ID(sheding.所需道具);
                var propNum = sheding.所需数量[Convert.ToInt32(user.魂器列表[id].转灵品级)];
                if (prop == null || Convert.ToInt32(prop.道具数量) < propNum)
                {
                    return "强化所需道具不足，无法转灵！需要" + propType.道具名字 + "*" + propNum;
                }
                #region 结晶扣除
                string jg = DZ.SubSkjj_getjj(sheding.所需结晶[user.魂器列表[id].转灵品级].ToString(), "转灵魂器" + id + ",当前品级：" + user.魂器列表[id].转灵品级, 1);
                if (string.IsNullOrEmpty(jg))
                {
                    return "连接超时，稍后再次尝试！";
                }
                string salt = DataProcess.GetSalt();
                //new DataProcess().SaveFile(DataProcess.GetStringHash(jg + salt),"a.txt");

                var jjg = jg.Split('|');
                if (jjg.Length >= 2 && SkCryptography.GetHash.GetStringHash(jjg[0] + salt).Equals("947812dc38e7868ede065c4bf0835342"))//VIP特权  获得积分
                {
                    DZ.JJ = Convert.ToInt32(jjg[1]);
                    if (Convert.ToInt16(user.vip) == NumEncrypt.十())
                    {
                        int jf = (int)Math.Floor(sheding.所需结晶[user.魂器列表[id].转灵品级] / 1000.0);
                        user.VIP积分 = (Convert.ToInt32(user.VIP积分) + jf).ToString();
                        if (sheding.所需结晶[user.魂器列表[id].转灵品级] >= 1000)
                        {
                            if (user.星辰VIP)
                            {
                                DataProcess.GameForm.发送游戏公告("尊敬的星辰VIP玩家，您本次购物获得了" + jf + "积分。");
                            }
                            else if (user.至尊VIP)
                            {
                                DataProcess.GameForm.发送游戏公告("尊敬的至尊VIP玩家，您本次购物获得了" + jf + "积分。");
                            }
                            else
                            {
                                DataProcess.GameForm.发送游戏公告("尊敬的VIP10玩家，您本次购物获得了" + jf + "积分。");
                            }
                        }

                    }
                    user.累计消耗结晶 = (Convert.ToInt32(user.累计消耗结晶) + sheding.所需结晶[user.魂器列表[id].转灵品级]).ToString();
                    LogSystem.JoinLog(LogSystem.EventKind.结晶购物,
                        "道具名字：转灵魂器，商品数量：1，购买数量：1，花费结晶：" + sheding.所需结晶[user.魂器列表[id].转灵品级]);

                }
                else
                {
                    return "结晶扣除失败，无法继续！";
                }
                #endregion
                new DataProcess().ReviseOrDeletePP(prop, propNum);
                /// 品级决定了能洗出哪些词条
                /// 0 普通
                /// 1 精良
                /// 2 优秀
                /// 3 稀有
                /// 4 史诗
                /// 5 传说
                ///概率设定如下：
                ///随机数<60则为普通
                ///否则随机数<80则为精良
                ///否则随机数<88则为优秀
                ///否则随机数<94则为稀有
                ///否则随机数<98则为史诗
                ///否则则有概率随机到传说
                ///
                ///当进入到随机传说分支时，随机一个0~5的随机数，
                ///如果为3则超过到传说，否则为史诗
                ///所以预期概率为：
                ///普通60%
                ///精良20%
                ///优秀8%
                ///稀有6%
                ///史诗5.8%
                ///传说0.2%
                ///如果转灵次数大于600次，则必定为传说
                //他取不到100，只能取到99
                int rNum = DataProcess.RandomGenerator.Next(1, 101);
                int pj = 0;
                if (rNum <= 40) pj = 0;//40%
                else if (rNum <= 65) pj = 1;//25%
                else if (rNum <= 85) pj = 2;//20%
                else if (rNum <= 94) pj = 3;//9%
                else if (rNum <= 99) pj = 4;//5%
                else
                {
                    rNum = DataProcess.RandomGenerator.Next(1, 6);
                    //其实就是有百分之二十五的可能成功拿到传说，否则就是史诗级
                    if (rNum == 3) pj = 5;
                    else pj = 4;

                    if (user.魂器列表[id].转灵次数 < 100) pj = 4;
                }
                //不允许超过当前品级的2级
                if (pj > lv + 2) pj = lv + 2;

                user.魂器列表[id].转灵次数++;
                if (user.魂器列表[id].转灵次数 >= 200)
                {
                    pj = 5;
                }

                if (user.魂器列表[id].转灵次数 < 40 && pj >= 5) {
                    pj = 4;//如果低于 40次，就算是传说也不给
                }

                if (user.魂器列表[id].转灵次数 < 20 && pj >= 4)
                {
                    pj = 3;//如果低于20次，就算是史诗也不给
                }
                user.魂器列表[id].转灵品级 = pj;

                DataProcess.GameForm.发送红色公告("成功将【" + id + "】的转灵品级变换到：" + 品级[user.魂器列表[id].转灵品级] + "，当前剩余结晶：" + DZ.JJ + "(以实际为准)，当前总转灵次数：" + user.魂器列表[id].转灵次数);

                new DataProcess().SaveUserDataFile(user);
                return "成功将魂器【" + id + "】的品级转灵至 " + 品级[user.魂器列表[id].转灵品级];
            }
            else
            {
                return "魂器不存在，无法强化！";
            }
        }
        /// <summary>
        /// 是否保留洗练出的结果
        /// </summary>
        /// <param name="id">魂器ID</param>
        /// <param name="x">是否保留</param>
        /// <returns></returns>
        public static string xilian_xuanze(String id, bool x)
        {
            var user = new DataProcess().ReadUserInfo();
            if (user.魂器列表 == null) user.魂器列表 = new Dictionary<string, hqinfo>();
            if (user.魂器列表.ContainsKey(id))
            {

                if (x)
                {
                    user.魂器列表[id].词条 = user.魂器列表[id].待保留词条;

                }
                user.魂器列表[id].待保留词条 = null;
            }
            new DataProcess().SaveUserDataFile(user);

            return x ? "词条保留成功" : "词条放弃成功";
        }
        /// <summary>
        /// 洗练词条
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static string xilian(String id)
        {
            var user = new DataProcess().ReadUserInfo();
            if (user.魂器列表 == null) user.魂器列表 = new Dictionary<string, hqinfo>();
            if (user.魂器列表.ContainsKey(id))
            {

                if (user.魂器列表[id].待保留词条 != null)
                {
                    return "魂器存在待保留词条，无法进行洗练，请重新打开时空神殿进行保留/放弃选择！";
                }
                if (user.魂器列表[id].等级 < 2)
                {
                    return "魂器需要强化等级达到2级，才能够使用洗练功能！";
                }
                var lv = user.魂器列表[id].转灵品级;
                var hunqis = GetHunqis();
                var s = hunqis.FirstOrDefault(C => C.魂器ID == id);

                if (s == null)
                {
                    return "魂器不在规则中存在，请联系管理员检查！";
                }
                var sheding = s.洗练设定;
                var propType = new DataProcess().GetAPType(sheding.所需道具);
                if (propType == null)
                {
                    return "所需道具不存在于当前版本，请联系管理员检查！";
                }

                var prop = new DataProcess().GetAP_ID(sheding.所需道具);
                var propNum = 1;//固定只需要一个
                if (prop == null || Convert.ToInt32(prop.道具数量) < propNum)
                {
                    return "洗练所需道具不足，无法洗练！需要" + propType.道具名字 + "*" + propNum;
                }
                #region 结晶扣除
                var 当前魂器 = user.魂器列表[id];
                string jg = DZ.SubSkjj_getjj(sheding.所需结晶[当前魂器.转灵品级].ToString(), "洗练魂器" + id + ",当前词条：" + JsonConvert.SerializeObject(user.魂器列表[id].词条), 1);
                if (string.IsNullOrEmpty(jg))
                {
                    return "连接超时，稍后再次尝试！";
                }
                string salt = DataProcess.GetSalt();
                //new DataProcess().SaveFile(DataProcess.GetStringHash(jg + salt),"a.txt");
                var jjg = jg.Split('|');
                if (jjg.Length >= 2 && SkCryptography.GetHash.GetStringHash(jjg[0] + salt).Equals("947812dc38e7868ede065c4bf0835342"))//VIP特权  获得积分
                {
                    DZ.JJ = Convert.ToInt32(jjg[1]);
                    if (Convert.ToInt16(user.vip) == NumEncrypt.十())
                    {
                        int jf = (int)Math.Floor(sheding.所需结晶[当前魂器.转灵品级] / 1000.0);
                        user.VIP积分 = (Convert.ToInt32(user.VIP积分) + jf).ToString();
                        if (sheding.所需结晶[当前魂器.转灵品级] >= 1000)
                        {
                            if (user.星辰VIP)
                            {
                                DataProcess.GameForm.发送游戏公告("尊敬的星辰VIP玩家，您本次购物获得了" + jf + "积分。");
                            }
                            else if (user.至尊VIP)
                            {
                                DataProcess.GameForm.发送游戏公告("尊敬的至尊VIP玩家，您本次购物获得了" + jf + "积分。");
                            }
                            else
                            {
                                DataProcess.GameForm.发送游戏公告("尊敬的VIP10玩家，您本次购物获得了" + jf + "积分。");
                            }
                        }

                    }
                    user.累计消耗结晶 = (Convert.ToInt32(user.累计消耗结晶) + sheding.所需结晶[当前魂器.转灵品级]).ToString();
                    LogSystem.JoinLog(LogSystem.EventKind.结晶购物,
                        "道具名字：洗练魂器，商品数量：1，购买数量：1，花费结晶：" + sheding.所需结晶[当前魂器.转灵品级]);

                }
                else
                {
                    return "结晶扣除失败，无法继续！";
                }
                #endregion
                new DataProcess().ReviseOrDeletePP(prop, propNum);
                var 当前品级 = 品级[当前魂器.转灵品级];
                string[] 可洗属性 = sheding.转灵品级可洗练属性[当前品级].Split('|');
                if (可洗属性.Length == 0) return "当前转灵品级未设定可洗练词条，请联系管理员！";
                //记录洗练结果的变量
                Dictionary<String, double> sxs = new Dictionary<string, double>();
                List<String> ct = new List<string>();
                if (当前魂器.等级 > s.强化设定.最高等级) 当前魂器.等级 = s.强化设定.最高等级;
                foreach (var sx in 可洗属性)
                {
                    var thisSheding = sheding.强化等级对应范围[当前魂器.等级][sx].Split('|');
                    var min = Convert.ToDouble(thisSheding[0]);
                    var max = Convert.ToDouble(thisSheding[1]);
                    var num = DataProcess.RandomNext(min, max, 2);//取一个随机数，这个随机数表示随机的属性值
                    var bl = DataProcess.RandomNext(0.7, 1.0, 2);//取一个随机数，这个随机数表示，最大保留多少随机出来的属性值
                    num *= bl;
                    num = Math.Round(num, 2);//保留两位小数
                    if (num < min) num = min + 0.01;
                    //之所以弄两个随机数是为了让洗出极限值的可能性降低
                    if (!sxs.ContainsKey(sx)) sxs.Add(sx, num);
                    ct.Add(sx + "+" + (num * 100) + "%");

                }
                user.魂器列表[id].待保留词条 = sxs;
                user.魂器列表[id].洗练次数++;
                new DataProcess().SaveUserDataFile(user);
                DataProcess.GameForm.发送红色公告("洗练成功！当前获取到的词条：" + string.Join("、", ct) + "，当前剩余结晶：" + DZ.JJ + "(以实际为准)");

                return "洗练成功！当前获取到的词条：" + string.Join("、", ct);
            }
            else
            {
                return "魂器不存在，无法强化！";
            }
        }
        /// <summary>
        /// 强化元素
        /// </summary>
        /// <param name="id">魂器ID</param>
        /// <param name="ys">元素名</param>
        /// <returns></returns>
        public static string up_yuansu(String id, String ys)
        {
            var user = new DataProcess().ReadUserInfo();
            if (user.魂器列表 == null) user.魂器列表 = new Dictionary<string, hqinfo>();
            if (user.魂器列表.ContainsKey(id))
            {
                var hunqis = GetHunqis();
                var s = hunqis.FirstOrDefault(C => C.魂器ID == id);
                if (s == null)
                {
                    return "魂器不在规则中存在，请联系管理员检查！";
                }
                var sheding = s.元素设定.属性规则[ys];
                var propType = new DataProcess().GetAPType(sheding.强化设定.道具);
                if (propType == null)
                {
                    return "所需道具不存在于当前版本，请联系管理员检查！";
                }
                var prop = new DataProcess().GetAP_ID(sheding.强化设定.道具);
                if (user.魂器列表[id].元素等级 == null) user.魂器列表[id].元素等级 = new Dictionary<string, int>();
                if (!user.魂器列表[id].元素等级.ContainsKey(ys))
                {
                    user.魂器列表[id].元素等级.Add(id, 0);
                }
                if (user.魂器列表[id].元素等级[ys] >= sheding.强化设定.满级)
                {
                    return "当前魂器的" + ys + "元素已经满级！";
                }
                var propNum = s.元素设定.默认规则[sheding.强化设定.指定数量配置][user.魂器列表[id].元素等级[ys] + 1];
                if (prop == null || Convert.ToInt32(prop.道具数量) < propNum)
                {
                    return "强化所需道具不足，无法强化！需要" + propType.道具名字 + "*" + propNum;
                }
                new DataProcess().ReviseOrDeletePP(prop, Convert.ToInt32(propNum));
                user.魂器列表[id].元素等级[ys]++;
                new DataProcess().SaveUserDataFile(user);
                return "成功将【" + id + "】的" + ys + "元素等级强化到：" + user.魂器列表[id].元素等级[ys];
            }
            else
            {
                return "魂器不存在，无法强化！";
            }
        }

        /// <summary>
        /// 计算所有属性
        /// </summary>
        /// <returns>返回一个词典，代表每个属性的值</returns>
        public static Dictionary<String, double> calcALL()
        {
            var user = new DataProcess().ReadUserInfo();
            var allHQ = GetHunqis();
            if (user.魂器列表 == null) user.魂器列表 = new Dictionary<string, hqinfo>();
            var userALLHQ = user.魂器列表;
            var calcResult = new Dictionary<String, double>();
            calcResult.Add("攻击", 0);
            calcResult.Add("命中", 0);
            calcResult.Add("防御", 0);
            calcResult.Add("速度", 0);
            calcResult.Add("闪避", 0);
            calcResult.Add("生命", 0);
            calcResult.Add("魔法", 0);
            calcResult.Add("加深", 0);
            calcResult.Add("抵消", 0);
            calcResult.Add("吸血", 0);
            calcResult.Add("吸魔", 0);
            foreach (var uh in userALLHQ)
            {
                var hq = allHQ.FirstOrDefault(C => C.魂器ID == uh.Key);
                if (hq != null)
                {
                    var sheding = hq.元素设定;
                    //计算词条属性
                    if (uh.Value.词条 != null)
                    {
                        foreach (var ct in uh.Value.词条)
                        {
                            calcResult[ct.Key] += ct.Value;
                        }
                    }
                    //计算元素属性
                    foreach (var ys in uh.Value.元素等级)
                    {
                        var skey = sheding.属性规则[ys.Key].效果;
                        var snum = sheding.默认规则[sheding.属性规则[ys.Key].强化设定.指定属性配置][ys.Value];
                        calcResult[skey] += snum;
                    }
                }

            }
            return calcResult;
        }
    }

    public class hunqi_xilian
    {
        public string 所需道具 { get; set; }
        public Dictionary<int, int> 所需结晶 { get; set; }
        public Dictionary<String, String> 转灵品级可洗练属性 { get; set; }
        public Dictionary<int, Dictionary<String, String>> 强化等级对应范围 { get; set; }


    }

    public class hunqi_qianghua
    {
        public int 最高等级 { get; set; }
        public string 所需道具 { get; set; }
        public Dictionary<int, int> 所需数量 { get; set; }
    }
    public class hunqi_zhuanling
    {
        public string 所需道具 { get; set; }
        public Dictionary<int, int> 所需结晶 { get; set; }
        public Dictionary<int, int> 所需数量 { get; set; }

    }
    public class hunqi_yuansu
    {
        public Dictionary<String, hunqi_yuansu_guize> 属性规则 { get; set; }
        public Dictionary<String, Dictionary<int, double>> 默认规则 { get; set; }

    }

    public class hunqi_yuansu_guize
    {
        public string 效果 { get; set; }
        public hunqi_yuansu_guize_qianghua 强化设定 { get; set; }
    }

    public class hunqi_yuansu_guize_qianghua
    {
        public int 满级 { get; set; }
        public string 道具 { get; set; }
        public string 指定数量配置 { get; set; }
        public string 指定属性配置 { get; set; }
    }
}
