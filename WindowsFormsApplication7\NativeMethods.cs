﻿//using EasyHook;

using System;
using System.IO;
using System.Runtime.InteropServices;
using PetShikongTools;

namespace Shikong.Pokemon2.PCG
{
    internal static class NativeMethods
    {
        

        internal static class AntiGear
        {
            //////////////////////////////////
            internal static DateTime GameStartDate;
            internal static ulong GameStartTick64;
            internal static uint GameStartTick32;

            [DllImport("kernel32")]
            internal static extern ulong GetTickCount64();

            [DllImport("kernel32")]
            internal static extern uint GetTickCount();
            /*[DllImport("winmm")]
            internal static extern uint timeGetTime();

            /*[DllImport("winmm")]
            static extern void timeBeginPeriod(int t);
            [DllImport("winmm")]
            static extern void timeEndPeriod(int t);

            [DllImport("kernel32.dll ")]
            static extern bool QueryPerformanceCounter(ref long lpPerformanceCount);*/

            internal static void CalcTime()
            {
                DateTime now = DateTime.Now.ToLocalTime();
                ulong nowTick64 = GetTickCount64();
                uint nowTick32 = GetTickCount();
                /*Console.WriteLine("NowTick64:" + NowTick64.ToString());
                Console.WriteLine("NowTick32:" + NowTick32.ToString());
                Console.WriteLine("StartTick64:" + GameStartTick64.ToString());
                Console.WriteLine("StartTick32:" + GameStartTick32.ToString());*/
                long δ1 = Convert.ToInt64(now.Subtract(GameStartDate).TotalMilliseconds);
                long δ2 = Convert.ToInt64(nowTick64 - GameStartTick64);
                int δ3 = Convert.ToInt32(nowTick32 - GameStartTick32);
                if (DateTime.Compare(now, GameStartDate) <= 0 || Math.Abs(δ3 - δ2) > 10000 ||
                    Math.Abs(δ1 - δ2) > 10000 || Math.Abs(δ1 - δ3) > 10000)
                {
                    SkTools.AutoClosedMsgBox.Show(Res.RM.GetString("变速齿轮提示"), Res.RM.GetString("严正警告"), 5000);
                    //Tools.ForcedExit("变速齿轮Tick检测");
                    AntiCheat.SetDrop();
                }

                /*Console.WriteLine("系统时间差:" + δ1.ToString());
                Console.WriteLine("Tick64时间差" + δ2.ToString());
                Console.WriteLine("Tick32时间差" + δ3.ToString());*/
            }
        }

        internal class IsInUse
        {
            [DllImport("kernel32.dll")]
            private static extern IntPtr _lopen(string lpPathName, int iReadWrite);
            [DllImport("kernel32.dll")]
            private static extern bool CloseHandle(IntPtr hObject);
            private const int OF_READWRITE = 2;
            private const int OF_SHARE_DENY_NONE = 0x40;
            private readonly IntPtr HFILE_ERROR = new IntPtr(-1);
            internal bool IsFileInUse(string vFileName) 
            {

                if (!File.Exists(vFileName))
                {
                    return false;
                }
                IntPtr vHandle = _lopen(vFileName, OF_READWRITE | OF_SHARE_DENY_NONE);
                if (vHandle == HFILE_ERROR)
                {
                    CloseHandle(vHandle);
                    return true;
                }
                CloseHandle(vHandle);
                return false;

            }
        }

        internal static class ControlCursor
        {
            [DllImport("User32.DLL")]
            internal static extern bool SetSystemCursor(IntPtr hcur, uint id);
            //public const uint OCR_NORMAL = 32512;
            //public const uint OCR_IBEAM = 32513;

            [DllImport("User32.DLL")]
            internal static extern bool SystemParametersInfo(uint uiAction, uint uiParam,
                IntPtr pvParam, uint fWinIni);
            internal const uint SPI_SETCURSORS = 87;
            internal const uint SPIF_SENDWININICHANGE = 2;
        }
    }
}
