# 时空口袋妖怪反作弊系统分析文档

## 目录
1. [系统概述](#系统概述)
2. [核心组件](#核心组件)
3. [检测机制](#检测机制)
4. [权限控制系统](#权限控制系统)
5. [惩罚机制](#惩罚机制)
6. [技术实现](#技术实现)
7. [安全漏洞分析](#安全漏洞分析)
8. [建议改进](#建议改进)

## 系统概述

时空口袋妖怪游戏的反作弊系统是一个多层次的安全防护体系，主要包含以下几个核心功能模块：

- **进程检测**：检测恶意工具和修改器
- **数据完整性检测**：验证存档和配置文件的完整性
- **道具数量检测**：防止道具数量异常
- **VIP权限检测**：防止VIP权限异常
- **硬件绑定检测**：防止存档被非法转移
- **时间检测**：防止变速齿轮等时间修改工具

## 核心组件

### 1. AntiCheat.cs - 主要反作弊类

#### 关键文件路径
```csharp
internal static string FinalPath = Path.Combine(DataProcess.ApplicationDataPath, @"Tencent\Config\p2p_200014357082.ini");
internal static string FP1 = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), @"txsso.db");
internal static string FP2 = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), @"u7x5t4e.dll");
```

#### 主要检测方法
- `AntiCheat_A()`: 存档完整性检测
- `AntiCheat_B()`: 道具数量检测
- `AntiCheat_C()`: VIP权限和容量检测
- `CheckProcess()`: 进程检测
- `Anti_AnJian()`: 按键精灵检测

### 2. NativeMethods.cs - 底层检测

#### 变速齿轮检测
```csharp
internal static void CalcTime()
{
    // 通过比较系统时间、GetTickCount64和GetTickCount32的差异检测变速齿轮
    long δ1 = Convert.ToInt64(now.Subtract(GameStartDate).TotalMilliseconds);
    long δ2 = Convert.ToInt64(nowTick64 - GameStartTick64);
    int δ3 = Convert.ToInt32(nowTick32 - GameStartTick32);
    
    if (Math.Abs(δ3 - δ2) > 10000 || Math.Abs(δ1 - δ2) > 10000 || Math.Abs(δ1 - δ3) > 10000)
    {
        Tools.ForcedExit("变速齿轮Tick检测");
    }
}
```

## 检测机制

### 1. 进程检测 (CheckProcess)

#### 检测目标
- **内存修改器**: CheatEngine, OllyDbg, OllyICE, MHSCN, KnightV, 吾爱破解[LCG], VZLA ENGINE, 金山游侠
- **变速工具**: 齿轮, GEARNT
- **启动器进程**: 时空单机启动器

#### 检测逻辑
```csharp
foreach (Process process in jclb)
{
    if (process.MainWindowTitle.ToUpper().Contains("CHEATENGINE") || 
        process.MainWindowTitle.ToUpper() == "OLLYDBG" ||
        process.MainWindowTitle.ToUpper() == "OLLYICE")
    {
        process.Kill();
        CheatCodeMsg("000（请勿使用内存修改器）");
        PunishmentProcess(0);
    }
}
```

### 2. 道具数量检测 (AntiCheat_B)

#### 检测列表
```csharp
internal static Dictionary<string, int> CheckList = new Dictionary<string, int>
{
    {"2016102001", 1000000},      // 每日礼包
    {"2017060302", 2100000000},   // 强化石
    {"2016101802", 1000},         // 滑稽女神之卵
    {"2020120403", 100},          // ★高级宝石自选包★
    {"10002", 1},                 // 我的专属称号
    {"2", 1},                     // 测试道具
};
```

#### 检测逻辑
```csharp
foreach (PropInfo 待检测道具 in DataProcess.PP_List)
{
    if (CheckList.ContainsKey(待检测道具.道具类型ID))
    {
        if (Convert.ToInt32(待检测道具.道具数量) > CheckList[待检测道具.道具类型ID] && !new DataProcess().getPower())
        {
            CheatCodeMsg("01A");
            PunishmentProcess(2);
        }
    }
}
```

### 3. VIP权限检测 (AntiCheat_C)

#### VIP等级检测
```csharp
if (grade < 10)
{
    if (credit > 0)                    // VIP等级<10但有VIP积分
    {
        CheatCodeMsg("020");
        PunishmentProcess(2);
    }
    if (用户.至尊VIP)                   // VIP等级<10但有至尊VIP
    {
        CheatCodeMsg("023");
        PunishmentProcess(2);
    }
    if (用户.星辰VIP)                   // VIP等级<10但有星辰VIP
    {
        CheatCodeMsg("024");
        PunishmentProcess(2);
    }
}
else if (grade == 10)
{
    if (credit > 10000000)             // VIP积分超过10000000
    {
        CheatCodeMsg("021");
        PunishmentProcess(2);
    }
}
else if (grade > 10)                   // VIP等级超过10
{
    CheatCodeMsg("022");
    PunishmentProcess(2);
}
```

#### 容量检测
```csharp
if (Convert.ToInt16(用户.道具容量) > 500 && !new DataProcess().getPower())
{
    CheatCodeMsg("B50");
    PunishmentProcess(2);
}

if (Convert.ToInt16(用户.牧场容量) > 120 && !new DataProcess().getPower())
{
    CheatCodeMsg("B20");
    PunishmentProcess(2);
}
```

### 4. 存档完整性检测 (AntiCheat_A)

#### 版本检测
```csharp
if (Convert.ToInt32(user.版本号) > Convert.ToInt32(DataProcess.Version))
{
    Tools.ForcedExit("尝试回老版本");
}
```

#### 硬件绑定检测
```csharp
if (DataProcess.EnvironmentMode == 1)  // 云端模式
{
    if (!DataProcess.IP.Equals(user.NB1))
    {
        NotYourData();
    }
}
else if (DataProcess.EnvironmentMode == 0 || DataProcess.EnvironmentMode == 2)  // 本地模式
{
    if (!string.IsNullOrEmpty(user.NB1) && DataProcess.DiskInfo == user.NB1 ||
        !string.IsNullOrEmpty(user.NB2) && DataProcess.DiskInfo == user.NB2 ||
        !string.IsNullOrEmpty(user.NB3) && DataProcess.DiskInfo == user.NB3)
    {
        // 硬件信息匹配
    }
    else
    {
        NotYourData();
    }
}
```

### 5. 按键精灵检测 (Anti_AnJian)

#### 检测方法
1. **进程名检测**: 检查进程名和窗口标题是否包含"按键精灵"
2. **注册表检测**: 检查注册表中的按键精灵安装路径
3. **文件占用检测**: 检查按键精灵文件是否被占用

```csharp
RegistryKey ajml = Registry.ClassesRoot.CreateSubKey(
    "WOW6432Node\\CLSID\\{EBEB87A4-E151-4054-AB45-A6E094C5334B}\\LocalServer32");

if (ajml?.GetValue("") != null)
{
    path = ajml.GetValue("").ToString();
}

if (!string.IsNullOrWhiteSpace(path) && File.Exists(path))
{
    aj = new NativeMethods.IsInUse().IsFileInUse(path);
}
```

## 权限控制系统

### getPower() 方法

#### 权限验证逻辑
```csharp
public bool getPower()
{
    if (!Program.getDebug()) return false;
    String id = new DataProcess().ReadUserInfo().论坛ID;
    return (id == "青衫" || id == "heima");
}
```

#### 权限检查点
- 道具数量检测
- VIP权限检测
- 容量检测
- 禁止道具使用
- 调试功能访问

### Program.getDebug() 方法

#### 调试模式控制
```csharp
private static bool debug = true;

public static bool getDebug()
{
    return debug;
}
```

#### 启动时权限判断
```csharp
try
{
    if (!File.Exists("NewAdmin.exe") && !File.Exists(@"QStext2022.shan") && !new DataProcess().getPower())
    {
        debug = false;
    }
}
catch { debug = false; }
```

## 惩罚机制

### 1. 惩罚等级

#### 惩罚参数
- `bnum = 0`: 警告，不增加作弊计数
- `bnum = 1`: 轻度惩罚，增加作弊计数1
- `bnum = 2`: 重度惩罚，增加作弊计数2

#### 作弊计数逻辑
```csharp
if (bnum != 0)
{
    UserInfo 用户 = new DataProcess().ReadUserInfo();
    用户.b = (Convert.ToInt16(用户.b) + bnum).ToString();
    new DataProcess().SaveUserDataFile(用户);

    if (Convert.ToInt16(用户.b) >= 2)
    {
        Cheating_Punishment();
    }
}
```

### 2. 惩罚类型

#### 警告消息
```csharp
if (多次 == 0)
{
    SkTools.AutoClosedMsgBox.Show(Res.RM.GetString("修改器警告"), Res.RM.GetString("严正警告"), 2000);
}
else if (多次 == 1)
{
    SkTools.AutoClosedMsgBox.Show(Res.RM.GetString("多次作弊警告"), Res.RM.GetString("严正警告"), 2000);
}
```

#### 强制退出
```csharp
Tools.ForcedExit("作弊或尝试作弊处理");
```

### 3. 作弊历史检测

#### 检测文件
```csharp
internal static void Checking_CheatingHistory()
{
    if (File.Exists(FinalPath) || File.Exists(FP1) || File.Exists(FP2))
    {
        Cheating_Punishment();
        PunishmentProcess(0, 1);
    }
}
```

#### 惩罚处理
```csharp
public static void Cheating_Punishment()
{
    new DataProcess().ReadUserInfo1();
    File.WriteAllText(FP1, "");
    File.WriteAllText(FP2, "");
    PunishmentProcess(0, 1);
}
```

## 技术实现

### 1. 定时检测

#### 进程检测定时器
```csharp
private static void SetTimer_checkProcess()
{
    _checkProcess = new System.Timers.Timer(10000);  // 10秒检测一次
    _checkProcess.Elapsed += OnTimedEvent_checkProcess;
    _checkProcess.AutoReset = true;
    _checkProcess.Enabled = true;
}
```

#### 变速齿轮检测定时器
```csharp
private static void SetTimer_antiGear1()
{
    _antiGear1 = new System.Timers.Timer(300000);    // 5分钟检测一次
    _antiGear1.Elapsed += OnTimedEvent_antiGear1;
    _antiGear1.AutoReset = true;
    _antiGear1.Enabled = true;
}
```

### 2. 文件操作

#### 强制退出实现
```csharp
internal static void ForcedExit(string reason)
{   
    LogSystem.JoinLog(LogSystem.EventKind.强制退出, reason);
    try
    {
        LogSystem.SaveLog();
    }
    catch
    {
        Environment.Exit(0);
    }       
    Environment.Exit(0);
}
```

### 3. 日志记录

#### 日志类型
- `LogSystem.EventKind.作弊检测`: 作弊检测日志
- `LogSystem.EventKind.强制退出`: 强制退出日志
- `LogSystem.EventKind.按键精灵`: 按键精灵检测日志

## 安全漏洞分析

### 1. 权限绕过漏洞

#### 问题描述
- `getPower()` 方法仅检查论坛ID，容易被伪造
- 调试模式可以通过修改 `debug` 变量绕过

#### 风险等级: 高

### 2. 检测绕过漏洞

#### 问题描述
- 进程检测仅检查窗口标题，可以通过修改进程名绕过
- 道具数量检测的阈值固定，容易被分析

#### 风险等级: 中

### 3. 文件检测漏洞

#### 问题描述
- 硬件绑定检测依赖本地文件，容易被伪造
- 作弊历史文件可以被删除或修改

#### 风险等级: 中

### 4. 时间检测漏洞

#### 问题描述
- 变速齿轮检测的时间阈值固定(10000ms)
- 可以通过调整变速倍率绕过检测

#### 风险等级: 中

## 建议改进

### 1. 权限系统改进

#### 建议方案
- 实现基于数字签名的权限验证
- 添加服务器端权限验证
- 使用加密的权限令牌

### 2. 检测机制改进

#### 建议方案
- 实现动态阈值检测
- 添加行为分析检测
- 实现多层检测机制

### 3. 防护强度提升

#### 建议方案
- 实现代码混淆和加密
- 添加反调试机制
- 实现内存保护

### 4. 监控系统改进

#### 建议方案
- 实现实时监控系统
- 添加异常行为分析
- 实现自动封禁机制

## 总结

时空口袋妖怪的反作弊系统虽然具备基本的防护功能，但在安全性方面仍存在一些漏洞。主要问题集中在权限验证、检测机制和防护强度方面。建议通过加强权限验证、改进检测机制、提升防护强度等方式来完善反作弊系统。

---

**文档版本**: 1.0  
**分析日期**: 2024年  
**分析工具**: 代码静态分析 