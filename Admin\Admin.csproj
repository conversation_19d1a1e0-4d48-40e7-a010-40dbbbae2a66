﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{9CB89AFD-A914-43CE-B3DA-629C1EACB4FC}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Admin</RootNamespace>
    <AssemblyName>Admin</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\..\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup>
    <SignAssembly>false</SignAssembly>
  </PropertyGroup>
  <PropertyGroup>
    <AssemblyOriginatorKeyFile>PetShikong.pfx</AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Newtonsoft.Json, Version=6.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\MvcApplication1\bin\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="editor.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="editor.Designer.cs">
      <DependentUpon>editor.cs</DependentUpon>
    </Compile>
    <Compile Include="FROM.cs" />
    <Compile Include="getPROPJSON.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="getPROPJSON.Designer.cs">
      <DependentUpon>getPROPJSON.cs</DependentUpon>
    </Compile>
    <Compile Include="任务管理.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="任务管理.Designer.cs">
      <DependentUpon>任务管理.cs</DependentUpon>
    </Compile>
    <Compile Include="合成公式编辑.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="合成公式编辑.Designer.cs">
      <DependentUpon>合成公式编辑.cs</DependentUpon>
    </Compile>
    <Compile Include="地图掉落管理.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="地图掉落管理.Designer.cs">
      <DependentUpon>地图掉落管理.cs</DependentUpon>
    </Compile>
    <Compile Include="套装管理.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="套装管理.Designer.cs">
      <DependentUpon>套装管理.cs</DependentUpon>
    </Compile>
    <Compile Include="宠物进化路线.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="宠物进化路线.Designer.cs">
      <DependentUpon>宠物进化路线.cs</DependentUpon>
    </Compile>
    <Compile Include="管理称号.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="管理称号.Designer.cs">
      <DependentUpon>管理称号.cs</DependentUpon>
    </Compile>
    <Compile Include="装备列表.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="装备列表.Designer.cs">
      <DependentUpon>装备列表.cs</DependentUpon>
    </Compile>
    <Compile Include="怪物列表.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="怪物列表.Designer.cs">
      <DependentUpon>怪物列表.cs</DependentUpon>
    </Compile>
    <Compile Include="怪物加入列表.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="怪物加入列表.Designer.cs">
      <DependentUpon>怪物加入列表.cs</DependentUpon>
    </Compile>
    <Compile Include="管理窗口.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="管理窗口.Designer.cs">
      <DependentUpon>管理窗口.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="宠物列表.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="宠物列表.Designer.cs">
      <DependentUpon>宠物列表.cs</DependentUpon>
    </Compile>
    <Compile Include="装备管理.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="装备管理.Designer.cs">
      <DependentUpon>装备管理.cs</DependentUpon>
    </Compile>
    <Compile Include="输入框.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="输入框.Designer.cs">
      <DependentUpon>输入框.cs</DependentUpon>
    </Compile>
    <Compile Include="道具列表.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="道具列表.Designer.cs">
      <DependentUpon>道具列表.cs</DependentUpon>
    </Compile>
    <EmbeddedResource Include="editor.resx">
      <DependentUpon>editor.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="getPROPJSON.resx">
      <DependentUpon>getPROPJSON.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="任务管理.resx">
      <DependentUpon>任务管理.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="合成公式编辑.resx">
      <DependentUpon>合成公式编辑.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="地图掉落管理.resx">
      <DependentUpon>地图掉落管理.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="套装管理.resx">
      <DependentUpon>套装管理.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="宠物进化路线.resx">
      <DependentUpon>宠物进化路线.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="管理称号.resx">
      <DependentUpon>管理称号.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="装备列表.resx">
      <DependentUpon>装备列表.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="怪物列表.resx">
      <DependentUpon>怪物列表.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="怪物加入列表.resx">
      <DependentUpon>怪物加入列表.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="管理窗口.resx">
      <DependentUpon>管理窗口.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <EmbeddedResource Include="宠物列表.resx">
      <DependentUpon>宠物列表.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="装备管理.resx">
      <DependentUpon>装备管理.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="输入框.resx">
      <DependentUpon>输入框.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="道具列表.resx">
      <DependentUpon>道具列表.cs</DependentUpon>
    </EmbeddedResource>
    <None Include="ClassDiagram1.cd" />
    <None Include="PetShikong.pfx" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\WindowsFormsApplication7\PetShikong.csproj">
      <Project>{179d3d5d-d3fd-4d07-84cf-cb17bb26a5ff}</Project>
      <Name>PetShikong</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>