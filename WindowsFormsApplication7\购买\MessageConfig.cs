﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ShikongPlus.Pokemon2.PCG.购买
{
    /// <summary>
    /// 购买系统消息配置类
    /// 定义购买过程中各种提示消息的文本内容
    /// 支持通过服务器配置动态修改消息内容，便于多语言支持和文案调整
    /// </summary>
    public class MessageConfig
    {
        /// <summary>
        /// 道具被禁用时的提示消息
        /// 当玩家尝试购买被服务器禁用的道具时显示
        /// 对应原代码中的硬编码消息："该道具暂时禁止购买!"
        /// </summary>
        public string ItemBannedMessage { get; set; } = "该道具暂时禁止购买!";

        /// <summary>
        /// 愚人节活动提示消息
        /// 在愚人节期间购买结晶道具时显示的特殊消息
        /// 对应原代码中商店类型6的特殊逻辑
        /// </summary>
        public string AprilFoolMessage { get; set; } = "愚人节快乐！想要购买结晶道具请输入/彩蛋 关闭后在购买";

        /// <summary>
        /// 所需道具不足的消息模板
        /// 使用字符串格式化，{0}为道具名称，{1}为所需数量
        /// 例如："所需道具不足！需要神龙宝藏钥匙*5"
        /// 对应原代码中特殊商店的道具检查逻辑
        /// </summary>
        public string InsufficientItemTemplate { get; set; } = "所需道具不足！需要{0}*{1}";

        /// <summary>
        /// 背包容量已满时的提示消息
        /// 当玩家道具数量达到上限无法继续购买时显示
        /// 对应原代码中的CanAddProp()检查失败情况
        /// </summary>
        public string InventoryFullMessage { get; set; } = "购买失败，道具数量已达上限。（仓库道具数量 + 背包道具数量 > 道具容量），请出售或消耗道具后再买。";

        /// <summary>
        /// 结晶不足时的提示消息
        /// 当玩家在结晶商店购买失败时显示
        /// 对应原代码中商店类型6的购买失败逻辑
        /// </summary>
        public string InsufficientCrystalMessage { get; set; } = "结晶不足！请充值。";
    }
}
