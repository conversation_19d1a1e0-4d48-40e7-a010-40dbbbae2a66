﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ShikongPlus.Pokemon2.PCG.购买
{
    public class MessageConfig
    {
        public string ItemBannedMessage { get; set; } = "该道具暂时禁止购买!";
        public string AprilFoolMessage { get; set; } = "愚人节快乐！想要购买结晶道具请输入/彩蛋 关闭后在购买";
        public string InsufficientItemTemplate { get; set; } = "所需道具不足！需要{0}*{1}";
        public string InventoryFullMessage { get; set; } = "购买失败，道具数量已达上限。（仓库道具数量 + 背包道具数量 > 道具容量），请出售或消耗道具后再买。";
        public string InsufficientCrystalMessage { get; set; } = "结晶不足！请充值。";
    }
}
