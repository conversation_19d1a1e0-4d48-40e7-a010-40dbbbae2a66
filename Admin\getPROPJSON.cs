﻿using Newtonsoft.Json;
using PetShikong;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace Admin
{
    public partial class getPROPJSON : Form
    {
        public getPROPJSON()
        {
            InitializeComponent();
        }

        private void button2_Click(object sender, EventArgs e)
        {
           
            List<商店道具信息> 道具列表 = new List<商店道具信息>();
            if (textBox4.Text != "") {
                道具列表 = JsonConvert.DeserializeObject<List<商店道具信息>>(textBox4.Text);
               
            }
            商店道具信息 道具 = new 商店道具信息();
          
            
            
            道具类型 类型 = new 数据处理().取指定道具类型(dataGridView1.Rows[dataGridView1.CurrentRow.Index].Cells["num"].Value.ToString());
            道具.商品序号 = 类型.道具序号;
            道具.道具名字 = 类型.道具名字;
            道具.道具图标 = 类型.道具图标;
            道具.货币类型 = checkBox1.Checked ? "金币" : "元宝";
            道具.商品价格 = Convert.ToInt32(textBox1.Text);
            道具.限购数量 = Convert.ToInt32(textBox3.Text);
            if (道具.限购数量 > 道具.商品价格) {
                MessageBox.Show("数量和价格异常，请检查！");
            }
            道具列表.Add(道具);
            textBox4.Text = JsonConvert.SerializeObject(道具列表, Formatting.Indented);

        }
        List<道具类型> 所有道具 = new List<道具类型>();
        string 道具使用类型;
        道具类型 现编辑道具 = new 道具类型();
        道具具体信息 现编辑道具具体信息 = new 道具具体信息();
        private void button1_Click(object sender, EventArgs e)
        {
            dataGridView1.Rows.Clear();
            所有道具 = new 数据处理().读取所有道具类型();
            foreach (道具类型 道具 in 所有道具)
            {
                string[] str = { 道具.道具序号, 道具.道具名字 };
                dataGridView1.Rows.Add(str);
            }
            //label4.Text = "道具数：" + dataGridView1.Rows.Count;
        }

        private void button8_Click(object sender, EventArgs e)
        {
            dataGridView1.Rows.Clear();
            foreach (道具类型 道具 in 所有道具)
            {
                if (道具.道具名字.IndexOf(textBox2.Text) > -1 || 道具.道具序号.IndexOf(textBox2.Text) > -1)
                {
                    string[] str = { 道具.道具序号, 道具.道具名字 };
                    dataGridView1.Rows.Add(str);
                }

            }
        }

        private void dataGridView1_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {
            现编辑道具.道具名字 = dataGridView1.Rows[dataGridView1.CurrentRow.Index].Cells["name"].Value.ToString();
            现编辑道具.道具序号 = dataGridView1.Rows[dataGridView1.CurrentRow.Index].Cells["num"].Value.ToString();
            现编辑道具具体信息 = new 数据处理().读取道具脚本(现编辑道具.道具序号);
            textBox6.Text = 现编辑道具具体信息.道具脚本;
            textBox5.Text = 现编辑道具具体信息.道具说明;
        }

        private void button3_Click(object sender, EventArgs e)
        {
            var l = JsonConvert.DeserializeObject<List<商店道具信息>>(textBox4.Text);
            
            l = l.Select(a => new { a, newID = Guid.NewGuid() }).OrderBy(b => b.newID).Select(c => c.a).ToList();
            textBox4.Text = JsonConvert.SerializeObject(l, Formatting.Indented);
        }
    }
}
