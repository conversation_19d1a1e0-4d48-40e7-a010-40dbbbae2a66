﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ShikongPlus.Pokemon2.PCG.购买
{
    /// <summary>
    /// 商店类型配置类
    /// 定义游戏中各种商店的类型ID和名称映射
    /// 用于替代原代码中硬编码的商店类型判断
    /// </summary>
    public class ShopTypeConfig
    {
        /// <summary>
        /// 结晶商店的类型ID
        /// 对应原代码中的硬编码值：商店类型 == 6
        /// 结晶商店是使用时之结晶购买道具的特殊商店
        /// 具有特殊的购买逻辑和限制条件
        /// </summary>
        public int CrystalShopType { get; set; } = 6;

        /// <summary>
        /// 特殊商店的类型ID
        /// 对应原代码中的硬编码值：商店类型 == 8
        /// 特殊商店包含需要特定道具才能购买的商品
        /// 如神龙宝匣、神秘符文、龙魂召唤等
        /// </summary>
        public int SpecialShopType { get; set; } = 8;

        /// <summary>
        /// 商店类型ID与名称的映射字典
        /// Key: 商店类型ID
        /// Value: 商店显示名称
        ///
        /// 标准商店类型定义：
        /// 1 = 元宝商店 - 使用元宝购买道具
        /// 2 = 水晶商店 - 使用水晶购买道具
        /// 3 = 金币商店 - 使用金币购买道具
        /// 4 = 积分商店 - 使用积分购买道具
        /// 5 = 威望商店 - 使用威望购买道具
        /// 6 = 结晶商店 - 使用时之结晶购买道具
        /// 8 = 特殊商店 - 需要特定道具的特殊商店
        ///
        /// 支持动态添加新的商店类型而无需修改代码
        /// </summary>
        public Dictionary<int, string> ShopTypeNames { get; set; }
    }
}
