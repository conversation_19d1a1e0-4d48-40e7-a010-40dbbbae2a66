﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using 怪物信息 = Shikong.Pokemon2.PCG.MonsterInfo;
using 用户信息 = Shikong.Pokemon2.PCG.UserInfo;
using 宠物信息 = Shikong.Pokemon2.PCG.PetInfo;
using 宠物类型 = Shikong.Pokemon2.PCG.PetConfig;
using 道具信息 = Shikong.Pokemon2.PCG.PropInfo;
using 地图信息 = Shikong.Pokemon2.PCG.MapInfo;
using 装备类型 = Shikong.Pokemon2.PCG.EquipmentType;
using 装备信息 = Shikong.Pokemon2.PCG.EquipmentInfo;
using 道具类型 = Shikong.Pokemon2.PCG.PropType;
using 道具具体信息 = Shikong.Pokemon2.PCG.PropConfig;
using 数据处理 = Shikong.Pokemon2.PCG.DataProcess;
using Shikong.Pokemon2.PCG;

namespace Admin
{
    public partial class 合成公式编辑 : Form
    {
        public 合成公式编辑()
        {
            InitializeComponent();
        }
        List<宠物类型> 所有宠物 = new 数据处理().ReadPetTypeList();
        宠物类型 现编辑宠物 = new 宠物类型();
        private void button1_Click(object sender, EventArgs e)
        {
            dataGridView1.Rows.Clear();
            所有宠物 = new 数据处理().ReadPetTypeList();
            foreach (宠物类型 宠物 in 所有宠物)
            {
                string[] str = { 宠物.宠物序号, 宠物.宠物名字,宠物.系别 };
                dataGridView1.Rows.Add(str);
            }
            label4.Text = "宠物数：" + dataGridView1.Rows.Count;
        }

        private void button8_Click(object sender, EventArgs e)
        {
            dataGridView1.Rows.Clear();
            foreach (宠物类型 宠物 in 所有宠物)
            {
                if (宠物.宠物名字.IndexOf(textBox2.Text) > -1 || 宠物.宠物序号.IndexOf(textBox2.Text) > -1)
                {
                    string[] str = { 宠物.宠物序号, 宠物.宠物名字, 宠物.系别 };
                    dataGridView1.Rows.Add(str);
                }

            }
            label4.Text = "宠物数：" + dataGridView1.Rows.Count;
        }

        private void button2_Click(object sender, EventArgs e)
        {
            string[] str = { dataGridView1.Rows[dataGridView1.CurrentRow.Index].Cells["num"].Value.ToString(), dataGridView1.Rows[dataGridView1.CurrentRow.Index].Cells["name"].Value.ToString(), "0" ,"0","0","0",""};
            dataGridView2.Rows.Add(str);
        }

        private void 合成公式编辑_Load(object sender, EventArgs e)
        {

        }
    }
}
