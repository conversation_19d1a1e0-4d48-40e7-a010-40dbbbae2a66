﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using PetShikong;

namespace Admin
{
    public partial class 地图掉落管理 : Form
    {
        public 地图掉落管理()
        {
            InitializeComponent();
        }
        List<地图信息> 所有地图信息 = new List<地图信息>();
        List<道具类型> 所有道具 = new 数据处理().读取所有道具类型();
        private void 地图掉落管理_Load(object sender, EventArgs e)
        {
            string[] 地图编号集合 = { "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "101", "102", "103", "104", "105", "106", "107" };
            foreach(string 编号 in 地图编号集合)
            {
                地图信息 信息 = new 数据处理().取地图信息(编号);
                if (信息!=null)
                {
                    
                    所有地图信息.Add(信息);
                    dataGridView1.Rows.Add(编号);
                }
                

            }
            
        }

        private void dataGridView1_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {
            foreach (地图信息 信息 in 所有地图信息)
            {
                string 现选行 = dataGridView1.Rows[dataGridView1.CurrentRow.Index].Cells[0].Value.ToString();
                if (信息.地图ID == 现选行)
                {
                    dataGridView2.Rows.Clear();
                    string[] 掉落组 = 信息.掉落道具.Split('|');
                    int 总道具数 = 0;
                    string[] 小指令组;
                    foreach (string 物品 in 掉落组)
                    {
                        小指令组 = 物品.Split(',');
                        if (小指令组.Length > 1)
                        {
                            string[] s = { 小指令组[0], 小指令组[1], 取物品名称(小指令组[0]) };
                            dataGridView2.Rows.Add(s);
                            总道具数 += Convert.ToInt32(小指令组[1]);
                        }
                        else
                        {
                            string[] s = { 小指令组[0], "1" , 取物品名称(小指令组[0]) };
                            dataGridView2.Rows.Add(s);
                            总道具数++;
                        }
                    }
                    break;
                  
                }
            }
        }

        private void button2_Click(object sender, EventArgs e)
        {
            string tex = "";
            for (int row = 0; row < dataGridView2.Rows.Count-1; ++row)
            {
                if (row != 0)
                    tex += '|';

                tex = tex + dataGridView2.Rows[row].Cells[0].Value.ToString() + ',' + dataGridView2.Rows[row].Cells[1].Value.ToString();
            }
            textBox1.Text = tex;
        }

        private void button1_Click(object sender, EventArgs e)
        {
            dataGridView1.Rows.Clear();
            string[] 地图编号集合 = { "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "101", "102", "103", "104", "105", "106", "107" };
            foreach (string 编号 in 地图编号集合)
            {
                地图信息 信息 = new 数据处理().取地图信息(编号);
                if (信息 != null)
                {

                    所有地图信息.Add(信息);
                    dataGridView1.Rows.Add(编号);
                }


            }
        }

        private string 取物品名称(string 物品序号)
        {
            if (物品序号 == null || 物品序号 == "" || 物品序号 == "!@#")
                return "";
            foreach(道具类型 道具 in 所有道具)
            {
                if (道具.道具序号 == 物品序号)
                    return 道具.道具名字;
            }
            return "";
        }
    }
}
