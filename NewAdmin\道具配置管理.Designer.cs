﻿namespace Admin
{
    partial class 道具配置管理
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.button8 = new System.Windows.Forms.Button();
            this.textBox2 = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.button1 = new System.Windows.Forms.Button();
            this.button_更新道具脚本 = new System.Windows.Forms.Button();
            this.button_更新道具说明 = new System.Windows.Forms.Button();
            this.textBox3 = new System.Windows.Forms.TextBox();
            this.label6 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.textBox1 = new System.Windows.Forms.TextBox();
            this.button2 = new System.Windows.Forms.Button();
            this.label2 = new System.Windows.Forms.Label();
            this.道具分类 = new System.Windows.Forms.ComboBox();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.checkBox_搜索时包含道具说明 = new System.Windows.Forms.CheckBox();
            this.checkBox_搜索时包含道具脚本 = new System.Windows.Forms.CheckBox();
            this.dataGridView1 = new System.Windows.Forms.DataGridView();
            this.num = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.name = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.道具图标 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column1 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column2 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.button3 = new System.Windows.Forms.Button();
            this.label3 = new System.Windows.Forms.Label();
            this.数量上限 = new System.Windows.Forms.TextBox();
            this.button4 = new System.Windows.Forms.Button();
            this.label4 = new System.Windows.Forms.Label();
            this.搜索_道具图标 = new System.Windows.Forms.TextBox();
            this.label7 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.搜索_道具分类 = new System.Windows.Forms.ComboBox();
            this.道具ID精确匹配 = new System.Windows.Forms.CheckBox();
            this.道具名称新 = new System.Windows.Forms.TextBox();
            this.button5 = new System.Windows.Forms.Button();
            this.label9 = new System.Windows.Forms.Label();
            this.道具图标新 = new System.Windows.Forms.TextBox();
            this.button6 = new System.Windows.Forms.Button();
            this.label10 = new System.Windows.Forms.Label();
            this.道具价格 = new System.Windows.Forms.TextBox();
            this.button7 = new System.Windows.Forms.Button();
            this.label11 = new System.Windows.Forms.Label();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView1)).BeginInit();
            this.SuspendLayout();
            // 
            // button8
            // 
            this.button8.Location = new System.Drawing.Point(166, 8);
            this.button8.Name = "button8";
            this.button8.Size = new System.Drawing.Size(59, 25);
            this.button8.TabIndex = 19;
            this.button8.Text = "搜索";
            this.button8.UseVisualStyleBackColor = true;
            this.button8.Click += new System.EventHandler(this.button8_Click);
            // 
            // textBox2
            // 
            this.textBox2.Location = new System.Drawing.Point(74, 8);
            this.textBox2.Multiline = true;
            this.textBox2.Name = "textBox2";
            this.textBox2.Size = new System.Drawing.Size(83, 21);
            this.textBox2.TabIndex = 18;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(14, 12);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(53, 12);
            this.label1.TabIndex = 17;
            this.label1.Text = "搜索内容";
            this.label1.Click += new System.EventHandler(this.label1_Click);
            // 
            // button1
            // 
            this.button1.Location = new System.Drawing.Point(224, 8);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(59, 25);
            this.button1.TabIndex = 15;
            this.button1.Text = "刷新";
            this.button1.UseVisualStyleBackColor = true;
            this.button1.Click += new System.EventHandler(this.button1_Click);
            // 
            // button_更新道具脚本
            // 
            this.button_更新道具脚本.ForeColor = System.Drawing.Color.Red;
            this.button_更新道具脚本.Location = new System.Drawing.Point(516, 420);
            this.button_更新道具脚本.Name = "button_更新道具脚本";
            this.button_更新道具脚本.Size = new System.Drawing.Size(104, 34);
            this.button_更新道具脚本.TabIndex = 28;
            this.button_更新道具脚本.Text = "更新道具脚本";
            this.button_更新道具脚本.UseVisualStyleBackColor = true;
            this.button_更新道具脚本.Click += new System.EventHandler(this.button_更新道具脚本_Click);
            // 
            // button_更新道具说明
            // 
            this.button_更新道具说明.Location = new System.Drawing.Point(516, 513);
            this.button_更新道具说明.Name = "button_更新道具说明";
            this.button_更新道具说明.Size = new System.Drawing.Size(104, 34);
            this.button_更新道具说明.TabIndex = 27;
            this.button_更新道具说明.Text = "更新道具说明";
            this.button_更新道具说明.UseVisualStyleBackColor = true;
            this.button_更新道具说明.Click += new System.EventHandler(this.button_更新道具说明_Click);
            // 
            // textBox3
            // 
            this.textBox3.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.textBox3.Location = new System.Drawing.Point(94, 458);
            this.textBox3.Multiline = true;
            this.textBox3.Name = "textBox3";
            this.textBox3.Size = new System.Drawing.Size(416, 101);
            this.textBox3.TabIndex = 26;
            // 
            // label6
            // 
            this.label6.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(15, 461);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(65, 12);
            this.label6.TabIndex = 25;
            this.label6.Text = "道具说明：";
            // 
            // label5
            // 
            this.label5.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(15, 397);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(65, 12);
            this.label5.TabIndex = 24;
            this.label5.Text = "道具脚本：";
            // 
            // textBox1
            // 
            this.textBox1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.textBox1.Location = new System.Drawing.Point(94, 394);
            this.textBox1.Multiline = true;
            this.textBox1.Name = "textBox1";
            this.textBox1.Size = new System.Drawing.Size(416, 60);
            this.textBox1.TabIndex = 23;
            // 
            // button2
            // 
            this.button2.Location = new System.Drawing.Point(636, 338);
            this.button2.Name = "button2";
            this.button2.Size = new System.Drawing.Size(104, 34);
            this.button2.TabIndex = 31;
            this.button2.Text = "批量修改分类";
            this.button2.UseVisualStyleBackColor = true;
            this.button2.Click += new System.EventHandler(this.button2_Click);
            // 
            // label2
            // 
            this.label2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(636, 297);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(65, 12);
            this.label2.TabIndex = 29;
            this.label2.Text = "道具分类：";
            // 
            // 道具分类
            // 
            this.道具分类.FormattingEnabled = true;
            this.道具分类.Items.AddRange(new object[] {
            "其他",
            "礼包",
            "消耗",
            "特殊",
            "收集",
            "地图钥匙",
            "进化材料",
            "技能书",
            "战斗捕捉",
            "宠物合成",
            "魔法卡牌",
            "皮肤",
            "宠物卵",
            "图纸",
            "活动道具",
            "涅槃类材料",
            "强化辅助",
            "岁月蚀刻"});
            this.道具分类.Location = new System.Drawing.Point(638, 312);
            this.道具分类.Name = "道具分类";
            this.道具分类.Size = new System.Drawing.Size(87, 20);
            this.道具分类.TabIndex = 53;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.checkBox_搜索时包含道具说明);
            this.groupBox1.Controls.Add(this.checkBox_搜索时包含道具脚本);
            this.groupBox1.Location = new System.Drawing.Point(647, 488);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(149, 71);
            this.groupBox1.TabIndex = 54;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "搜索设置";
            // 
            // checkBox_搜索时包含道具说明
            // 
            this.checkBox_搜索时包含道具说明.AutoSize = true;
            this.checkBox_搜索时包含道具说明.Location = new System.Drawing.Point(12, 43);
            this.checkBox_搜索时包含道具说明.Name = "checkBox_搜索时包含道具说明";
            this.checkBox_搜索时包含道具说明.Size = new System.Drawing.Size(132, 16);
            this.checkBox_搜索时包含道具说明.TabIndex = 3;
            this.checkBox_搜索时包含道具说明.Text = "搜索时包含道具说明";
            this.checkBox_搜索时包含道具说明.UseVisualStyleBackColor = true;
            // 
            // checkBox_搜索时包含道具脚本
            // 
            this.checkBox_搜索时包含道具脚本.AutoSize = true;
            this.checkBox_搜索时包含道具脚本.Location = new System.Drawing.Point(12, 21);
            this.checkBox_搜索时包含道具脚本.Name = "checkBox_搜索时包含道具脚本";
            this.checkBox_搜索时包含道具脚本.Size = new System.Drawing.Size(132, 16);
            this.checkBox_搜索时包含道具脚本.TabIndex = 2;
            this.checkBox_搜索时包含道具脚本.Text = "搜索时包含道具脚本";
            this.checkBox_搜索时包含道具脚本.UseVisualStyleBackColor = true;
            // 
            // dataGridView1
            // 
            this.dataGridView1.AllowUserToAddRows = false;
            this.dataGridView1.AllowUserToDeleteRows = false;
            this.dataGridView1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.dataGridView1.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridView1.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.num,
            this.name,
            this.道具图标,
            this.Column1,
            this.Column2});
            this.dataGridView1.Location = new System.Drawing.Point(16, 61);
            this.dataGridView1.Name = "dataGridView1";
            this.dataGridView1.ReadOnly = true;
            this.dataGridView1.RowHeadersVisible = false;
            this.dataGridView1.RowTemplate.Height = 23;
            this.dataGridView1.Size = new System.Drawing.Size(620, 325);
            this.dataGridView1.TabIndex = 55;
            this.dataGridView1.CellContentClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.dataGridView1_CellContentClick);
            this.dataGridView1.SelectionChanged += new System.EventHandler(this.dataGridView1_Click);
            this.dataGridView1.Click += new System.EventHandler(this.dataGridView1_Click);
            // 
            // num
            // 
            this.num.HeaderText = "道具序号";
            this.num.Name = "num";
            this.num.ReadOnly = true;
            // 
            // name
            // 
            this.name.HeaderText = "道具名称";
            this.name.Name = "name";
            this.name.ReadOnly = true;
            this.name.Width = 150;
            // 
            // 道具图标
            // 
            this.道具图标.HeaderText = "道具图标";
            this.道具图标.Name = "道具图标";
            this.道具图标.ReadOnly = true;
            this.道具图标.Width = 130;
            // 
            // Column1
            // 
            this.Column1.HeaderText = "道具分类";
            this.Column1.Name = "Column1";
            this.Column1.ReadOnly = true;
            this.Column1.Width = 130;
            // 
            // Column2
            // 
            this.Column2.HeaderText = "最大数量";
            this.Column2.Name = "Column2";
            this.Column2.ReadOnly = true;
            this.Column2.Width = 90;
            // 
            // button3
            // 
            this.button3.Location = new System.Drawing.Point(638, 441);
            this.button3.Name = "button3";
            this.button3.Size = new System.Drawing.Size(116, 34);
            this.button3.TabIndex = 57;
            this.button3.Text = "批量修改最大数量";
            this.button3.UseVisualStyleBackColor = true;
            this.button3.Click += new System.EventHandler(this.button3_Click);
            // 
            // label3
            // 
            this.label3.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(638, 400);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(161, 12);
            this.label3.TabIndex = 56;
            this.label3.Text = "数量上限（为空/0不限制）：";
            // 
            // 数量上限
            // 
            this.数量上限.Location = new System.Drawing.Point(638, 415);
            this.数量上限.Name = "数量上限";
            this.数量上限.Size = new System.Drawing.Size(83, 21);
            this.数量上限.TabIndex = 58;
            // 
            // button4
            // 
            this.button4.Location = new System.Drawing.Point(480, 8);
            this.button4.Name = "button4";
            this.button4.Size = new System.Drawing.Size(156, 25);
            this.button4.TabIndex = 59;
            this.button4.Text = "保存道具配置";
            this.button4.UseVisualStyleBackColor = true;
            this.button4.Click += new System.EventHandler(this.button4_Click);
            // 
            // label4
            // 
            this.label4.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(427, 41);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(77, 12);
            this.label4.TabIndex = 60;
            this.label4.Text = "现编辑道具：";
            // 
            // 搜索_道具图标
            // 
            this.搜索_道具图标.Location = new System.Drawing.Point(72, 36);
            this.搜索_道具图标.Name = "搜索_道具图标";
            this.搜索_道具图标.Size = new System.Drawing.Size(83, 21);
            this.搜索_道具图标.TabIndex = 61;
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(40, 41);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(29, 12);
            this.label7.TabIndex = 62;
            this.label7.Text = "图标";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(161, 41);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(29, 12);
            this.label8.TabIndex = 63;
            this.label8.Text = "分类";
            // 
            // 搜索_道具分类
            // 
            this.搜索_道具分类.FormattingEnabled = true;
            this.搜索_道具分类.Items.AddRange(new object[] {
            "其他",
            "礼包",
            "消耗",
            "特殊",
            "收集",
            "地图钥匙",
            "进化材料",
            "技能书",
            "战斗捕捉",
            "宠物合成",
            "魔法卡牌",
            "皮肤",
            "宠物卵",
            "图纸",
            "活动道具",
            "涅槃类材料",
            "强化辅助",
            "岁月蚀刻"});
            this.搜索_道具分类.Location = new System.Drawing.Point(196, 37);
            this.搜索_道具分类.Name = "搜索_道具分类";
            this.搜索_道具分类.Size = new System.Drawing.Size(87, 20);
            this.搜索_道具分类.TabIndex = 64;
            // 
            // 道具ID精确匹配
            // 
            this.道具ID精确匹配.AutoSize = true;
            this.道具ID精确匹配.Location = new System.Drawing.Point(299, 39);
            this.道具ID精确匹配.Name = "道具ID精确匹配";
            this.道具ID精确匹配.Size = new System.Drawing.Size(108, 16);
            this.道具ID精确匹配.TabIndex = 65;
            this.道具ID精确匹配.Text = "道具ID精确匹配";
            this.道具ID精确匹配.UseVisualStyleBackColor = true;
            // 
            // 道具名称新
            // 
            this.道具名称新.Location = new System.Drawing.Point(638, 77);
            this.道具名称新.Name = "道具名称新";
            this.道具名称新.Size = new System.Drawing.Size(153, 21);
            this.道具名称新.TabIndex = 68;
            // 
            // button5
            // 
            this.button5.Location = new System.Drawing.Point(638, 103);
            this.button5.Name = "button5";
            this.button5.Size = new System.Drawing.Size(116, 28);
            this.button5.TabIndex = 67;
            this.button5.Text = "修改选中道具";
            this.button5.UseVisualStyleBackColor = true;
            this.button5.Click += new System.EventHandler(this.button5_Click);
            // 
            // label9
            // 
            this.label9.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(638, 62);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(53, 12);
            this.label9.TabIndex = 66;
            this.label9.Text = "道具名称";
            // 
            // 道具图标新
            // 
            this.道具图标新.Location = new System.Drawing.Point(638, 152);
            this.道具图标新.Name = "道具图标新";
            this.道具图标新.Size = new System.Drawing.Size(153, 21);
            this.道具图标新.TabIndex = 71;
            // 
            // button6
            // 
            this.button6.Location = new System.Drawing.Point(638, 178);
            this.button6.Name = "button6";
            this.button6.Size = new System.Drawing.Size(116, 28);
            this.button6.TabIndex = 70;
            this.button6.Text = "修改选中道具";
            this.button6.UseVisualStyleBackColor = true;
            this.button6.Click += new System.EventHandler(this.button6_Click);
            // 
            // label10
            // 
            this.label10.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(638, 137);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(53, 12);
            this.label10.TabIndex = 69;
            this.label10.Text = "道具图标";
            // 
            // 道具价格
            // 
            this.道具价格.Location = new System.Drawing.Point(638, 227);
            this.道具价格.Name = "道具价格";
            this.道具价格.Size = new System.Drawing.Size(153, 21);
            this.道具价格.TabIndex = 74;
            // 
            // button7
            // 
            this.button7.Location = new System.Drawing.Point(638, 253);
            this.button7.Name = "button7";
            this.button7.Size = new System.Drawing.Size(116, 28);
            this.button7.TabIndex = 73;
            this.button7.Text = "修改选中道具";
            this.button7.UseVisualStyleBackColor = true;
            this.button7.Click += new System.EventHandler(this.button7_Click);
            // 
            // label11
            // 
            this.label11.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(638, 212);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(53, 12);
            this.label11.TabIndex = 72;
            this.label11.Text = "道具价格";
            // 
            // 道具配置管理
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(806, 569);
            this.Controls.Add(this.道具价格);
            this.Controls.Add(this.button7);
            this.Controls.Add(this.label11);
            this.Controls.Add(this.道具图标新);
            this.Controls.Add(this.button6);
            this.Controls.Add(this.label10);
            this.Controls.Add(this.道具名称新);
            this.Controls.Add(this.button5);
            this.Controls.Add(this.label9);
            this.Controls.Add(this.道具ID精确匹配);
            this.Controls.Add(this.搜索_道具分类);
            this.Controls.Add(this.label8);
            this.Controls.Add(this.label7);
            this.Controls.Add(this.搜索_道具图标);
            this.Controls.Add(this.label4);
            this.Controls.Add(this.button4);
            this.Controls.Add(this.数量上限);
            this.Controls.Add(this.button3);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.dataGridView1);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.道具分类);
            this.Controls.Add(this.button2);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.button_更新道具脚本);
            this.Controls.Add(this.button_更新道具说明);
            this.Controls.Add(this.textBox3);
            this.Controls.Add(this.label6);
            this.Controls.Add(this.label5);
            this.Controls.Add(this.textBox1);
            this.Controls.Add(this.button8);
            this.Controls.Add(this.textBox2);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.button1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.Name = "道具配置管理";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Load += new System.EventHandler(this.道具配置管理_Load);
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView1)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Button button8;
        private System.Windows.Forms.TextBox textBox2;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Button button1;
        private System.Windows.Forms.Button button_更新道具脚本;
        private System.Windows.Forms.Button button_更新道具说明;
        private System.Windows.Forms.TextBox textBox3;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.TextBox textBox1;
        private System.Windows.Forms.Button button2;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.ComboBox 道具分类;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.CheckBox checkBox_搜索时包含道具说明;
        private System.Windows.Forms.CheckBox checkBox_搜索时包含道具脚本;
        private System.Windows.Forms.DataGridView dataGridView1;
        private System.Windows.Forms.Button button3;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.TextBox 数量上限;
        private System.Windows.Forms.DataGridViewTextBoxColumn num;
        private System.Windows.Forms.DataGridViewTextBoxColumn name;
        private System.Windows.Forms.DataGridViewTextBoxColumn 道具图标;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column1;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column2;
        private System.Windows.Forms.Button button4;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.TextBox 搜索_道具图标;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.ComboBox 搜索_道具分类;
        private System.Windows.Forms.CheckBox 道具ID精确匹配;
        private System.Windows.Forms.TextBox 道具名称新;
        private System.Windows.Forms.Button button5;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.TextBox 道具图标新;
        private System.Windows.Forms.Button button6;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.TextBox 道具价格;
        private System.Windows.Forms.Button button7;
        private System.Windows.Forms.Label label11;
    }
}