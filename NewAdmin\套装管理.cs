﻿
using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using Shikong;
using 怪物信息 = Shikong.Pokemon2.PCG.MonsterInfo;
using 用户信息 = Shikong.Pokemon2.PCG.UserInfo;
using 宠物信息 = Shikong.Pokemon2.PCG.PetInfo;
using 宠物类型 = Shikong.Pokemon2.PCG.PetConfig;
using 道具信息 = Shikong.Pokemon2.PCG.PropInfo;
using 地图信息 = Shikong.Pokemon2.PCG.MapInfo;
using 装备类型 = Shikong.Pokemon2.PCG.EquipmentType;
using 装备信息 = Shikong.Pokemon2.PCG.EquipmentInfo;
using 道具类型 = Shikong.Pokemon2.PCG.PropType;
using 道具具体信息 = Shikong.Pokemon2.PCG.PropConfig;
using 数据处理 = Shikong.Pokemon2.PCG.DataProcess;
using Shikong.Pokemon2.PCG;

namespace Admin
{
    public partial class 套装管理 : Form
    {
        public TextBox TEXT;
        public 套装管理()
        {
            InitializeComponent();
        }
        List<suits> 套装列表 = new 数据处理().GetAllSuits();
        private void 套装管理_Load(object sender, EventArgs e)
        {
            dataGridView1.DataSource = 套装列表;

        }
        private void textBox1_TextChanged(object sender, EventArgs e)
        {

        }
        private void linkLabel13_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            
            装备列表 列表 = new 装备列表();
            列表.类型 = "ID";
            列表.TEXT = textBox3;
            列表.ShowDialog();
        }

        private void button2_Click(object sender, EventArgs e)
        {
            update();
            suits 套装 = new suits();
            套装.套装名=名字.Text;
            套装.套装序号=序号.Text;
            套装.装备列表 = textBox3.Text.Split('|').ToList();
            套装.套装属性 = 套装属性;
            new 数据处理().SaveSuit(套装);
            MessageBox.Show("增加完毕!");
        }
        List<suit> 套装属性 = new List<suit>();
        private void button1_Click(object sender, EventArgs e)
        {
          
            suit s = new suit();
            s.addNump = textBox2.Text;
            s.Type = comboBox1.Text;
            套装属性.Add(s);
            dataGridView2.DataSource = null;
            dataGridView2.DataSource = 套装属性;
            label5.Text = "一共有" + textBox3.Text.Split('|').Count() + "件装备,已添加了" + 套装属性.Count() + "个属性";
        }

        private void linkLabel1_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            update();
            MessageBox.Show("应用完毕!");
        }
        public void update() {
            List<String> list = textBox3.Text.Split('|').ToList();
            foreach (String s in list)
            {
                if (s.Length == 0)
                {
                    continue;
                }
                装备类型 装备 = new 数据处理().GetAET(s);
                装备.suitID = 序号.Text;
                new 数据处理().AddNewEquipment(装备, true);
            }
           
        }
        private void linkLabel2_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            套装属性 = new List<suit>();
            dataGridView2.DataSource = 套装属性;
            label5.Text = "一共有" + textBox3.Text.Split('|').Count() + "件装备,已添加了" + 套装属性.Count() + "个属性";
        }

        private void textBox3_TextChanged(object sender, EventArgs e)
        {
            label5.Text = "一共有" + textBox3.Text.Split('|').Count() + "件装备,已添加了" + 套装属性.Count() + "个属性";
        }

        private void button3_Click(object sender, EventArgs e)
        {
            if (TEXT == null) return;
            TEXT.Text = dataGridView1.CurrentRow.Cells["套装序号"].Value.ToString();
            Close();
        }

        private void dataGridView1_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {

        }

        private void 复制数据为EXCEL信息_Click(object sender, EventArgs e)
        {
            //套装名 \t 属性+数值 （包含小数点转为百分比）
            string str = "";
            foreach(var t in 套装列表)
            {
                string txt = $"{t.套装名}\t";
                foreach(var sx in t.套装属性)
                {
                    txt += sx.Type + "+" + 属性内容处理(sx.addNump) + "\t";
                }
                str += txt +"\r\n";
            }
            Clipboard.SetText(str);
            MessageBox.Show("已复制");
        }

        /// <summary>
        /// 如果传入的属性不包含小数点则返回整数，否则返回百分比
        /// </summary>
        /// <param name="sx"></param>
        /// <returns></returns>
        string 属性内容处理(string sx)
        {
            if (sx == null)
            {
                return null;
            }
            if (sx.IndexOf(".") == -1)
            {
                return sx;
            }
            else
            {
                double num = Convert.ToDouble(sx) * 100;
                return num.ToString() + "%";
            }
        }

    }
}
