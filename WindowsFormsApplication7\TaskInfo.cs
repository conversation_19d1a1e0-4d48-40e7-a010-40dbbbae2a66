﻿using System.Collections.Generic;

namespace Shikong.Pokemon2.PCG
{
    public class TaskInfo
    {
        public string 任务序号 { get; set; }
        public string 任务名 { get; set; }
        public List<task> 任务目标 { get; set; }
        public string 指定宠物 { get; set; }
        public string 任务奖励 { get; set; }
        /// <summary>
        /// 为1就是允许重复,否则不允许重复
        /// </summary>
        public string 允许重复 { get; set; }
        /// <summary>
        /// 1为没有完成，0为已经完成
        /// </summary>
        public string 已完成 { get; set; } = "0";

        public string 前置任务 { get; set; }
        public string 任务介绍 { get; set; }
        public bool? 网络任务 { get; set; }
    }
}
