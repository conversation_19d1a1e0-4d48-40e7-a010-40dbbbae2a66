﻿namespace Shikong.Pokemon2.PCG
{
    public class FightResult
    {
        /// <summary>
        /// 1为结束 0为没结束
        /// </summary>
        public int 战斗是否结束 { get; set; }
        public int 是否死亡 { get; set; }
        public long 输出 { get; set; }
        public long 受到伤害 { get; set; }
        public long 对方剩余HP { get; set; }
        public long 己方剩余HP { get; set; }
        public string 获得道具 { get; set; }
        public int 获得元宝 { get; set; }
        public int 获得金币 { get; set; }
        public int 获得经验 { get; set; }
        public long 抵消伤害 { get; set; }
        public long 加深伤害 { get; set; }
        public long 吸血 { get; set; }
        public long 吸魔 { get; set; }
        public long 剩余魔法 { get; set; }
        public int Auto { get; set; }  //0 正常 //1 打不过 2没钥匙

        public int advance {get; set; }
    }
}
