﻿using System;
using System.IO;
using PetShikongTools;

namespace Shikong.Pokemon2.PCG
{
    internal class LogSystem
    {
        internal enum EventKind
        {
            启动游戏 = 100,
            界面加载 = 101,
            关闭游戏 = 102,
            登录论坛 = 103,
            结晶购物 = 104,
            宠物缓存 = 105,
            自动存档 = 106,
            自动备份 = 107,
            作弊检测 = 108,
            强制退出 = 109,
            按键精灵 = 110,
            每日礼包 = 111,
            加入日志 = 112,
            领取邮件 = 113
            ////更多事件有待扩充
        }

        internal static string LogPath = string.Empty;
        internal const string LogDirectory = @"PageMain/Logs";

        
        internal static void JoinLog(EventKind eventKind, string data = "")
        {
            string nowTime = new Tools.GetTime().GetSystemTime();
            if (eventKind == EventKind.启动游戏)
            {
                string netVersion = SkTools.CheckSystemInfos.GetNetFrameworkVersion();
                LogPath = Path.Combine(LogDirectory, nowTime + ".txt");
                Res.Log.Append("******Log of Shikong.Pokemon2.PCG******");
                Res.Log.Append($"\r\nGame Version = {DataProcess.Version}.");
                Res.Log.Append($"\r\nOS Version = {Environment.OSVersion.VersionString}.");
                Res.Log.Append($"\r\n.Net Version = {netVersion}.");          
                Res.Log.Append($"\r\n游戏启动时间：{nowTime}。");
            }
            else if (eventKind == EventKind.界面加载)
            {
                Res.Log.Append($"\r\n游戏界面加载完毕时间：{nowTime}。");
            }
            else if (eventKind == EventKind.关闭游戏)
            {
                Res.Log.Append($"\r\n游戏关闭时间：{nowTime}。");
            }
            else if (eventKind == EventKind.登录论坛)
            {
                Res.Log.Append($"\r\n登录论坛时间：{nowTime}。{data}。");
            }
            else if (eventKind == EventKind.结晶购物)
            {
                Res.Log.Append($"\r\n结晶购物时间：：{nowTime}。{data}。");
            }
            else if (eventKind == EventKind.宠物缓存)
            {
                Res.Log.Append($"\r\n宠物缓存{data}，时间为：{nowTime}。");
            }
            else if (eventKind == EventKind.自动存档)
            {
                Res.Log.Append($"\r\n自动存档成功，时间为：{nowTime}。");
            }
            else if (eventKind == EventKind.自动备份)
            {
                Res.Log.Append($"\r\n自动备份成功，时间为：{nowTime}。");
            }
            else if (eventKind == EventKind.作弊检测)
            {
                Res.Log.Append($"\r\n发现作弊，时间为:{nowTime}。");
            }
            else if (eventKind == EventKind.强制退出)
            {
                Res.Log.Append($"\r\n强制退出游戏，时间为：{nowTime}，原因为：{data}。");
            }
            else if (eventKind == EventKind.按键精灵)
            {
                Res.Log.Append($"\r\n按键精灵事件，时间为：{nowTime}，原因为：{data}。");
            }
            else if (eventKind == EventKind.每日礼包)
            {
                Res.Log.Append($"\r\n每日礼包领取成功，时间为：{nowTime}。");
            }
            else if (eventKind == EventKind.加入日志)
            {
                Res.Log.Append($"\r\n自定义日志：{data}。");
            }
            else if (eventKind == EventKind.领取邮件)
            {
                Res.Log.Append($"\r\n邮件日志，时间为{nowTime}：{data}。");
            }
        }

        internal static void SaveLog()
        {
            new DataProcess().SaveFile(SkRC4.DES.EncryptRC4(Res.Log.ToString(), new DataProcess().GetKey(1)), LogPath);
        }
    }
}
