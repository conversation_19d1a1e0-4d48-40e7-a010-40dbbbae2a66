能量汇聚配置说明
================

1. 配置文件位置
   - 路径: PageMain\成长突破\EnergyGatherExp.config
   - 格式: RC4加密的JSON数据

2. 配置文件结构
   - JSON格式: [{"level":0,"exp":100}, {"level":1,"exp":200}, ...]
   - level: 当前等级
   - exp: 升级到下一级所需的经验值

3. 经验规律
   - 基础公式: 100 + 等级 * 100
   - 0级升1级需要100经验
   - 1级升2级需要200经验
   - 99级升100级需要10000经验

4. 生成配置文件
   - 运行 GenerateExpConfig.GenerateEncryptedConfig() 方法
   - 自动生成加密的配置文件

5. 读取机制
   - EnergyGather类会自动加载并缓存配置
   - 如果配置文件不存在，会使用默认公式
   - 可调用 EnergyGather.ReloadConfig() 重新加载配置

6. 加密方式
   - 使用RC4加密算法
   - 密钥: DataProcess.GetKey(1)
   - 与游戏其他配置文件保持一致的加密方式

7. 修改配置
   - 修改GenerateExpConfig中的经验值
   - 重新运行生成方法
   - 调用ReloadConfig()使新配置生效 