﻿namespace Shikong.Pokemon2.PCG
{
    public class EvolutionWay
    {
        public string petID { get; set; }

        /// <summary>
        /// A路线ID
        /// </summary>
        public string AI { get; set; }
        /// <summary>
        /// B路线ID
        /// </summary>
        public string BI { get; set; }
        /// <summary>
        /// B进化宠物,格式为:道具ID|道具数量
        /// </summary>
        public string AP { get; set; }
         /// <summary>
        /// A路线道具小号,格式为:道具ID|道具数量
        /// </summary>
        public string BP { get; set; }
        public string ALV { get; set; }
        public string BLV { get; set; }

        public string AN
        {
            get
            {
                if (AI == null || AI.Equals("-1"))
                {
                    return "";
                }
                return new DataProcess().GetAppointedPetType(AI).宠物名字;
            }
            set { }
        }
        public string BN
        {
            get
            {
                if (BI == null || BI.Equals("-1"))
                {
                    return "";
                }
                return new DataProcess().GetAppointedPetType(BI).宠物名字;
            }
            set { }
        }
        public string APN
        {
            get
            {
                if (AP == null)
                {
                    return "";
                }
                return new DataProcess().GetAPType(AP).道具名字;
            }
            set { }
        }
        public string BPN
        {
            get
            {
                if (BP == null)
                {
                    return "";
                }
                return new DataProcess().GetAPType(BP).道具名字;
            }
            set { }
        }
        public string PN
        {
            get
            {
                if (petID == null)
                {
                    return "";
                }
                return new DataProcess().GetAppointedPetType(petID).宠物名字;
            }
            set { }
        }
    }
}
