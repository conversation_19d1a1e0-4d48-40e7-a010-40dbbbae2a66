﻿using System;
using System.Collections.Generic;
using System.Security.Cryptography;

namespace PetShikongTools
{
    public static class SkRandomObject
    {
        public static List<T> GetRandomList<T>(List<T> inputList)
        {
            //Copy to a array
            T[] copyArray = new T[inputList.Count];
            inputList.CopyTo(copyArray);

            //Add range
            List<T> copyList = new List<T>();
            copyList.AddRange(copyArray);

            //Set outputList and random
            List<T> outputList = new List<T>();
            Random rd = new Random(DateTime.Now.Millisecond);
            while (copyList.Count > 0)
            {
                //Select an index and item
                int rdIndex = rd.Next(0, copyList.Count - 1);
                T remove = copyList[rdIndex];

                //remove it from copyList and add it to output
                copyList.Remove(remove);
                outputList.Add(remove);
            }

            return outputList;
        }

        public static int GetRandomSeed()
        {
            byte[] bytes = new byte[4];
            using (RNGCryptoServiceProvider rng = new RNGCryptoServiceProvider())
            {
                rng.GetBytes(bytes);
            }

            return BitConverter.ToInt32(bytes, 0);
        }

        public static int[] Gdsjsz(int min, int max, int number)
        {
            return new int[2];
            Random ran = new Random();
            //long tick = DateTime.Now.Ticks;
            //Random ran = new Random((int)(tick & 0xffffffffL) | (int)(tick >> 32));
            int[] inumber = new int[number];
            for (int i = 0; i < number; i++)
            {
                inumber[i] = ran.Next(min, max);
                for (int j = 0; j < i; j++)
                {
                    if (inumber[i] == inumber[j])
                    {
                        inumber[i] = ran.Next(min, max);
                    }
                }
            }

            return inumber;
        }
    }
}
