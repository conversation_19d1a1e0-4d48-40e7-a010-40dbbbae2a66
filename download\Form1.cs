﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Net;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using static download.tools;

namespace download
{
    public partial class Form1 : Form
    {
        public Form1()
        {
            InitializeComponent();
        }

        private void button1_Click(object sender, EventArgs e)
        {
            KillProc("PetShikong.exe");
            button1.Enabled = false;
            var scanThread = new Thread(new ThreadStart(update));
            scanThread.IsBackground = true;
            scanThread.Start();
     

        }
        delegate void SetTextCallback(string text);
        private void SetText(string text)
        {
            if (label1.InvokeRequired)
            {
                SetTextCallback d = SetText;
                Invoke(d, text);
            }
            else
            {
                label1.Text=(text);
            }
        }
        delegate void BCallback(bool b);
        private void B(bool b)
        {
            if (button1.InvokeRequired)
            {
                BCallback d = B;
                Invoke(d, b);
            }
            else
            {
                button1.Enabled = b;
            }
        }
        delegate void SetPosition1Callback(int i, int j);
        private void SetPosition1(int i,int j)
        {
            if (progressBar1.InvokeRequired)
            {
                SetPosition1Callback d = SetPosition1;
                Invoke(d, i,j);
            }
            else
            {
                progressBar1.Maximum = j;
                progressBar1.Value = (i);
            }
        }
        delegate void SetPosition2Callback(int i, int j);
        private void SetPosition2(int i,int j)
        {
            if (progressBar2.InvokeRequired)
            {
                SetPosition2Callback d = SetPosition2;
                Invoke(d, i,j);
            }
            else
            {
                progressBar2.Maximum = j;
                if (i > j) i = j;
                progressBar2.Value = (i);
            }
        }
        public void update() {
            //
            SetText( "正在获取要更新的清单。");
            SetPosition1(1,100);
            String hash = readHttp(new Uri(@"http://221.229.175.10:801/hlist.dat"));
            if (hash == null)
            {
                MessageBox.Show("因网络问题无法获取到下载清单，请稍后再试。");
                return;
            }
            //String petHash = readHttp(new Uri(@"http://221.229.175.10:801/PageMain/Content/resources/styles/images/4ie/Shikong.Pokemon3.PCG.cfg"));
            //if (hash == null)
            //{
            //    MessageBox.Show("因网络问题无法获取到下载清单！请稍后再试！");
            //    return;
            //}
            SetPosition1(1,100);
            hash = SkCryptography.DES.DecryptRC4(hash, T.GetKey(1));
            //petHash = SkCryptography.DES.DecryptRC4(petHash, T.GetKey(1));

            //读取文件的本地哈希
         //   string hashtable = @"PageMain\Content\resources\styles\images\4ie\Shikong.Pokemon2.PCG.cfg";
           // string hash1 = ReadFile(hashtable, true);
            //hash1 = SkCryptography.DES.DecryptRC4(hash1, T.GetKey(1));

            //读取宠物的本地哈希
         //   hashtable = @"PageMain\Content\resources\styles\images\4ie\Shikong.Pokemon3.PCG.cfg";
       //     string hash2 = ReadFile(hashtable, true);
        //    hash2 = SkCryptography.DES.DecryptRC4(hash2, T.GetKey(1));

            string[] hashU = hash.Split(new[] { "\r\n" }, StringSplitOptions.None);
           // string[] hashD = hash1.Split(new[] { "\r\n" }, StringSplitOptions.None);
         //   string[] petHashU = petHash.Split(new[] { "\r\n" }, StringSplitOptions.None);
      //      string[] petHashD = hash2.Split(new[] { "\r\n" }, StringSplitOptions.None);
            List<String> fileList = new List<string>();
      
            SetText("正在对比清单，检查要更新的文件...");
            foreach (string hu in hashU)
            {
                string path = hu.Split('|')[0];
                //string hash_hu = hashD.FirstOrDefault(C => C.Split('|')[0] == path);
                //if (hash_hu == null) {
                //    hash_hu = "-1";
                //}
                //else
                //{
                //    hash_hu = hash_hu.Split('|')[1];
                //}
                if (hu == "") continue;
                string hash_hu = SkCryptography.GetHash.GetFileHash(Environment.CurrentDirectory + path);
                if (!hash_hu.Equals(hu.Split('|')[1]) || hash_hu.Equals("-1"))
                {
                    if (path.Contains("hlist.dat")) continue;
                    fileList.Add(path);
                    SetText("检测到" + fileList.Count + "个文件需要更新");
                }
            }
            SetPosition1(1,100);
            //foreach (string hu in petHashU)
            //{
            //    string path = hu.Split('|')[0];
            //    //string hash_hu = petHashD.FirstOrDefault(C => C.Split('|')[0] == path);
            //    //if (hash_hu == null)
            //    //{
            //    //    hash_hu = "-1";
            //    //}
            //    //else
            //    //{
            //    //    hash_hu = hash_hu.Split('|')[1];
            //    //}
            //    string hash_hu = SkCryptography.GetHash.GetFileHash(Environment.CurrentDirectory + path);
            //    if (!hash_hu.Equals(hu.Split('|')[1]) || hash_hu.Equals("-1"))
            //    {
                   
            //        fileList.Add(path);
            //        SetText("检测到" + fileList.Count + "个文件需要更新");
            //    }
            //}

            SetPosition1(1,100);
            SetText("准备开始下载文件，需要更新的文件数目：" + fileList.Count);
            int ok = 0;
            int i
                = 0;
            foreach (var fl in fileList) {
               
                i++;
                SetText("正在下载第" + i + "个文件...");
                SetPosition2(1,100);
                SetPosition1(i, fileList.Count);
                KillProc("PetShikong.exe");
                if (Download("http://221.229.175.10:801/" + fl, Environment.CurrentDirectory + fl)){
                    ok++;
                }
                SetPosition2(100,100);
            }
          
            SetPosition1(100, 100);
            SetText("成功下载数量：" + ok+"，清单中一共："+(fileList.Count));
            B(true);
        }
        public void KillProc(string strProcName)
        {
            try
            {
                //精确进程名  用GetProcessesByName
                foreach (Process p in Process.GetProcessesByName(strProcName))
                {
                    if (!p.CloseMainWindow())
                    {
                        p.Kill();
                    }
                }
            }
            catch
            {

            }
        }
        public bool Download(string url, string localfile)
        {
            bool flag = false;
            long startPosition = 0; // 上次下载的文件起始位置
            FileStream writeStream; // 写入本地文件流对象

            // 判断要下载的文件夹是否存在
            if (File.Exists(localfile))
            {
                File.Delete(localfile);

                //writeStream = File.OpenWrite(localfile);             // 存在则打开要下载的文件
                //startPosition = writeStream.Length;                  // 获取已经下载的长度
                //writeStream.Seek(startPosition, SeekOrigin.Current); // 本地文件写入位置定位
            }
            string p = localfile.Substring(0, localfile.LastIndexOf(@"\"));
            if (!Directory.Exists(p))

            {

                Directory.CreateDirectory(p);

            }
            writeStream = new FileStream(localfile, FileMode.Create);
            startPosition = 0;
            try
            {
                HttpWebRequest myRequest = (HttpWebRequest)HttpWebRequest.Create(url);// 打开网络连接      
                myRequest.Timeout = 5000;
                if (startPosition > 0)
                {
                //    myRequest.AddRange((int)startPosition);// 设置Range值,与上面的writeStream.Seek用意相同,是为了定义远程文件读取位置
                }
                var newR = myRequest.GetResponse();
                Stream readStream = newR.GetResponseStream();// 向服务器请求,获得服务器的回应数据流
                long max = newR.ContentLength;
                SetText("文件大小：" + (max / 1024.0) + "KB");

                byte[] btArray = new byte[512];// 定义一个字节数据,用来向readStream读取内容和向writeStream写入内容
                int contentSize = readStream.Read(btArray, 0, btArray.Length);// 向远程文件读第一次
                int i = 0;
                long doin = 0;
                while (contentSize > 0)// 如果读取长度大于零则继续读
                {
                    doin += 512;
                    if (doin % 1024==0) {
                      SetPosition2((int)doin,(int)max);
                    }
                    writeStream.Write(btArray, 0, contentSize);// 写入本地文件
                    contentSize = readStream.Read(btArray, 0, btArray.Length);// 继续向远程文件读取
                }

                //关闭流
                writeStream.Close();
                readStream.Close();

                flag = true;        //返回true下载成功
            }
            catch (Exception ex)
            {

                Console.WriteLine(url);
                if (url.Contains("png")) {
                    if (1==1) {

                    }
                }
                writeStream.Close();
                flag = false;       //返回false下载失败
                return Download(url, localfile);
            }

            return flag;
        }
        internal string ReadFile(string name, bool utf8)
        {
            if (!System.IO.File.Exists(name))
            {
                return null;
            }

            string text;
            using (StreamReader sr = new StreamReader(name))
            {
                text = sr.ReadToEnd();
            }

            return text;
        }

        internal string readHttp(Uri url)
        {
            try
            {

                HttpWebRequest request = (HttpWebRequest)WebRequest.Create(url);
                request.ServicePoint.Expect100Continue = false;
                request.ServicePoint.UseNagleAlgorithm = false;
                request.ServicePoint.ConnectionLimit = 65500;
                request.AllowWriteStreamBuffering = false;
                 HttpWebResponse respone = (HttpWebResponse)request.GetResponse();
                StreamReader stream = new StreamReader(respone.GetResponseStream() ?? throw new InvalidOperationException(), Encoding.GetEncoding("gb2312"));
                string jsonText = stream.ReadToEnd();
                stream.Close();
                respone.Close();

                return jsonText;
            }
            catch (Exception)
            {
                return null;
            }
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
           
        }

        private void Form1_Load(object sender, EventArgs e)
        {
            KillProc("PetShikong.exe");
        }
    }
}
