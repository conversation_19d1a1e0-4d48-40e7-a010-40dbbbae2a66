```C#
using Shikong.Pokemon2.PCG.成长突破;

/// <summary>
/// 成长突破 - 执行突破
/// </summary>
public void GrowthBreakthrough_TryBreakthrough(int phoenixStoneCount)
{
    string result = GrowthBreakthrough.TryBreakthrough(phoenixStoneCount);
    SendMessage(result);
}

/// <summary>
/// 成长突破 - 获取信息
/// </summary>
/// <returns>JSON格式的突破信息</returns>
public string GrowthBreakthrough_GetInfo()
{
    return GrowthBreakthrough.GetBreakthroughInfo();
}

/// <summary>
/// 成长突破 - 获取所有配置
/// </summary>
/// <returns>JSON格式的配置信息</returns>
public string GrowthBreakthrough_GetAllConfigs()
{
    return GrowthBreakthrough.GetAllBreakthroughConfigs();
}

/// <summary>
/// 处理成长突破相关命令
/// </summary>
private void ProcessGrowthBreakthroughCommand(string command)
{
    string result = GrowthBreakthrough.ProcessCommand(command);
    if (result.StartsWith("{") || result.StartsWith("["))
    {
        // JSON数据，不发送到聊天框
        return;
    }
    SendMessage(result);
}

// 成长突破相关命令
if (str.StartsWith("/成长突破"))
{
    ProcessGrowthBreakthroughCommand(str);
    return;
} 
```

