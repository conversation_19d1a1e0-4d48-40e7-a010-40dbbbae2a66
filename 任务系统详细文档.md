# 宠物时空游戏任务系统详细文档

## 1. 系统概述

宠物时空游戏的任务系统是一个复杂的多层次任务管理框架，支持本地任务和网络任务，具备完整的任务生命周期管理功能。

### 1.1 核心特性
- **多类型任务支持**：支持日常任务、活动任务、神宠专属任务、追龙任务等
- **任务状态管理**：完整的任务接取、进度跟踪、完成、奖励发放流程
- **网络任务集成**：支持在线任务下发和管理
- **任务助手功能**：自动化任务执行和管理
- **前置任务系统**：支持任务依赖关系
- **重复任务机制**：支持可重复执行的任务

## 2. 核心数据结构

### 2.1 TaskInfo 类 (任务信息)
```csharp
public class TaskInfo
{
    public string 任务序号 { get; set; }        // 唯一标识符
    public string 任务名 { get; set; }          // 任务显示名称
    public List<task> 任务目标 { get; set; }    // 任务目标列表
    public string 指定宠物 { get; set; }        // 指定宠物ID
    public string 任务奖励 { get; set; }        // 奖励配置字符串
    public string 允许重复 { get; set; }        // "1"=允许重复，其他=不允许
    public string 已完成 { get; set; }          // "0"=已完成，"1"=未完成
    public string 前置任务 { get; set; }        // 前置任务ID
    public string 任务介绍 { get; set; }        // 任务描述文本
    public bool? 网络任务 { get; set; }         // 是否为网络任务
}
```

### 2.2 task 类 (任务目标)
```csharp
public class task
{
    public string Type { get; set; }    // 条件类型：击杀/收集/等级/VIP等
    public string Num { get; set; }     // 目标数值
    public string ID { get; set; }      // 相关ID（怪物ID/道具ID等）
    public string inNum { get; set; }   // 当前进度值
}
```

### 2.3 TaskPanel 类 (任务面板)
```csharp
public class TaskPanel
{
    public string 任务介绍 { get; set; }    // 任务详细介绍
    public string 任务目标 { get; set; }    // 格式化的目标描述
    public string 任务进度 { get; set; }    // 格式化的进度信息
    public string 任务奖励 { get; set; }    // 格式化的奖励描述
    public string 任务名字 { get; set; }    // 任务名称
    public string 是否完成 { get; set; }    // "0"=可完成，"1"=未完成
    public bool 位置 { get; set; }          // 是否已接取
    public bool 一键完成 { get; set; }      // 是否可一键完成
    public string 循环任务 { get; set; }    // 是否为循环任务
}
```

## 3. 任务类型系统

### 3.1 任务目标类型 (task.Type)

#### 3.1.1 基础类型
- **击杀**: 击杀指定怪物数量
- **收集**: 收集指定道具数量
- **等级**: 宠物达到指定等级
- **装备**: 收集指定装备

#### 3.1.2 资源类型
- **金币**: 拥有指定数量金币
- **元宝**: 拥有指定数量元宝
- **水晶**: 拥有指定数量水晶
- **时之券**: 拥有指定数量时之券
- **威望**: 威望达到指定数值

#### 3.1.3 成长类型
- **宠物**: 指定宠物达到指定成长值
- **主宠达到成长**: 主宠达到指定成长值
- **扣除成长**: 达到成长后保留10%
- **扣除成长2**: 达到成长后保留1%
- **保留成长**: 自定义保留百分比

#### 3.1.4 特殊类型
- **VIP**: VIP等级达到指定级别
- **至尊VIP**: 激活至尊VIP特权
- **星辰VIP**: 激活星辰VIP特权
- **积分**: VIP积分达到指定数值
- **地狱**: 地狱之门达到指定层数
- **通天**: 通天塔达到指定层数
- **时间**: 时间限制任务
- **开始时间**: 任务开始时间限制
- **结束时间**: 任务结束时间限制
- **多个主宠**: 支持多个指定主宠
- **激活卡牌**: 激活指定卡牌
- **激活卡牌数量**: 激活卡牌数量达标
- **自动合宠经验**: 自动合宠经验达标
- **自动合宠次数**: 自动合宠次数达标
- **一键完成道具**: 使用道具一键完成
- **隐藏主宠提示**: 隐藏主宠信息

### 3.2 任务分类

#### 3.2.1 按重复性分类
- **一次性任务**: 允许重复 = ""或null
- **重复任务**: 允许重复 = "1"

#### 3.2.2 按来源分类
- **本地任务**: 网络任务 = false或null
- **网络任务**: 网络任务 = true

#### 3.2.3 按功能分类
- **【日常】任务**: 日常重复任务
- **【活动】任务**: 限时活动任务
- **【神宠专属】任务**: 特定宠物专属任务
- **【追龙】任务**: 追龙相关任务
- **【技能】任务**: 技能相关任务
- **【装备】任务**: 装备相关任务

## 4. 任务存储系统

### 4.1 文件结构
```
PageMain/
├── task/
│   ├── _0.task              # 任务定义配置文件（加密）
│   └── [任务ID]_.dat        # 任务介绍文件（加密）
└── Main.dat                 # 用户已接任务存档（加密）
```

### 4.2 数据加密
- **加密算法**: RC4加密
- **密钥管理**: 使用GetKey()方法获取动态密钥
- **数据格式**: JSON序列化后加密存储

### 4.3 存档结构
用户存档包含7个部分，用"O4F89"分隔：
1. 基础信息
2. 宠物信息  
3. 道具信息
4. 存档版本
5. 装备信息
6. 进度信息
7. **任务信息** (第6个索引)

## 5. 任务管理核心类

### 5.1 DataProcess 类
任务系统的核心处理类，提供完整的任务CRUD操作。

#### 5.1.1 主要方法
```csharp
// 任务获取
List<TaskInfo> GetAllTaskAim()                    // 获取所有可用任务
List<TaskInfo> GetAllTask_AT()                    // 获取所有可领取任务
List<TaskInfo> GetTasks_PHR()                     // 获取玩家已领任务
TaskInfo GetAppointedTaskAim(string 序号)         // 获取指定任务定义
TaskInfo GetAppointedTask_HR(string 序号)         // 获取指定已领任务

// 任务操作
bool AddTaskAim(TaskInfo 任务, string 任务说明)   // 添加新任务定义
bool ReceiveTask(string taskid)                  // 接取任务
bool FulfilTask(string 任务序号)                 // 完成任务
bool AbortTask(string 任务序号)                  // 放弃任务
bool RefreshTask()                               // 刷新任务状态

// 任务面板
TaskPanel GetTaskPanelInfo(string 任务序号, bool 已接受)  // 获取任务面板信息
```

#### 5.1.2 任务状态管理
- **任务定义存储**: 所有任务模板存储在_0.task文件中
- **已领任务跟踪**: 玩家已接任务存储在用户存档中
- **进度实时计算**: 根据用户当前状态动态计算任务进度
- **完成状态检查**: 自动检测任务是否满足完成条件

### 5.2 任务管理窗口类
位于Admin和NewAdmin项目中，提供任务的可视化管理界面。

#### 5.2.1 主要功能
- **任务列表显示**: 显示所有已定义的任务
- **任务搜索过滤**: 支持按任务名称和序号搜索
- **任务编辑**: 添加、修改、删除任务
- **目标管理**: 可视化编辑任务目标
- **奖励配置**: 配置任务完成奖励
- **批量导入**: 支持文本批量导入任务目标和奖励

#### 5.2.2 智能解析功能
- **目标文本解析**: 自动解析"击杀 怪物名 数量"格式
- **奖励文本解析**: 自动解析"道具名 x 数量"格式
- **序号自动生成**: 基于日期自动生成任务序号
- **前置任务关联**: 自动设置前置任务关系

## 6. 任务助手系统

### 6.1 PlayerHelper 类
提供自动化任务执行功能，位于WindowsFormsApplication7项目中。

#### 6.1.1 核心功能
```csharp
// 任务表刷新
void RefreshTaskTable(bool prestrain = false)     // 刷新可选任务列表
void RefreshTaskTable1()                          // 刷新循环任务列表

// 任务助手配置
Dictionary<string, string> TaskTable              // 任务助手任务映射
Dictionary<string, string> TaskTable1             // 循环任务映射
```

#### 6.1.2 自动任务执行
- **批量任务接取**: 最多同时接取5个任务
- **自动完成检测**: 战斗后自动检查任务完成状态
- **循环任务执行**: 支持指定次数的任务循环执行
- **智能任务筛选**: 根据VIP等级和任务类型自动筛选

### 6.2 任务助手配置
存储在用户信息的TaskHelper字段中，格式：
```
"buy|任务ID1|任务ID2|任务ID3|任务ID4|任务ID5"
```

## 7. 网络任务系统

### 7.1 在线任务获取
- **服务器地址**: `{SK_getUrl()}:9696/sk/tlinfo.ini`
- **数据格式**: RC4加密的JSON数据
- **更新机制**: 游戏启动时自动获取最新在线任务
- **本地缓存**: 存储在DataProcess.taskServerStr中

### 7.2 任务禁用机制
- **禁用列表**: 从服务器获取被禁用的任务ID列表
- **服务器地址**: `{SK_getUrl()}:9696/sk/banTask.ini`
- **存储位置**: DataProcess.BanTask数组
- **应用范围**: 影响任务接取、完成和显示

## 8. 任务奖励系统

### 8.1 奖励类型
奖励配置使用"|"分隔多个奖励，","分隔奖励参数：

#### 8.1.1 道具奖励
```
"道具,道具ID,数量"
```

#### 8.1.2 装备奖励
```
"装备,装备ID,数量"
```

#### 8.1.3 货币奖励
```
"金币,数量"
"元宝,数量"
"水晶,数量"
```

#### 8.1.4 特殊奖励
```
"默认技能"           // 刷新宠物默认技能
"卸下魂宠"           // 卸载已装备魂宠
```

### 8.2 奖励发放流程
1. **验证奖励道具**: 检查奖励道具是否存在
2. **扣除任务消耗**: 扣除任务目标要求的资源
3. **发放奖励**: 按配置发放各类奖励
4. **更新任务状态**: 标记任务为已完成
5. **处理重复任务**: 重复任务自动重新接取

## 9. 任务进度计算

### 9.1 进度检测机制
任务系统实时检测各类进度：

#### 9.1.1 击杀进度
- **数据来源**: 任务目标中的inNum字段
- **更新时机**: 击杀怪物后实时更新
- **显示格式**: "已击杀 怪物名 (当前数量/目标数量)个"

#### 9.1.2 收集进度
- **数据来源**: 用户背包中的道具数量
- **实时检测**: 每次打开任务面板时重新计算
- **特殊处理**: 区分背包道具和仓库道具

#### 9.1.3 成长进度
- **数据来源**: 主宠物当前成长值
- **指定宠物**: 检查主宠是否为指定宠物
- **动态计算**: 实时获取宠物当前成长

#### 9.1.4 其他进度
- **用户属性**: VIP等级、威望、积分等
- **系统进度**: 地狱层数、通天塔层数等
- **时间限制**: 网络时间验证

### 9.2 完成条件判断
```csharp
// 所有目标都完成 + 指定宠物匹配（如有）= 任务可完成
bool isComplete = (completedCount >= totalTargets) && petMatched;
```

## 10. 文件路径和配置

### 10.1 关键路径常量
```csharp
public const string TDC_Path = @"PageMain\task";           // 任务定义配置路径
public const string PSC_Path = @"PageMain\propTable\";     // 道具脚本配置路径
public const string EDC_Path = @"PageMain\propTable\e\";   // 装备定义配置路径
```

### 10.2 任务文件命名规则
- **任务定义**: `_0.task`
- **任务介绍**: `{任务ID}_.dat`
- **用户存档**: `Main.dat`

## 11. 错误处理和调试

### 11.1 调试功能
- **TD模式**: 测试模式，显示所有任务（包括不满足条件的）
- **本地在线任务**: 调试本地活动任务功能
- **任务助手调试**: 支持管理员权限的特殊任务

### 11.2 异常处理
- **存档损坏**: 自动检测并提示用户更换存档
- **网络异常**: 优雅降级，本地任务正常运行
- **数据不一致**: 自动清理无效的已领任务

### 11.3 日志系统
- **任务操作日志**: 记录任务接取、完成、放弃操作
- **错误日志**: 记录任务系统运行中的异常
- **性能监控**: 监控任务执行耗时

## 12. 性能优化

### 12.1 缓存机制
- **任务定义缓存**: 避免重复读取文件
- **用户数据缓存**: 减少存档读写频率
- **计算结果缓存**: 缓存复杂的进度计算结果

### 12.2 异步处理
- **网络任务获取**: 异步获取在线任务数据
- **大批量操作**: 异步处理批量任务操作
- **UI更新**: 异步更新任务列表和进度显示

## 13. 扩展性设计

### 13.1 新任务类型添加
1. 在task.Type中添加新类型标识
2. 在GetTaskPanelInfo中添加目标描述逻辑
3. 在GetTaskPanelInfo中添加进度计算逻辑
4. 在FulfilTask中添加完成处理逻辑

### 13.2 新奖励类型添加
1. 在任务奖励解析中添加新格式支持
2. 在FulfilTask中添加奖励发放逻辑
3. 在GetTaskPanelInfo中添加奖励显示逻辑

## 14. 安全性考虑

### 14.1 数据加密
- **存档加密**: 所有任务相关数据都经过RC4加密
- **密钥管理**: 动态密钥生成，防止简单破解
- **完整性校验**: 使用哈希验证存档完整性

### 14.2 作弊防护
- **服务器验证**: 关键任务通过服务器验证
- **进度检查**: 防止任务进度异常修改
- **时间验证**: 使用网络时间防止本地时间作弊

---

*本文档基于对宠物时空游戏源代码的深入分析编写，涵盖了任务系统的所有核心功能和实现细节。*
