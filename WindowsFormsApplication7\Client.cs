﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Threading;
using System.Net.Sockets;
using System.Net;

namespace Shikong.Pokemon2.PCG
{
    public class Client
    {
        private static TcpClient myTcpClient;
        private static NetworkStream mynetworkstream;//网络数据流
        private static Thread myReceiveMsgThread;//创建接收消息线程
        IPEndPoint ipEndPoint = new IPEndPoint(IPAddress.Parse("***********"), 19730);//获得IP终结点  //***********
        public static bool 连接状态 = false;
        public static bool 是否重新连接 = true;
        internal static Form1 GameForm;
        //定义回调（委托），用以完成显示接收到的数据
        private delegate void ShowReceiveMsgCallBack(string text);
        public void Client_()
        {
            return;
            try
            {
                myTcpClient = new TcpClient();;
                myTcpClient.Connect(ipEndPoint);
                if (myTcpClient != null)
                {
                    连接状态 = true;
                }
                //绑定网络数据流
                mynetworkstream = myTcpClient.GetStream();
                //创建接收信息线程
                myReceiveMsgThread = new Thread(ReceiveMsg);
                //开启线程
                myReceiveMsgThread.Start();
            }
            catch (Exception ex)
            {
                连接状态 = false;
            }

        }
        public void CloseClient()
        {
            return;
            myTcpClient.Close();
            mynetworkstream.Dispose();
            myReceiveMsgThread.Abort();//结束进程
        }
        private void ReceiveMsg()
        {
            return;
            while (true)
            {
                try
                {
                    //获取数据
                    byte[] getData = new byte[1024];          //创建一个数组来存储接收到的数据
                    mynetworkstream.Read(getData, 0, getData.Length);//将接收到的数据放到数组中
                    //转换为字符串
                    string msg = Encoding.Default.GetString(getData);
                    //将受到的信息添加到列表中
                    msg = msg.Replace("\0", "").Split('?')[0];
                    if (msg == "" || msg == null)
                    {
                        连接状态 = false;
                        //if (Fight.WorldBOSS = true)
                        //{
                        //    Fight.BOSSKILL = true;
                        //}
                        myReceiveMsgThread.Abort();
                    }
                    else 
                    {
                        UserInfo user = new DataProcess().ReadUserInfo();
                        连接状态 = true;
                        string[] str = msg.Split('|');
                        if (str.Length >= 2)
                        {
                            if (str[0].Equals("BOSSHP")) Fight.BOSSHP = str[1];
                            if (str[0].Equals("KillBOSS"))
                            {
                                Fight.KillName =str[1];
                                //Fight.GameForm.发送红色公告("世界BOSS已被玩家[" + Fight.KillName + "]击杀!");//不能跨线程调用，放弃
                            }
                        }
                    }
                }
                catch (ThreadAbortException)
                {
                    //无操作，用来响应人为抛出的异常；
                }
                catch (Exception ex)
                {
                    if (mynetworkstream != null)//有可能网络数据流还没有建立
                        mynetworkstream.Dispose();
                    break;//当抛出异常后，需要break一下，才能终结；

                }
            }
        }

        //public bool Send_Msg(string str)
        //{
        //    byte[] sendData;//定义发送数组
        //    //将数组进行字节编码，以便能够发送
        //    sendData = Encoding.Default.GetBytes(str);
        //    //发送，写入网络数据流中，括号中依次是：发送的数组，发送的开始位置，发送数据的长度
        //    if (连接状态 == true && mynetworkstream!=null)
        //    {
        //        mynetworkstream.Write(sendData, 0, sendData.Length);
        //        return true;
        //    }
        //    else return false;
        //}
    }
}
